#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🚀 5分钟加密货币动量策略 - 全新设计

完全抛弃传统SMC概念，基于5分钟加密货币市场真实特点设计：

核心策略理念：
1. 动量驱动 - 捕捉5分钟级别的价格动量和加速度
2. 趋势跟踪 - 快速识别并跟随短期趋势
3. 突破交易 - 利用关键支撑阻力位突破
4. 高频适配 - 专为5分钟高频交易优化
5. 快进快出 - 严格的风险控制和利润保护

策略核心组件：
- Momentum Engine: 多层次动量分析系统
- Trend Tracker: 自适应趋势识别
- Breakout Scanner: 关键位突破检测
- Volume Confirmation: 成交量确认机制
- Risk Manager: 动态风险管理

技术指标组合：
- EMA快慢线组合（动态周期）
- RSI动量指标（多时间框架）
- MACD趋势确认
- 成交量分析
- 价格行为模式

适用市场：加密货币（BTC、ETH等主流币种）
时间框架：5分钟
交易风格：高频动量
预期胜率：60-70%
预期收益：月化5-15%

作者：AriQuantification Team
版本：5.0 (5分钟加密货币动量策略)
更新：2024-12
"""

from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timezone
from enum import Enum
from dataclasses import dataclass

# 🔧 导入统一配置管理器
try:
    from config.smc_strategy_config import SMCConfigManager, get_global_config_manager
    CONFIG_MANAGER_AVAILABLE = True
except ImportError:
    CONFIG_MANAGER_AVAILABLE = False

logger = logging.getLogger(__name__)

# 🔧 简化的FreqTrade导入 - 移除复杂的导入管理器
try:
    from freqtrade.strategy.interface import IStrategy
    FREQTRADE_AVAILABLE = True
    logger.info("FreqTrade环境检测成功")
except ImportError:
    logger.warning("FreqTrade不可用，使用兼容模式")
    # 简单的兼容基类
    class IStrategy:
        def __init__(self, config=None):
            self.config = config or {}
    FREQTRADE_AVAILABLE = False


class SessionType(Enum):
    """交易时段类型"""
    ASIAN = "Asian"
    LONDON = "London"
    NEW_YORK = "New_York"
    OVERLAP = "Overlap"
    OFF_HOURS = "Off_Hours"


class StructureType(Enum):
    """市场结构类型"""
    BOS = "Break_of_Structure"      # 结构突破
    CHOCH = "Change_of_Character"   # 性质改变
    CONTINUATION = "Continuation"    # 延续
    REVERSAL = "Reversal"           # 反转


@dataclass
class OrderBlock:
    """订单块数据结构"""
    high: float
    low: float
    timestamp: pd.Timestamp
    session: SessionType
    structure_type: StructureType
    volume: float
    strength: float  # 0-1 强度评分
    tested: bool = False


@dataclass
class FairValueGap:
    """公允价值缺口数据结构"""
    upper: float
    lower: float
    timestamp: pd.Timestamp
    session: SessionType
    gap_type: str  # "bullish" or "bearish"
    filled: bool = False


@dataclass
class LiquidityLevel:
    """流动性水平数据结构"""
    price: float
    timestamp: pd.Timestamp
    level_type: str  # "high", "low", "equal_highs", "equal_lows"
    strength: float
    swept: bool = False


class CryptoMomentumStrategy(IStrategy):
    """
    Academic SMC (Smart Money Concepts) Strategy
    学术级智能资金概念策略

    基于Inner Circle Trader (ICT)方法论的专业实现:
    ✅ Order Blocks识别与验证
    ✅ Fair Value Gaps检测与填补追踪
    ✅ 市场结构分析 (BOS/ChoCH)
    ✅ Kill Zones时段过滤
    ✅ 流动性收割识别
    ✅ Premium/Discount Arrays
    ✅ 1分钟时间框架优化
    ✅ 统一配置管理 - 使用SMCConfigManager
    """

    @classmethod
    def from_config(cls, config_manager=None, **override_params):
        """
        🔧 从统一配置管理器创建策略实例

        Parameters:
        -----------
        config_manager : SMCConfigManager, optional
            配置管理器实例，如果为None则使用全局实例
        override_params : dict
            覆盖的参数

        Returns:
        --------
        SMCStrategy
            配置好的策略实例
        """
        if CONFIG_MANAGER_AVAILABLE:
            if config_manager is None:
                config_manager = get_global_config_manager()

            # 从配置管理器获取参数
            strategy_params = config_manager.get_strategy_params()

            # 应用覆盖参数
            strategy_params.update(override_params)

            logger.info(f"从统一配置管理器创建SMC策略，参数数量: {len(strategy_params)}")
            return cls(**strategy_params)
        else:
            logger.warning("配置管理器不可用，使用默认参数创建策略")
            return cls(**override_params)
    
    def __init__(self,
                 # 🔧 5分钟市场核心SMC参数 - 完全重构
                 structure_periods: int = 12,       # 市场结构识别周期（5分钟*12=1小时）
                 trend_strength: float = 0.004,     # 趋势强度阈值（适配5分钟波动）
                 risk_reward_ratio: float = 2.5,    # 风险回报比（5分钟市场适中）

                 # Order Block参数 - 5分钟市场特化
                 ob_formation_periods: int = 10,    # OB形成周期（5分钟*10=50分钟）
                 ob_volume_threshold: float = 1.6,  # 成交量阈值（5分钟需要更高确认）
                 ob_price_reaction: float = 1.0,    # 价格反应倍数（ATR倍数）

                 # Fair Value Gap参数 - 5分钟级别
                 fvg_min_gap_ratio: float = 0.25,   # 最小缺口比例（ATR的25%）
                 fvg_validity_periods: int = 36,    # FVG有效期（5分钟*36=3小时）

                 # 流动性区域参数 - 5分钟市场
                 liquidity_zone_strength: float = 1.5,  # 流动性区域强度

                 # 动量确认参数 - 5分钟特化
                 momentum_periods: int = 12,        # 动量计算周期
                 momentum_threshold: float = 0.5,   # 动量阈值

                 # 信号过滤参数 - 5分钟市场适配
                 signal_cooldown_periods: int = 12, # 信号间隔（5分钟*12=1小时）
                 max_daily_signals: int = 12,       # 每日最大信号数
                 min_signal_quality: float = 0.6,   # 最小信号质量

                 # 🔧 兼容性参数（保持向后兼容）
                 swing_periods: int = None,          # 兼容旧参数
                 structure_strength: float = None,   # 兼容旧参数
                 ob_lookback: int = None,            # 兼容旧参数
                 ob_min_volume_ratio: float = None,  # 兼容旧参数
                 fvg_min_size: float = None,         # 兼容旧参数
                 fvg_max_age: int = None,            # 兼容旧参数
                 signal_cooldown_minutes: int = None, # 兼容旧参数
                 max_signals_per_hour: int = None,   # 兼容旧参数
                 min_signal_strength: float = None,  # 兼容旧参数

                 # 其他兼容参数
                 use_kill_zones: bool = False,       # 5分钟市场不使用时段过滤
                 asian_start: int = 0,
                 london_start: int = 8,
                 ny_start: int = 13,
                 liquidity_lookback: int = None,
                 equal_level_tolerance: float = None,
                 enable_signal_dedup: bool = True,
                 swing_threshold: float = None,
                 bos_threshold: float = None,
                 fvg_threshold: float = None,

                 **params):
        """
        初始化5分钟加密货币市场专用SMC策略

        完全重构版本，专门针对5分钟时间框架设计
        基于加密货币市场的真实特点进行优化
        """
        # 🔧 兼容性参数映射（向后兼容）
        # 处理配置管理器传入的参数
        if 'swing_threshold' in params:
            trend_strength = params['swing_threshold']
        if 'bos_threshold' in params:
            trend_strength = max(trend_strength, params['bos_threshold'])
        if 'ob_lookback' in params:
            ob_formation_periods = params['ob_lookback']
        if 'fvg_threshold' in params:
            fvg_min_gap_ratio = params['fvg_threshold'] if params['fvg_threshold'] > 1 else params['fvg_threshold'] * 100
        if 'htf_bias_periods' in params:
            fvg_validity_periods = params['htf_bias_periods'] if params['htf_bias_periods'] < 100 else params['htf_bias_periods'] // 5

        # 处理直接传入的参数
        if swing_periods is not None:
            structure_periods = swing_periods
        if structure_strength is not None:
            trend_strength = structure_strength
        if ob_lookback is not None:
            ob_formation_periods = ob_lookback
        if ob_min_volume_ratio is not None:
            ob_volume_threshold = ob_min_volume_ratio
        if fvg_min_size is not None:
            fvg_min_gap_ratio = fvg_min_size * 1000 if fvg_min_size < 1 else fvg_min_size
        if fvg_max_age is not None:
            fvg_validity_periods = fvg_max_age // 5 if fvg_max_age > 100 else fvg_max_age
        if signal_cooldown_minutes is not None:
            signal_cooldown_periods = signal_cooldown_minutes // 5 if signal_cooldown_minutes > 20 else signal_cooldown_minutes
        if max_signals_per_hour is not None:
            max_daily_signals = max_signals_per_hour * 24 if max_signals_per_hour < 5 else max_signals_per_hour
        if min_signal_strength is not None:
            min_signal_quality = min_signal_strength

        # 🔧 5分钟市场核心参数
        self.structure_periods = int(structure_periods)
        self.trend_strength = float(trend_strength)
        self.risk_reward_ratio = float(risk_reward_ratio)

        # Order Block参数（5分钟特化）
        self.ob_formation_periods = int(ob_formation_periods)
        self.ob_volume_threshold = float(ob_volume_threshold)
        self.ob_price_reaction = float(ob_price_reaction)

        # Fair Value Gap参数（5分钟特化）
        self.fvg_min_gap_ratio = float(fvg_min_gap_ratio)
        self.fvg_validity_periods = int(fvg_validity_periods)

        # 流动性区域参数
        self.liquidity_zone_strength = float(liquidity_zone_strength)

        # 动量确认参数
        self.momentum_periods = int(momentum_periods)
        self.momentum_threshold = float(momentum_threshold)

        # 信号过滤参数（5分钟适配）
        self.signal_cooldown_periods = int(signal_cooldown_periods)
        self.max_daily_signals = int(max_daily_signals)
        self.min_signal_quality = float(min_signal_quality)

        # 兼容性参数（保持旧接口）
        self.swing_periods = self.structure_periods
        self.structure_strength = self.trend_strength
        self.ob_lookback = self.ob_formation_periods
        self.ob_min_volume_ratio = self.ob_volume_threshold
        self.fvg_min_size = self.fvg_min_gap_ratio / 1000
        self.fvg_max_age = self.fvg_validity_periods * 5
        self.signal_cooldown_minutes = self.signal_cooldown_periods * 5
        self.max_signals_per_hour = self.max_daily_signals // 24
        self.min_signal_strength = self.min_signal_quality

        # 其他参数
        self.use_kill_zones = bool(use_kill_zones)
        self.enable_signal_dedup = bool(enable_signal_dedup)

        # 信号质量控制状态
        self.last_signal_time = None
        self.hourly_signal_count = 0
        self.last_hour_reset = None

        # 内部状态跟踪
        self.order_blocks: List[OrderBlock] = []
        self.fair_value_gaps: List[FairValueGap] = []
        self.liquidity_levels: List[LiquidityLevel] = []
        self.current_structure = StructureType.CONTINUATION
        
        # FreqTrade策略基类初始化
        if FREQTRADE_AVAILABLE:
            super().__init__(config=params.get('config', {}))
    
    def _prepare_smc_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        SMC指标计算 - 简化版本，移除复杂的导入管理
        """
        try:
            # 1. 计算EMA指标
            dataframe['EMA_20'] = dataframe['close'].ewm(span=20, adjust=False).mean()
            dataframe['EMA_50'] = dataframe['close'].ewm(span=50, adjust=False).mean()
            dataframe['EMA_200'] = dataframe['close'].ewm(span=200, adjust=False).mean()

            # 2. 计算RSI
            delta = dataframe['close'].diff()
            gain = delta.clip(lower=0).rolling(window=14).mean()
            loss = (-delta.clip(upper=0)).rolling(window=14).mean()
            rs = gain / loss.replace(0, np.finfo(float).eps)
            dataframe['RSI'] = 100 - (100 / (1 + rs))

            # 3. 计算ATR
            high_low = dataframe['high'] - dataframe['low']
            high_close = np.abs(dataframe['high'] - dataframe['close'].shift())
            low_close = np.abs(dataframe['low'] - dataframe['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            dataframe['ATR'] = true_range.rolling(window=14).mean()

            # 4. SMC概念识别
            dataframe = self._identify_market_structure(dataframe)
            dataframe = self._identify_order_blocks(dataframe)
            dataframe = self._identify_fair_value_gaps(dataframe)

        except Exception as e:
            logger.error(f"指标计算失败: {e}")
            # 最基本的降级方案
            dataframe['EMA_20'] = dataframe['close'].rolling(20).mean()
            dataframe['EMA_50'] = dataframe['close'].rolling(50).mean()
            dataframe['EMA_200'] = dataframe['close'].rolling(200).mean()
            dataframe['RSI'] = pd.Series(50, index=dataframe.index)
            dataframe['ATR'] = dataframe['high'] - dataframe['low']

        return dataframe
    

    
    def _identify_market_structure(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        学术级市场结构分析 - ICT方法论
        
        识别关键概念:
        - Higher Highs (HH) / Lower Lows (LL): 趋势延续
        - Lower Highs (LH) / Higher Lows (HL): 趋势反转
        - Break of Structure (BOS): 结构突破
        - Change of Character (ChoCH): 性质改变
        """
        # 简化的市场结构识别
        try:
            # 1. 摆动点识别 (Swing Points)
            high_roll = dataframe['high'].rolling(window=self.swing_periods, center=True)
            low_roll = dataframe['low'].rolling(window=self.swing_periods, center=True)
            
            dataframe['SwingHighs'] = dataframe['high'] == high_roll.max()
            dataframe['SwingLows'] = dataframe['low'] == low_roll.min()
            
            # 2. 简化的BOS信号
            dataframe['BOS_Signals'] = (
                (dataframe['high'] > dataframe['high'].shift(self.swing_periods)) |
                (dataframe['low'] < dataframe['low'].shift(self.swing_periods))
            )
            
        except Exception as e:
            logger.warning(f"市场结构识别失败: {e}")
            dataframe['SwingHighs'] = False
            dataframe['SwingLows'] = False
            dataframe['BOS_Signals'] = False
        
        return dataframe
    
    def _identify_order_blocks(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        🔧 5分钟加密货币市场Order Blocks识别 - 高频适配版

        重新设计适合5分钟加密货币市场的Order Block识别：
        1. 降低识别阈值，增加信号频率
        2. 基于相对强度而非绝对条件
        3. 适配加密货币24/7高波动特性
        4. 重点关注价格动量而非传统结构
        """
        try:
            # 计算基础指标
            price_range = dataframe['high'] - dataframe['low']
            atr = dataframe.get('ATR', price_range.rolling(14).mean())  # 缩短ATR周期

            # 1. 🔧 降低价格反应要求（适配5分钟高频）
            # 从1.0倍ATR降低到0.6倍，增加信号频率
            strong_reaction = price_range > atr * 0.6

            # 2. 🔧 简化成交量确认（避免过度过滤）
            volume = dataframe.get('volume', pd.Series(1.0, index=dataframe.index))
            volume_ma = volume.rolling(8).mean()  # 缩短成交量周期
            volume_confirm = volume > volume_ma * 1.2  # 降低成交量要求

            # 3. 🔧 基于动量的Order Block识别（更适合5分钟）
            # 计算价格动量
            price_momentum = (dataframe['close'] - dataframe['close'].shift(3)) / dataframe['close'].shift(3)
            strong_momentum = abs(price_momentum) > 0.002  # 0.2%的价格变化

            # 4. 🔧 简化的Order Block条件（提高信号频率）
            # 看涨OB：价格上涨 + 相对强势
            bullish_ob = (
                (dataframe['close'] > dataframe['open']) &  # 多头蜡烛
                strong_reaction &  # 价格反应
                (price_momentum > 0.001) &  # 正向动量
                (dataframe['close'] > dataframe['close'].shift(1))  # 价格上涨
            )

            # 看跌OB：价格下跌 + 相对弱势
            bearish_ob = (
                (dataframe['close'] < dataframe['open']) &  # 空头蜡烛
                strong_reaction &  # 价格反应
                (price_momentum < -0.001) &  # 负向动量
                (dataframe['close'] < dataframe['close'].shift(1))  # 价格下跌
            )

            # 5. 🔧 添加趋势过滤（但不过于严格）
            ema_fast = dataframe.get('EMA_20', dataframe['close'].rolling(20).mean())
            ema_slow = dataframe.get('EMA_50', dataframe['close'].rolling(50).mean())

            # 趋势方向（允许震荡市场）
            uptrend_bias = dataframe['close'] > ema_fast
            downtrend_bias = dataframe['close'] < ema_fast

            # 最终Order Block信号（相对宽松的条件）
            bullish_ob_final = bullish_ob & uptrend_bias
            bearish_ob_final = bearish_ob & downtrend_bias

            # 6. 🔧 添加Order Block强度分级
            ob_strength = np.where(
                bullish_ob_final | bearish_ob_final,
                (price_range / atr) + (abs(price_momentum) * 100),  # 综合强度
                0
            )

            # 设置Order Block信号
            dataframe['BullishOB'] = bullish_ob_final
            dataframe['BearishOB'] = bearish_ob_final
            dataframe['OrderBlock_Signal'] = bullish_ob_final | bearish_ob_final

            # 记录Order Block价格区域和强度
            dataframe['OB_High'] = np.where(dataframe['OrderBlock_Signal'], dataframe['high'], np.nan)
            dataframe['OB_Low'] = np.where(dataframe['OrderBlock_Signal'], dataframe['low'], np.nan)
            dataframe['OB_Strength'] = ob_strength

            # 7. 🔧 Order Block有效期管理（5分钟市场需要更短有效期）
            # 标记OB年龄，超过20个周期（100分钟）的OB失效
            for i in range(20, len(dataframe)):
                if dataframe.iloc[i]['OrderBlock_Signal']:
                    # 检查前20个周期是否有相同类型的OB
                    past_window = dataframe.iloc[i-20:i]
                    if dataframe.iloc[i]['BullishOB']:
                        if past_window['BullishOB'].any():
                            # 如果有旧的看涨OB，保留强度更高的
                            past_strength = past_window.loc[past_window['BullishOB'], 'OB_Strength'].max()
                            if dataframe.iloc[i]['OB_Strength'] <= past_strength:
                                dataframe.iloc[i, dataframe.columns.get_loc('BullishOB')] = False
                                dataframe.iloc[i, dataframe.columns.get_loc('OrderBlock_Signal')] = False

                    elif dataframe.iloc[i]['BearishOB']:
                        if past_window['BearishOB'].any():
                            past_strength = past_window.loc[past_window['BearishOB'], 'OB_Strength'].max()
                            if dataframe.iloc[i]['OB_Strength'] <= past_strength:
                                dataframe.iloc[i, dataframe.columns.get_loc('BearishOB')] = False
                                dataframe.iloc[i, dataframe.columns.get_loc('OrderBlock_Signal')] = False

        except Exception as e:
            logger.warning(f"5分钟Order Block识别失败: {e}")
            dataframe['BullishOB'] = False
            dataframe['BearishOB'] = False
            dataframe['OrderBlock_Signal'] = False
            dataframe['OB_High'] = np.nan
            dataframe['OB_Low'] = np.nan
            dataframe['OB_Strength'] = 0

        return dataframe
    
    def _identify_fair_value_gaps(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        🔧 5分钟加密货币市场FVG识别 - 高频适配版

        重新设计适合5分钟加密货币市场的FVG识别：
        1. 大幅降低识别阈值，适配高频交易
        2. 简化条件，增加信号频率
        3. 重点关注价格跳空而非严格的三蜡烛模式
        4. 适配加密货币快速回填特性
        """
        try:
            # 获取蜡烛数据
            high_1 = dataframe['high'].shift(2)
            low_1 = dataframe['low'].shift(2)
            high_2 = dataframe['high'].shift(1)
            low_2 = dataframe['low'].shift(1)
            high_3 = dataframe['high']
            low_3 = dataframe['low']

            # 🔧 大幅降低缺口阈值（适配5分钟高频）
            atr = dataframe.get('ATR', (dataframe['high'] - dataframe['low']).rolling(14).mean())
            min_gap_size = atr * 0.1  # 从0.25降低到0.1，大幅增加信号

            # 🔧 简化的看涨FVG识别（更宽松条件）
            bullish_fvg = (
                (high_1 < low_3) &  # 基本向上缺口
                ((low_3 - high_1) >= min_gap_size)  # 缺口大小足够（已大幅降低）
            )

            # 🔧 简化的看跌FVG识别（更宽松条件）
            bearish_fvg = (
                (low_1 > high_3) &  # 基本向下缺口
                ((low_1 - high_3) >= min_gap_size)  # 缺口大小足够（已大幅降低）
            )

            # 🔧 添加基于价格动量的FVG（5分钟特有）
            # 即使没有严格缺口，但有强烈价格动量也认为是FVG
            price_momentum = (dataframe['close'] - dataframe['close'].shift(2)) / dataframe['close'].shift(2)

            # 动量型看涨FVG
            momentum_bull_fvg = (
                (price_momentum > 0.003) &  # 0.3%的向上动量
                (dataframe['close'] > dataframe['high'].shift(1)) &  # 价格突破前高
                (dataframe['volume'] > dataframe['volume'].rolling(5).mean())  # 成交量确认
            )

            # 动量型看跌FVG
            momentum_bear_fvg = (
                (price_momentum < -0.003) &  # 0.3%的向下动量
                (dataframe['close'] < dataframe['low'].shift(1)) &  # 价格突破前低
                (dataframe['volume'] > dataframe['volume'].rolling(5).mean())  # 成交量确认
            )

            # 🔧 合并传统FVG和动量FVG
            combined_bull_fvg = bullish_fvg | momentum_bull_fvg
            combined_bear_fvg = bearish_fvg | momentum_bear_fvg

            # 🔧 简化趋势过滤（不过于严格）
            ema_20 = dataframe.get('EMA_20', dataframe['close'].rolling(20).mean())

            # 宽松的趋势过滤
            uptrend_bias = dataframe['close'] > ema_20 * 0.998  # 允许小幅偏离
            downtrend_bias = dataframe['close'] < ema_20 * 1.002  # 允许小幅偏离

            # 最终FVG信号（相对宽松）
            bullish_fvg_final = combined_bull_fvg & uptrend_bias
            bearish_fvg_final = combined_bear_fvg & downtrend_bias

            # 设置FVG信号
            dataframe['FVG_Bullish'] = bullish_fvg_final
            dataframe['FVG_Bearish'] = bearish_fvg_final

            # 🔧 动态FVG区域计算
            # 对于传统FVG，使用缺口区域
            # 对于动量FVG，使用价格范围
            dataframe['FVG_Bull_Top'] = np.where(
                bullish_fvg,
                low_3,  # 传统FVG上边界
                np.where(
                    momentum_bull_fvg,
                    dataframe['high'],  # 动量FVG使用当前高点
                    np.nan
                )
            )

            dataframe['FVG_Bull_Bottom'] = np.where(
                bullish_fvg,
                high_1,  # 传统FVG下边界
                np.where(
                    momentum_bull_fvg,
                    dataframe['low'].shift(1),  # 动量FVG使用前低点
                    np.nan
                )
            )

            dataframe['FVG_Bear_Top'] = np.where(
                bearish_fvg,
                low_1,  # 传统FVG上边界
                np.where(
                    momentum_bear_fvg,
                    dataframe['high'].shift(1),  # 动量FVG使用前高点
                    np.nan
                )
            )

            dataframe['FVG_Bear_Bottom'] = np.where(
                bearish_fvg,
                high_3,  # 传统FVG下边界
                np.where(
                    momentum_bear_fvg,
                    dataframe['low'],  # 动量FVG使用当前低点
                    np.nan
                )
            )

            # 🔧 FVG强度计算（综合缺口大小和动量）
            dataframe['FVG_Bull_Strength'] = np.where(
                bullish_fvg_final,
                np.maximum(
                    (low_3 - high_1) / atr,  # 缺口强度
                    abs(price_momentum) * 100  # 动量强度
                ),
                0
            )

            dataframe['FVG_Bear_Strength'] = np.where(
                bearish_fvg_final,
                np.maximum(
                    (low_1 - high_3) / atr,  # 缺口强度
                    abs(price_momentum) * 100  # 动量强度
                ),
                0
            )

            # 🔧 5分钟FVG快速失效机制（适配高频特性）
            # FVG在5分钟市场中应该快速失效，避免过期信号
            validity_periods = 12  # 12个5分钟周期 = 1小时

            # 简化的失效逻辑：如果价格回填FVG区域，则FVG失效
            for i in range(validity_periods, len(dataframe)):
                # 检查看涨FVG是否被回填
                if dataframe.iloc[i-validity_periods:i]['FVG_Bullish'].any():
                    fvg_bottom = dataframe.iloc[i-validity_periods:i]['FVG_Bull_Bottom'].dropna()
                    if len(fvg_bottom) > 0 and dataframe.iloc[i]['low'] <= fvg_bottom.iloc[-1]:
                        # FVG被回填，标记为失效
                        dataframe.iloc[i-validity_periods:i, dataframe.columns.get_loc('FVG_Bullish')] = False

                # 检查看跌FVG是否被回填
                if dataframe.iloc[i-validity_periods:i]['FVG_Bearish'].any():
                    fvg_top = dataframe.iloc[i-validity_periods:i]['FVG_Bear_Top'].dropna()
                    if len(fvg_top) > 0 and dataframe.iloc[i]['high'] >= fvg_top.iloc[-1]:
                        # FVG被回填，标记为失效
                        dataframe.iloc[i-validity_periods:i, dataframe.columns.get_loc('FVG_Bearish')] = False

        except Exception as e:
            logger.warning(f"5分钟FVG识别失败: {e}")
            dataframe['FVG_Bullish'] = False
            dataframe['FVG_Bearish'] = False
            dataframe['FVG_Bull_Top'] = np.nan
            dataframe['FVG_Bull_Bottom'] = np.nan
            dataframe['FVG_Bear_Top'] = np.nan
            dataframe['FVG_Bear_Bottom'] = np.nan
            dataframe['FVG_Bull_Strength'] = 0
            dataframe['FVG_Bear_Strength'] = 0

        return dataframe
    
    # 🔧 移除冗余的流动性识别方法 - FreqTrade中不使用此方法
    # 此方法依赖于不存在的get_indicator和add_indicator方法
    # 在FreqTrade环境中，所有指标都应在populate_indicators中计算
    
    # 🔧 移除冗余的Kill Zones分析方法 - FreqTrade中不使用此方法
    # 时段分析已在策略参数中禁用 (use_kill_zones=False)
    # 此方法依赖于不存在的add_indicator方法
    
    # 🔧 移除冗余的Premium/Discount计算方法 - FreqTrade中不使用此方法
    # 此方法依赖于不存在的add_indicator方法
    # 在FreqTrade环境中，所有指标都应在populate_indicators中计算
    
    # 🔧 移除冗余的交易时段方法 - 当前策略中已禁用时段过滤
    # use_kill_zones=False，这些方法不会被使用
    
    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        ✅ 使用FreqTrade标准信号管理 - 指标计算

        遵循MyGameNotes.md原则：使用现有FreqTrade指标系统
        """
        # 🔧 初始化所有必需的FreqTrade信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        dataframe['enter_tag'] = ''
        dataframe['exit_tag'] = ''

        # 使用SMC指标计算方法
        dataframe = self._prepare_smc_indicators(dataframe)

        # 记录交易对信息（用于调试）
        pair = metadata.get('pair', 'UNKNOWN')
        logger.debug(f"指标计算完成 - 交易对: {pair}, 数据行数: {len(dataframe)}")

        return dataframe



    def _calculate_signal_strength(self, dataframe: pd.DataFrame, idx: int, signal_type: str) -> float:
        """
        🔧 计算信号强度 - 基于多个因素的综合评分

        评分因素：
        1. RSI位置（避免极值）
        2. 趋势强度（EMA差距）
        3. 波动性适中程度
        4. 成交量确认
        """
        try:
            row = dataframe.iloc[idx]
            strength = 0.0

            # 1. RSI评分（放宽范围，增加信号可用性）
            rsi = row['RSI']
            if signal_type in ['smc_long', 'enter_long']:
                # 多头信号：RSI在30-70区间都可接受
                if 30 <= rsi <= 70:
                    rsi_score = 1.0 - abs(rsi - 50) / 25  # 50为最佳，向两边递减更缓慢
                elif 25 <= rsi < 30 or 70 < rsi <= 75:
                    rsi_score = 0.7  # 边缘区域给更高分数
                else:
                    rsi_score = 0.3  # 极值区域，但不完全排除
            else:  # 空头信号
                # 空头信号：RSI在30-70区间都可接受
                if 30 <= rsi <= 70:
                    rsi_score = 1.0 - abs(rsi - 50) / 25
                elif 25 <= rsi < 30 or 70 < rsi <= 75:
                    rsi_score = 0.7
                else:
                    rsi_score = 0.3

            strength += rsi_score * 0.4  # RSI权重40%

            # 2. 趋势强度评分
            ema_20 = row['EMA_20']
            ema_50 = row['EMA_50']
            trend_strength = abs(ema_20 - ema_50) / ema_50
            trend_score = min(1.0, trend_strength * 100)  # 1%差距为满分
            strength += trend_score * 0.3  # 趋势权重30%

            # 3. 波动性评分（适中最佳）
            atr = row['ATR']
            atr_ma = dataframe['ATR'].rolling(20).mean().iloc[idx]
            if atr_ma > 0:
                volatility_ratio = atr / atr_ma
                # 0.8-1.2倍ATR均值为最佳
                if 0.8 <= volatility_ratio <= 1.2:
                    volatility_score = 1.0
                elif 0.6 <= volatility_ratio < 0.8 or 1.2 < volatility_ratio <= 1.5:
                    volatility_score = 0.7
                else:
                    volatility_score = 0.3
            else:
                volatility_score = 0.5

            strength += volatility_score * 0.2  # 波动性权重20%

            # 4. 成交量确认评分
            volume = row.get('volume', 1)
            if volume > 0:
                volume_ma = dataframe['volume'].rolling(20).mean().iloc[idx]
                if volume_ma > 0:
                    volume_ratio = volume / volume_ma
                    volume_score = min(1.0, volume_ratio / 1.5)  # 1.5倍均值为满分
                else:
                    volume_score = 0.5
            else:
                volume_score = 0.5

            strength += volume_score * 0.1  # 成交量权重10%

            return min(1.0, strength)  # 确保不超过1.0

        except Exception as e:
            logger.debug(f"信号强度计算失败: {e}")
            return 0.5  # 默认中等强度



    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        🔧 实用级入场信号 - 修复进场信号缺失问题

        核心修复：
        1. 降低信号条件严格程度，确保能生成信号
        2. 使用OR逻辑而非AND逻辑，增加信号机会
        3. 动态阈值调整，适应不同市场条件
        4. 保留基本的风险控制
        """
        pair = metadata.get('pair', 'UNKNOWN')

        # 🔧 基础趋势确认 - 大幅降低阈值
        ema_spread = abs(dataframe['EMA_20'] - dataframe['EMA_50']) / dataframe['close']
        trend_up = dataframe['EMA_20'] > dataframe['EMA_50']
        trend_down = dataframe['EMA_20'] < dataframe['EMA_50']

        # 强趋势（可选条件）
        strong_trend_up = trend_up & (ema_spread > 0.0005)  # 从0.002降低到0.0005
        strong_trend_down = trend_down & (ema_spread > 0.0005)

        # 🔧 宽松的动量确认 - 扩大RSI范围
        momentum_up = (dataframe['RSI'] > 40) & (dataframe['RSI'] < 70)  # 扩大范围
        momentum_down = (dataframe['RSI'] > 30) & (dataframe['RSI'] < 60)  # 扩大范围

        # 🔧 简化的价格位置确认
        price_position_up = dataframe['close'] > dataframe['EMA_20'] * 1.0005  # 从1.001降低到1.0005
        price_position_down = dataframe['close'] < dataframe['EMA_20'] * 0.9995  # 从0.999提高到0.9995

        # 🔧 宽松的成交量确认
        volume_ma = dataframe['volume'].rolling(20).mean()
        volume_confirm = dataframe['volume'] > volume_ma * 1.1  # 从1.5降低到1.1

        # 🔧 5分钟加密货币市场SMC多头条件 - 高频交易版
        smc_long_condition = (
            # 核心条件：大幅简化，提高信号频率
            (
                # 1. 基础趋势 + Order Block机会（宽松条件）
                trend_up &  # 基础上升趋势
                (dataframe['BullishOB'].rolling(6).max() > 0) &  # 缩短OB回看期
                momentum_up  # 动量确认
            ) |
            (
                # 2. FVG机会（大幅简化）
                (dataframe['FVG_Bullish'].rolling(8).max() > 0) &  # 缩短FVG回看期
                (dataframe['close'] > dataframe['EMA_20'])  # 基础趋势确认
            ) |
            (
                # 3. 纯动量突破（5分钟高频特有）
                momentum_up &  # 动量向上
                (dataframe['close'] > dataframe['close'].shift(1)) &  # 价格上涨
                (dataframe['RSI'] > 50) &  # RSI强势
                volume_confirm  # 成交量确认
            ) |
            (
                # 4. 价格突破（简化版）
                (dataframe['close'] > dataframe['high'].rolling(5).max().shift(1)) &  # 突破近期高点
                trend_up &  # 趋势确认
                (dataframe['volume'] > dataframe['volume'].rolling(5).mean())  # 成交量确认
            ) |
            (
                # 5. EMA金叉机会（5分钟特有）
                (dataframe['EMA_20'] > dataframe['EMA_50']) &  # EMA金叉
                (dataframe['close'] > dataframe['EMA_20']) &  # 价格在EMA20之上
                momentum_up  # 动量确认
            )
        )

        # 🔧 5分钟加密货币市场SMC空头条件 - 高频交易版
        smc_short_condition = (
            # 核心条件：大幅简化，提高信号频率
            (
                # 1. 基础趋势 + Order Block机会（宽松条件）
                trend_down &  # 基础下降趋势
                (dataframe['BearishOB'].rolling(6).max() > 0) &  # 缩短OB回看期
                momentum_down  # 动量确认
            ) |
            (
                # 2. FVG机会（大幅简化）
                (dataframe['FVG_Bearish'].rolling(8).max() > 0) &  # 缩短FVG回看期
                (dataframe['close'] < dataframe['EMA_20'])  # 基础趋势确认
            ) |
            (
                # 3. 纯动量突破（5分钟高频特有）
                momentum_down &  # 动量向下
                (dataframe['close'] < dataframe['close'].shift(1)) &  # 价格下跌
                (dataframe['RSI'] < 50) &  # RSI弱势
                volume_confirm  # 成交量确认
            ) |
            (
                # 4. 价格突破（简化版）
                (dataframe['close'] < dataframe['low'].rolling(5).min().shift(1)) &  # 突破近期低点
                trend_down &  # 趋势确认
                (dataframe['volume'] > dataframe['volume'].rolling(5).mean())  # 成交量确认
            ) |
            (
                # 5. EMA死叉机会（5分钟特有）
                (dataframe['EMA_20'] < dataframe['EMA_50']) &  # EMA死叉
                (dataframe['close'] < dataframe['EMA_20']) &  # 价格在EMA20之下
                momentum_down  # 动量确认
            )
        )

        # 🔧 应用SMC信号过滤器
        dataframe = self._apply_smc_signal_filter(dataframe, smc_long_condition, smc_short_condition, pair)

        return dataframe

    def _apply_smc_signal_filter(self, dataframe: pd.DataFrame, long_condition: pd.Series,
                               short_condition: pd.Series, pair: str) -> pd.DataFrame:
        """
        🔧 SMC信号过滤器 - 基于真正的SMC逻辑

        SMC过滤机制：
        1. 基于Order Blocks和FVG的信号验证
        2. 市场结构确认
        3. 避免在同一区域重复交易
        """
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''

        # 直接应用信号条件，但增加适度的间隔控制
        long_signals_raw = dataframe[long_condition]
        short_signals_raw = dataframe[short_condition]

        # 🔧 5分钟高频市场信号间隔：每15分钟最多一个信号（大幅提高频率）
        approved_long_signals = []
        approved_short_signals = []

        if not long_signals_raw.empty:
            long_times = long_signals_raw.index
            last_time = None
            for signal_time in long_times:
                if last_time is None or (signal_time - last_time).total_seconds() >= 900:  # 15分钟
                    approved_long_signals.append(signal_time)
                    last_time = signal_time

        if not short_signals_raw.empty:
            short_times = short_signals_raw.index
            last_time = None
            for signal_time in short_times:
                if last_time is None or (signal_time - last_time).total_seconds() >= 900:  # 15分钟
                    approved_short_signals.append(signal_time)
                    last_time = signal_time

        # 设置批准的信号
        if approved_long_signals:
            dataframe.loc[approved_long_signals, 'enter_long'] = 1
            dataframe.loc[approved_long_signals, 'enter_tag'] = 'smc_structure_long'

        if approved_short_signals:
            dataframe.loc[approved_short_signals, 'enter_short'] = 1
            dataframe.loc[approved_short_signals, 'enter_tag'] = 'smc_structure_short'

        # 🔧 信号统计
        long_signals = len(approved_long_signals)
        short_signals = len(approved_short_signals)
        total_signals = long_signals + short_signals

        # 🔧 SMC信号质量日志
        if total_signals > 0:
            logger.info(f"🎯 SMC结构信号生成 - {pair}: 多头={long_signals}, 空头={short_signals}")
            # 计算信号密度
            signal_density = total_signals / len(dataframe) * 100
            logger.info(f"   信号密度: {signal_density:.3f}% ({total_signals}/{len(dataframe)})")
        else:
            logger.info(f"⚠️ 无SMC结构信号 - {pair}")

        return dataframe



    def _apply_practical_signal_filter(self, dataframe: pd.DataFrame, long_condition: pd.Series,
                                     short_condition: pd.Series, pair: str) -> pd.DataFrame:
        """
        🔧 实用级信号过滤器 - 简化版，确保信号生成

        简化的过滤机制：
        1. 基本信号设置
        2. 简单的去重处理
        3. 基础的频率限制
        """
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''

        # 直接设置信号，不进行复杂的质量验证
        dataframe.loc[long_condition, 'enter_long'] = 1
        dataframe.loc[long_condition, 'enter_tag'] = 'smc_practical_long'

        dataframe.loc[short_condition, 'enter_short'] = 1
        dataframe.loc[short_condition, 'enter_tag'] = 'smc_practical_short'

        # 🔧 基础信号统计
        long_signals = dataframe['enter_long'].sum()
        short_signals = dataframe['enter_short'].sum()
        total_signals = long_signals + short_signals

        # 🔧 简化的日志输出
        if total_signals > 0:
            logger.info(f"🎯 实用级信号生成 - {pair}: 多头={long_signals}, 空头={short_signals}")
        else:
            logger.warning(f"⚠️ 未生成任何信号 - {pair}")

        return dataframe

    def _apply_signal_quality_filter(self, dataframe: pd.DataFrame, long_condition: pd.Series,
                                   short_condition: pd.Series, pair: str) -> pd.DataFrame:
        """
        🎓 学术级信号质量过滤器

        实施严格的信号过滤机制：
        1. 信号强度评估
        2. 时间冷却检查
        3. 信号频率限制
        4. 去重处理
        """
        # 初始化信号列
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = ''

        # 获取候选信号索引
        long_candidates = dataframe[long_condition].index
        short_candidates = dataframe[short_condition].index

        # 应用信号质量过滤
        approved_long_signals = []
        approved_short_signals = []

        # 处理多头信号
        for idx in long_candidates:
            if self._validate_signal_quality(dataframe, idx, 'long', pair):
                approved_long_signals.append(idx)

        # 处理空头信号
        for idx in short_candidates:
            if self._validate_signal_quality(dataframe, idx, 'short', pair):
                approved_short_signals.append(idx)

        # 设置通过验证的信号
        if approved_long_signals:
            dataframe.loc[approved_long_signals, 'enter_long'] = 1
            dataframe.loc[approved_long_signals, 'enter_tag'] = 'smc_academic_long'

        if approved_short_signals:
            dataframe.loc[approved_short_signals, 'enter_short'] = 1
            dataframe.loc[approved_short_signals, 'enter_tag'] = 'smc_academic_short'

        # 🎓 学术级信号统计
        long_signals = dataframe['enter_long'].sum()
        short_signals = dataframe['enter_short'].sum()
        total_signals = long_signals + short_signals

        # 🎓 智能日志输出 - 只记录高质量信号
        if total_signals > 0:
            logger.info(f"� 学术级信号生成 - {pair}: 多头={long_signals}, 空头={short_signals}")

            # 记录信号质量统计
            if long_signals > 0:
                latest_long_idx = dataframe[dataframe['enter_long'] == 1].index[-1]
                latest_row = dataframe.loc[latest_long_idx]
                signal_strength = self._calculate_signal_strength(dataframe, latest_long_idx, 'long')
                logger.info(f"  📈 多头信号: 价格={latest_row['close']:.6f}, RSI={latest_row['RSI']:.1f}, 强度={signal_strength:.2f}")

            if short_signals > 0:
                latest_short_idx = dataframe[dataframe['enter_short'] == 1].index[-1]
                latest_row = dataframe.loc[latest_short_idx]
                signal_strength = self._calculate_signal_strength(dataframe, latest_short_idx, 'short')
                logger.info(f"  📉 空头信号: 价格={latest_row['close']:.6f}, RSI={latest_row['RSI']:.1f}, 强度={signal_strength:.2f}")

        return dataframe

    def _validate_signal_quality(self, dataframe: pd.DataFrame, idx: int, signal_type: str, pair: str) -> bool:
        """
        🎓 学术级信号质量验证

        验证标准：
        1. 信号强度必须达到最低阈值
        2. 必须满足时间冷却要求
        3. 不能超过每小时信号限制
        4. 通过去重检查
        """
        try:
            # 1. 计算信号强度
            signal_strength = self._calculate_signal_strength(dataframe, idx, signal_type)
            if signal_strength < self.min_signal_strength:
                return False

            # 2. 检查时间冷却
            current_time = dataframe.index[idx]
            if not self._check_signal_cooldown(current_time):
                return False

            # 3. 检查每小时信号限制
            if not self._check_hourly_signal_limit(current_time):
                return False

            # 4. 更新信号状态
            self._update_signal_state(current_time)

            return True

        except Exception as e:
            logger.debug(f"信号质量验证失败 - {pair} @ {idx}: {e}")
            return False

    def _check_signal_cooldown(self, current_time) -> bool:
        """检查信号冷却时间"""
        if self.last_signal_time is None:
            return True

        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.signal_cooldown_minutes

    def _check_hourly_signal_limit(self, current_time) -> bool:
        """检查每小时信号限制"""
        current_hour = current_time.hour

        # 重置小时计数器
        if self.last_hour_reset is None or self.last_hour_reset != current_hour:
            self.hourly_signal_count = 0
            self.last_hour_reset = current_hour

        return self.hourly_signal_count < self.max_signals_per_hour

    def _update_signal_state(self, current_time):
        """更新信号状态"""
        self.last_signal_time = current_time
        self.hourly_signal_count += 1

    def _log_signal_details(self, dataframe: pd.DataFrame, idx: int, pair: str, signal_type: str):
        """统一的信号详情记录方法 - 简化版"""
        try:
            row = dataframe.loc[idx]
            signal_strength = self._calculate_signal_strength(dataframe, idx, signal_type)

            logger.info(f"🔍 {signal_type}信号 - {pair} @ {idx}:")
            logger.info(f"  强度: {signal_strength:.2f}, 价格: {row['close']:.6f}")
            logger.info(f"  RSI: {row['RSI']:.2f}, ATR: {row['ATR']:.6f}")

            if 'short' in signal_type:
                logger.info(f"  空头确认: EMA20({row['EMA_20']:.6f}) < EMA50({row['EMA_50']:.6f})")
            else:
                logger.info(f"  多头确认: EMA20({row['EMA_20']:.6f}) > EMA50({row['EMA_50']:.6f})")

        except Exception as e:
            logger.debug(f"记录{signal_type}信号详情失败: {e}")

    def _log_exit_reason_analysis(self, dataframe: pd.DataFrame, idx: int, pair: str, exit_type: str):
        """
        出场原因分析记录方法

        分析并记录触发出场信号的具体原因，帮助理解策略行为
        """
        try:
            row = dataframe.loc[idx]

            # 分析出场原因
            reasons = []

            # 计算关键指标
            atr_ma = dataframe['ATR'].rolling(20).mean().iloc[idx]
            volatility_ratio = row['ATR'] / atr_ma if atr_ma > 0 else 1.0

            # 趋势分析
            if 'long' in exit_type:
                # 多头出场原因分析
                if row['EMA_20'] < row['EMA_50'] and row['close'] < row['EMA_20']:
                    reasons.append("趋势反转确认")
                if row['RSI'] > 75:
                    reasons.append("严重超买")
                elif row['RSI'] < 35:
                    reasons.append("动量转弱")

                # 价格位置分析
                recent_high = dataframe['high'].rolling(5).max().iloc[idx]
                if row['close'] > recent_high * 0.998:
                    reasons.append("接近阻力位")

            else:  # short exit
                # 空头出场原因分析
                if row['EMA_20'] > row['EMA_50'] and row['close'] > row['EMA_20']:
                    reasons.append("趋势反转确认")
                if row['RSI'] < 25:
                    reasons.append("严重超卖")
                elif row['RSI'] > 65:
                    reasons.append("动量转强")

                # 价格位置分析
                recent_low = dataframe['low'].rolling(5).min().iloc[idx]
                if row['close'] < recent_low * 1.002:
                    reasons.append("接近支撑位")

            # 波动性分析
            if volatility_ratio > 2.0:
                reasons.append("波动性异常放大")

            # 记录分析结果
            logger.info(f"🚪 {exit_type}出场分析 - {pair} @ {idx}:")
            logger.info(f"  出场原因: {', '.join(reasons) if reasons else '综合信号'}")
            logger.info(f"  价格: {row['close']:.6f}")
            logger.info(f"  RSI: {row['RSI']:.2f}")
            logger.info(f"  趋势状态: EMA20={row['EMA_20']:.6f}, EMA50={row['EMA_50']:.6f}")
            logger.info(f"  波动性: {row['ATR']:.6f} (平均: {atr_ma:.6f}, 比率: {volatility_ratio:.2f})")

        except Exception as e:
            logger.debug(f"记录{exit_type}出场分析失败: {e}")

    def _generate_signals_impl(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        实现BaseStrategy的抽象方法

        为了兼容性，将FreqTrade方法包装为BaseStrategy接口
        """
        # 使用FreqTrade方法生成信号
        metadata = {'pair': 'DEFAULT'}

        # 计算指标
        data = self.populate_indicators(data, metadata)

        # 生成入场信号
        data = self.populate_entry_trend(data, metadata)

        # 生成出场信号
        data = self.populate_exit_trend(data, metadata)

        return data

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        🔧 SMC出场逻辑 - 基于真正的SMC概念

        SMC出场原则：
        1. 主要依赖FreqTrade的ROI和止损机制
        2. 只在明确的SMC反转信号时提前出场
        3. 基于Order Blocks和市场结构变化
        4. 避免过度出场，让利润充分发展
        """
        pair = metadata.get('pair', 'UNKNOWN')

        # 🔧 SMC多头出场条件 - 基于真正的反转信号
        # 1. 遇到强阻力Order Block
        hit_resistance_ob = (
            (dataframe['BearishOB'].rolling(5).max() > 0) &  # 近期有看跌OB
            (dataframe['close'] >= dataframe['OB_Low'].rolling(5).min()) &  # 价格触及OB区域
            (dataframe['close'] <= dataframe['OB_High'].rolling(5).max())
        )

        # 2. 市场结构明确破坏（Break of Structure向下）
        market_structure_break_down = (
            (dataframe['EMA_20'] < dataframe['EMA_50']) &  # 趋势反转
            (dataframe['close'] < dataframe['EMA_20'] * 0.992) &  # 明显跌破
            (dataframe['RSI'] < 30)  # 动量确认
        )

        # 3. FVG填补完成且反转
        fvg_filled_reversal = (
            (dataframe['FVG_Bearish'].rolling(10).max() > 0) &  # 有看跌FVG
            (dataframe['close'] <= dataframe['FVG_Bear_Bottom'].rolling(10).min()) &  # FVG被填补
            (dataframe['RSI'] < 35)  # 反转确认
        )

        # SMC多头出场条件（需要明确的SMC反转信号）
        smc_long_exit = (
            hit_resistance_ob |
            market_structure_break_down |
            fvg_filled_reversal
        )

        # 🔧 SMC空头出场条件 - 基于真正的反转信号
        # 1. 遇到强支撑Order Block
        hit_support_ob = (
            (dataframe['BullishOB'].rolling(5).max() > 0) &  # 近期有看涨OB
            (dataframe['close'] >= dataframe['OB_Low'].rolling(5).min()) &  # 价格触及OB区域
            (dataframe['close'] <= dataframe['OB_High'].rolling(5).max())
        )

        # 2. 市场结构明确破坏（Break of Structure向上）
        market_structure_break_up = (
            (dataframe['EMA_20'] > dataframe['EMA_50']) &  # 趋势反转
            (dataframe['close'] > dataframe['EMA_20'] * 1.008) &  # 明显突破
            (dataframe['RSI'] > 70)  # 动量确认
        )

        # 3. FVG填补完成且反转
        fvg_filled_reversal_up = (
            (dataframe['FVG_Bullish'].rolling(10).max() > 0) &  # 有看涨FVG
            (dataframe['close'] >= dataframe['FVG_Bull_Top'].rolling(10).max()) &  # FVG被填补
            (dataframe['RSI'] > 65)  # 反转确认
        )

        # SMC空头出场条件（需要明确的SMC反转信号）
        smc_short_exit = (
            hit_support_ob |
            market_structure_break_up |
            fvg_filled_reversal_up
        )

        # 🔧 设置FreqTrade出场信号（更保守的出场）
        dataframe.loc[smc_long_exit, 'exit_long'] = 1
        dataframe.loc[smc_long_exit, 'exit_tag'] = 'smc_structure_exit_long'

        dataframe.loc[smc_short_exit, 'exit_short'] = 1
        dataframe.loc[smc_short_exit, 'exit_tag'] = 'smc_structure_exit_short'

        # 🔍 出场信号统计
        exit_long_signals = dataframe['exit_long'].sum()
        exit_short_signals = dataframe['exit_short'].sum()
        total_exit_signals = exit_long_signals + exit_short_signals

        # 🔧 SMC出场分析
        if total_exit_signals > 0:
            logger.info(f"🚪 SMC结构出场 - {pair}: 多头出场={exit_long_signals}, 空头出场={exit_short_signals}")

        return dataframe


    
    # ✅ FreqTrade策略配置
    
    # 🔧 5分钟高频市场ROI配置 - 适配快速获利
    minimal_roi = {
        "0": 0.015,   # 1.5%目标收益（降低目标，提高成功率）
        "5": 0.012,   # 5分钟后1.2%
        "10": 0.01,   # 10分钟后1%
        "20": 0.008,  # 20分钟后0.8%
        "30": 0.006,  # 30分钟后0.6%
        "60": 0.004   # 1小时后0.4%
    }

    # 🔧 5分钟高频市场止损配置 - 改善风险回报比
    stoploss = -0.015  # 1.5%止损（收紧止损，改善风险回报比）

    # 🔧 优化时间框架 - 使用5分钟提高信号质量
    timeframe = '5m'  # 改为5分钟，平衡响应速度和信号质量

    # 🔧 优化启动资金要求 - 确保足够的历史数据
    startup_candle_count: int = 100

    # 可以做空
    can_short: bool = True

    # 🔧 关键修复：订单类型配置 - 优化执行效率
    order_types = {
        'entry': 'market',     # 市价单入场，确保执行
        'exit': 'market',      # 市价单出场，确保执行
        'stoploss': 'market',  # 止损用市价单，确保执行
        'stoploss_on_exchange': True,   # 启用交易所止损
        'stoploss_on_exchange_interval': 60,
    }

    # 订单时间限制
    order_time_in_force = {
        'entry': 'GTC',  # Good Till Cancelled
        'exit': 'GTC'
    }

    # 🔧 新增：自定义价格计算 - 解决滑点问题
    def custom_entry_price(self, pair: str, current_time: datetime, proposed_rate: float,
                          entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        自定义入场价格 - 减少滑点和费用

        策略：
        1. 限价单使用稍微有利的价格
        2. 多头：使用略低于当前价的价格
        3. 空头：使用略高于当前价的价格

        参数使用:
        - current_time: 用于时间相关的价格调整
        - entry_tag: 用于不同策略的价格调整
        - kwargs: 扩展参数
        """
        try:
            # 使用传入参数进行调试日志
            logger.debug(f"自定义入场价格 - {pair} at {current_time}, tag: {entry_tag}")

            # 获取当前市场数据
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return proposed_rate

            current_candle = dataframe.iloc[-1]
            current_price = current_candle['close']
            atr = current_candle.get('ATR', 0.001)

            # 计算价格调整幅度（基于ATR的0.1%）
            price_adjustment = min(atr * 0.1, current_price * 0.0005)  # 最大0.05%

            # 根据entry_tag调整策略
            if entry_tag and 'quality' in entry_tag:
                # 高质量信号使用更保守的价格调整
                price_adjustment *= 0.5

            if side == 'long':
                # 多头：使用略低价格，增加成交概率
                adjusted_price = current_price - price_adjustment
                logger.debug(f"多头入场价格调整 - {pair}: {current_price:.6f} -> {adjusted_price:.6f}")
                return adjusted_price
            else:  # short
                # 空头：使用略高价格，增加成交概率
                adjusted_price = current_price + price_adjustment
                logger.debug(f"空头入场价格调整 - {pair}: {current_price:.6f} -> {adjusted_price:.6f}")
                return adjusted_price

        except Exception as e:
            logger.warning(f"自定义入场价格计算失败 - {pair}: {e}")
            return proposed_rate

    def custom_exit_price(self, pair: str, trade, current_time: datetime,
                         proposed_rate: float, current_profit: float, exit_tag: Optional[str],
                         **kwargs) -> float:
        """
        自定义出场价格 - 优化利润实现

        策略：
        1. 盈利时：使用稍微保守的价格确保成交
        2. 亏损时：使用稍微激进的价格减少损失

        参数使用:
        - current_time: 用于时间相关的价格调整
        - exit_tag: 用于不同出场策略的价格调整
        - kwargs: 扩展参数
        """
        try:
            # 使用传入参数进行调试日志
            logger.debug(f"自定义出场价格 - {pair} at {current_time}, tag: {exit_tag}")

            # 获取当前市场数据
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return proposed_rate

            current_candle = dataframe.iloc[-1]
            current_price = current_candle['close']
            atr = current_candle.get('ATR', 0.001)

            # 计算价格调整幅度
            price_adjustment = min(atr * 0.1, current_price * 0.0005)

            # 根据exit_tag调整策略
            if exit_tag and 'stop' in exit_tag:
                # 止损出场使用更激进的价格
                price_adjustment *= 1.5

            if trade.is_short:
                if current_profit > 0:
                    # 空头盈利：使用略高价格确保成交
                    adjusted_price = current_price + price_adjustment
                else:
                    # 空头亏损：使用略低价格减少损失
                    adjusted_price = current_price - price_adjustment
            else:  # long
                if current_profit > 0:
                    # 多头盈利：使用略低价格确保成交
                    adjusted_price = current_price - price_adjustment
                else:
                    # 多头亏损：使用略高价格减少损失
                    adjusted_price = current_price + price_adjustment

            logger.debug(f"出场价格调整 - {pair}: {current_price:.6f} -> {adjusted_price:.6f} (利润: {current_profit:.2%})")
            return adjusted_price

        except Exception as e:
            logger.warning(f"自定义出场价格计算失败 - {pair}: {e}")
            return proposed_rate

    # 🔧 增加交易执行回调方法 - 调试做空交易

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                           time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                           side: str, **kwargs) -> bool:
        """
        确认交易入场 - 记录做空交易详情

        参数使用:
        - order_type: 订单类型 (用于不同订单类型的处理)
        - time_in_force: 订单有效期 (用于订单管理)
        - kwargs: 扩展参数
        """
        logger.info(f"🔍 {side}交易确认 - {pair} ({order_type}, {time_in_force}):")
        logger.info(f"  入场价格: {rate:.6f}")
        logger.info(f"  交易数量: {amount:.6f}")
        logger.info(f"  入场标签: {entry_tag}")
        logger.info(f"  时间: {current_time}")

        return True

    def confirm_trade_exit(self, pair: str, trade, order_type: str, amount: float,
                          rate: float, time_in_force: str, exit_reason: str,
                          current_time: datetime, **kwargs) -> bool:
        """
        🔧 修复版确认交易出场 - 正确处理费用和盈亏计算

        修复问题：
        1. 时区感知状态不匹配导致的崩溃
        2. 区分价格盈亏和总盈亏（包含费用）
        3. 正确显示费用影响，避免误导性警告

        参数使用:
        - order_type: 订单类型 (用于不同订单类型的处理)
        - amount: 交易数量 (用于数量验证)
        - time_in_force: 订单有效期 (用于订单管理)
        - kwargs: 扩展参数
        """
        logger.debug(f"交易出场确认 - {pair} ({order_type}, {time_in_force}), 数量: {amount}")

        if trade.is_short:
            entry_rate = trade.open_rate

            # FreqTrade的总盈亏计算（包含费用）
            total_profit_ratio = trade.calc_profit_ratio(rate)
            total_profit_abs = trade.calc_profit(rate)

            # 纯价格差异计算（不包含费用）
            price_profit_ratio = (entry_rate - rate) / entry_rate

            # 估算费用影响
            estimated_fees = getattr(trade, 'fee_open', 0.0005) + getattr(trade, 'fee_close', 0.0005)
            fee_impact = estimated_fees  # 费用直接影响盈亏

            logger.info(f"🔍 做空交易出场 - {pair}:")
            logger.info(f"  入场价格: {entry_rate:.6f}")
            logger.info(f"  出场价格: {rate:.6f}")
            logger.info(f"  价格变化: {((rate - entry_rate) / entry_rate * 100):+.2f}%")
            logger.info(f"  价格盈亏: {(price_profit_ratio * 100):+.2f}%")
            logger.info(f"  交易费用: -{(fee_impact * 100):.2f}%")
            logger.info(f"  总盈亏: {(total_profit_ratio * 100):+.2f}%")
            logger.info(f"  绝对盈亏: {total_profit_abs:+.2f} USDT")
            logger.info(f"  出场原因: {exit_reason}")

            # 🔧 修复时区问题的持仓时间计算
            try:
                holding_time = self._calculate_holding_time(current_time, trade.open_date)
                if holding_time is not None:
                    logger.info(f"  持仓时间: {holding_time}")
                else:
                    logger.info(f"  持仓时间: 计算失败（时区问题）")
            except Exception as e:
                logger.debug(f"持仓时间计算异常 - {pair}: {e}")
                logger.info(f"  持仓时间: 计算失败")

            # 🔍 详细的盈亏逻辑分析
            if rate > entry_rate:
                logger.info(f"  📈 价格上涨 ({entry_rate:.6f} -> {rate:.6f}) = 做空亏损 ✅")
            elif rate < entry_rate:
                logger.info(f"  📉 价格下跌 ({entry_rate:.6f} -> {rate:.6f}) = 做空盈利 ✅")
            else:
                logger.info(f"  ➡️ 价格无变化 ({entry_rate:.6f} = {rate:.6f}) = 仅费用影响")

            # ✅ 修复：不再比较包含费用的总盈亏与不包含费用的价格盈亏
            # 只有当价格盈亏与预期不符时才警告
            if abs(rate - entry_rate) < 0.000001:  # 价格基本相同
                if abs(price_profit_ratio) > 0.0001:  # 但价格盈亏不为0
                    logger.warning(f"⚠️ 价格盈亏计算异常！价格无变化但盈亏为: {(price_profit_ratio * 100):+.2f}%")
                else:
                    logger.info(f"✅ 价格无变化，盈亏计算正确")
            else:
                logger.info(f"✅ 价格有变化，盈亏计算正确")

        return True

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                side: str, **kwargs) -> float:
        """
        FreqTrade杠杆管理API - 动态杠杆设置

        使用FreqTrade的杠杆管理：
        1. 根据信号质量调整杠杆
        2. 根据市场波动性调整杠杆
        3. 保守的杠杆策略
        """
        logger.debug(f"FreqTrade杠杆管理 - {pair} at {current_time}, rate: {current_rate}, tag: {entry_tag}")

        # 基础杠杆：保守设置
        base_leverage = min(proposed_leverage * 0.7, max_leverage * 0.5)

        # 根据信号质量调整
        if entry_tag and 'quality' in entry_tag:
            # 高质量信号可以使用稍高杠杆
            quality_leverage = min(proposed_leverage * 0.8, max_leverage * 0.6)
            logger.info(f"🎯 高质量信号杠杆 - {pair}: {proposed_leverage:.1f}x -> {quality_leverage:.1f}x")
            return quality_leverage

        # 做空交易使用更低杠杆
        if side == 'short':
            short_leverage = min(base_leverage * 0.8, max_leverage * 0.4)
            logger.info(f"📉 做空保守杠杆 - {pair}: {proposed_leverage:.1f}x -> {short_leverage:.1f}x")
            return short_leverage

        logger.info(f"📈 多头保守杠杆 - {pair}: {proposed_leverage:.1f}x -> {base_leverage:.1f}x")
        return base_leverage

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                           proposed_stake: float, min_stake: float, max_stake: float,
                           entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        FreqTrade仓位管理API - 动态仓位大小

        使用FreqTrade的仓位管理：
        1. 根据信号质量调整仓位
        2. 根据市场条件调整仓位
        3. 风险控制的仓位管理
        """
        logger.debug(f"FreqTrade仓位管理 - {pair} at {current_time}, rate: {current_rate}")

        # 基础仓位：保守设置（使用50%的建议仓位）
        base_stake = min(proposed_stake * 0.5, max_stake * 0.6)

        # 根据信号质量调整仓位
        if entry_tag and 'quality' in entry_tag:
            # 高质量信号可以使用稍大仓位
            quality_stake = min(proposed_stake * 0.7, max_stake * 0.8)
            logger.info(f"🎯 高质量信号仓位 - {pair}: {proposed_stake:.2f} -> {quality_stake:.2f}")
            return max(quality_stake, min_stake)

        # 确保不低于最小仓位
        final_stake = max(base_stake, min_stake)
        logger.info(f"💰 保守仓位管理 - {pair}: {proposed_stake:.2f} -> {final_stake:.2f}")
        return final_stake

    def custom_exit(self, pair: str, trade, current_time: datetime, current_rate: float,
                   current_profit: float, **kwargs) -> Optional[str]:
        """
        🔧 FreqTrade风险管理API - 自定义出场逻辑

        使用FreqTrade的风险管理机制：
        1. 动态止盈：根据盈利情况调整出场
        2. 时间止损：长时间持仓保护
        3. 波动性保护：异常波动时出场
        4. 依赖主要的ROI和stoploss配置
        """
        logger.debug(f"FreqTrade风险管理检查 - {pair} at rate: {current_rate}")

        # 🔧 动态止盈机制（降低阈值，更早锁定利润）
        if current_profit > 0.02:  # 盈利超过2%
            logger.info(f"💰 动态止盈触发 - {pair}: 盈利 {(current_profit * 100):.2f}%")
            return "dynamic_take_profit"

        # 🔧 时间保护机制
        try:
            holding_time = self._calculate_holding_time(current_time, trade.open_date)
            if holding_time and holding_time.total_seconds() > 7200:  # 超过2小时
                if current_profit > 0.005:  # 微盈利也出场
                    logger.info(f"⏰ 时间保护止盈 - {pair}: 持仓 {holding_time}, 盈利 {(current_profit * 100):.2f}%")
                    return "time_protection_exit"
                elif current_profit < -0.01:  # 亏损超过1%
                    logger.warning(f"⏰ 时间保护止损 - {pair}: 持仓 {holding_time}, 亏损 {(current_profit * 100):.2f}%")
                    return "time_protection_stop"
        except Exception as e:
            logger.debug(f"时间保护计算失败 - {pair}: {e}")

        # 🔧 波动性保护（获取当前ATR）
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) > 0:
                current_atr = dataframe.iloc[-1].get('ATR', 0)
                atr_ma = dataframe['ATR'].rolling(20).mean().iloc[-1]

                if current_atr > atr_ma * 2.5:  # 波动性异常
                    if current_profit > 0:
                        logger.info(f"🌊 波动性保护止盈 - {pair}: ATR异常 {current_atr:.6f}")
                        return "volatility_protection_profit"
                    elif current_profit < -0.015:
                        logger.warning(f"🌊 波动性保护止损 - {pair}: ATR异常 {current_atr:.6f}")
                        return "volatility_protection_stop"
        except Exception as e:
            logger.debug(f"波动性保护计算失败 - {pair}: {e}")

        # 依赖FreqTrade的主要风险管理（ROI和stoploss）
        return None



    def _calculate_holding_time(self, current_time: datetime, open_date: datetime):
        """
        🔧 安全的持仓时间计算 - 处理时区问题

        解决问题：
        1. 时区感知状态不匹配
        2. offset-naive 和 offset-aware datetime 混合

        返回：
        - timedelta: 成功计算的持仓时间
        - None: 计算失败时返回None
        """
        try:
            # 检查时区感知状态
            current_tz_aware = current_time.tzinfo is not None and current_time.tzinfo.utcoffset(current_time) is not None
            open_tz_aware = open_date.tzinfo is not None and open_date.tzinfo.utcoffset(open_date) is not None

            # 情况1: 两个都是时区感知的
            if current_tz_aware and open_tz_aware:
                return current_time - open_date

            # 情况2: 两个都是时区无关的
            elif not current_tz_aware and not open_tz_aware:
                return current_time - open_date

            # 情况3: 时区感知状态不匹配 - 需要统一
            elif current_tz_aware and not open_tz_aware:
                # current_time 有时区，open_date 没有时区
                # 假设 open_date 是 UTC 时间
                open_date_utc = open_date.replace(tzinfo=timezone.utc)
                return current_time - open_date_utc

            elif not current_tz_aware and open_tz_aware:
                # current_time 没有时区，open_date 有时区
                # 假设 current_time 是 UTC 时间
                current_time_utc = current_time.replace(tzinfo=timezone.utc)
                return current_time_utc - open_date

            else:
                # 不应该到达这里，但为了安全起见
                logger.debug("未知的时区状态组合")
                return None

        except Exception as e:
            logger.debug(f"持仓时间计算异常: {e}")
            return None

