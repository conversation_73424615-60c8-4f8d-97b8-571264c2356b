# Docker构建忽略文件
# 功能：排除不必要的文件，减少Docker镜像大小，提高构建速度

# Git相关
.git
.gitignore
.gitattributes

# Docker相关
Dockerfile
docker-compose.yml
.dockerignore

# Python缓存
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/*.log.*

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 测试文件
.pytest_cache/
.coverage
htmlcov/
.tox/

# 操作系统文件
.DS_Store
Thumbs.db

# 大型数据文件（保留结构但排除具体数据）
storage/*/
!storage/.gitkeep
BTCUSDT/
user_data/data/
user_data/backtest_results/
user_data/hyperopt_results/

# 数据库文件（运行时挂载）
*.sqlite
*.sqlite-shm
*.sqlite-wal

# 配置文件中的敏感信息（如果有）
*.pem
*.key
.env
