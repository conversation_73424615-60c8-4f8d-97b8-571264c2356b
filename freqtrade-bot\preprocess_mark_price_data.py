#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
标记价格数据预处理脚本

将JSON格式的标记价格数据转换为FreqTrade标准的feather格式，
以便在回测时高效加载和使用。
"""

import pandas as pd
import json
import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def convert_mark_price_to_feather(symbol: str, data_dir: str = "user_data/data/binance/futures", 
                                  start_time: str = None, end_time: str = None):
    """
    将单个交易对的标记价格JSON数据转换为feather格式
    
    Args:
        symbol: 交易对符号，如 'BTC/USDT:USDT'
        data_dir: 数据目录路径
        start_time: 开始时间，格式 '2024-07-01 00:00:00'
        end_time: 结束时间，格式 '2025-06-30 23:59:59'
    """
    try:
        # 如果指定了时间范围，重新下载数据
        if start_time and end_time:
            logger.info(f"🔄 重新下载标记价格数据 - {symbol}")
            logger.info(f"📅 时间范围: {start_time} 到 {end_time}")
            
            # 下载指定时间范围的标记价格数据
            mark_data = download_mark_price_data(symbol, start_time, end_time)
            if not mark_data:
                logger.error(f"❌ 下载标记价格数据失败 - {symbol}")
                return False
        else:
            # 使用现有JSON文件
            symbol_filename = symbol.replace('/', '_').replace(':', '_')
            json_file = os.path.join(data_dir, f"{symbol_filename}-3m-mark.json")
            
            if not os.path.exists(json_file):
                logger.error(f"❌ 标记价格文件不存在: {json_file}")
                return False
            
            logger.info(f"📖 读取现有标记价格文件: {json_file}")
            with open(json_file, 'r') as f:
                mark_data = json.load(f)
        
        # 转换为DataFrame
        mark_df = pd.DataFrame(mark_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        
        # 转换时间戳为datetime
        mark_df['date'] = pd.to_datetime(mark_df['timestamp'], unit='ms', utc=True)
        mark_df = mark_df.drop('timestamp', axis=1)  # 删除timestamp列
        
        # 确保列顺序符合FreqTrade标准: date, open, high, low, close, volume
        mark_df = mark_df[['date', 'open', 'high', 'low', 'close', 'volume']]
        
        # 按时间排序（但不设置为索引）
        mark_df = mark_df.sort_values('date').reset_index(drop=True)
        
        # 数据质量检查
        logger.info(f"📊 标记价格数据统计 - {symbol}:")
        logger.info(f"   总记录数: {len(mark_df)}")
        logger.info(f"   时间范围: {mark_df['date'].min()} 到 {mark_df['date'].max()}")
        logger.info(f"   价格范围: {mark_df['close'].min():.2f} 到 {mark_df['close'].max():.2f}")
        
        # 保存为feather文件
        symbol_filename = symbol.replace('/', '_').replace(':', '_')
        feather_file = os.path.join(data_dir, f"{symbol_filename}-3m-mark.feather")
        mark_df.to_feather(feather_file)
        
        logger.info(f"✅ 标记价格数据转换完成: {feather_file}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 标记价格数据转换失败 - {symbol}: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def download_mark_price_data(symbol: str, start_time: str, end_time: str):
    """
    从币安API下载指定时间范围的标记价格数据
    
    Args:
        symbol: 交易对符号，如 'BTC/USDT:USDT'  
        start_time: 开始时间字符串，如 '2024-07-01 00:00:00'
        end_time: 结束时间字符串，如 '2025-06-30 23:59:59'
    
    Returns:
        list: 标记价格数据列表
    """
    try:
        import requests
        from datetime import datetime
        
        # 转换符号格式 (BTC/USDT:USDT -> BTCUSDT)
        binance_symbol = symbol.replace('/', '').replace(':USDT', '')
        
        # 转换时间为毫秒时间戳
        start_ts = int(datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
        end_ts = int(datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
        
        logger.info(f"🌐 从币安API下载标记价格数据...")
        logger.info(f"   交易对: {binance_symbol}")
        logger.info(f"   开始时间戳: {start_ts}")
        logger.info(f"   结束时间戳: {end_ts}")
        
        all_data = []
        current_start = start_ts
        limit = 1500  # 币安API单次最大1500条
        
        while current_start < end_ts:
            # 计算当前批次的结束时间
            current_end = min(current_start + (limit * 3 * 60 * 1000) - 1, end_ts)
            
            # 构建API请求
            url = "https://fapi.binance.com/fapi/v1/markPriceKlines"
            params = {
                'symbol': binance_symbol,
                'interval': '3m',
                'startTime': current_start,
                'endTime': current_end,
                'limit': limit
            }
            
            logger.info(f"📥 下载批次: {datetime.fromtimestamp(current_start/1000)} 到 {datetime.fromtimestamp(current_end/1000)}")
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if not data:
                logger.warning(f"⚠️ 该时间段无数据: {current_start} - {current_end}")
                break
            
            all_data.extend(data)
            logger.info(f"   获取 {len(data)} 条记录")
            
            # 更新下次开始时间
            current_start = current_end + 1
            
            # 避免API限制
            import time
            time.sleep(0.1)
        
        logger.info(f"✅ 标记价格数据下载完成: 总共 {len(all_data)} 条记录")
        return all_data
        
    except Exception as e:
        logger.error(f"❌ 下载标记价格数据失败: {str(e)}")
        return None

def main():
    """主函数：处理所有配置的交易对"""
    # 与OHLCV完全匹配的时间范围
    start_time = "2024-07-01 00:00:00"
    end_time = "2025-06-30 23:59:59"
    
    symbols = [
        'BTC/USDT:USDT',
        'ETH/USDT:USDT', 
        'SOL/USDT:USDT',
        'BNB/USDT:USDT',
        'XRP/USDT:USDT',
        'ADA/USDT:USDT'
    ]
    
    logger.info("🚀 开始标记价格数据预处理...")
    logger.info(f"📅 目标时间范围: {start_time} 到 {end_time}")
    logger.info(f"📊 处理交易对: {symbols}")
    
    success_count = 0
    total_count = len(symbols)
    
    for symbol in symbols:
        logger.info(f"\n🔄 处理交易对: {symbol}")
        
        # 重新下载并转换数据，确保时间范围完全匹配
        if convert_mark_price_to_feather(symbol, start_time=start_time, end_time=end_time):
            success_count += 1
            logger.info(f"✅ {symbol} 处理成功")
        else:
            logger.error(f"❌ {symbol} 处理失败")
    
    logger.info(f"\n🎯 预处理完成: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        logger.info("🎉 所有标记价格数据已成功处理，时间范围与OHLCV完全对齐！")
    else:
        logger.warning(f"⚠️ 部分数据处理失败，请检查错误信息")

if __name__ == "__main__":
    main() 