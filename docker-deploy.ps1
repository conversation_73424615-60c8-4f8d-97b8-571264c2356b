# FreqTrade Docker部署脚本
# 功能：自动化Docker容器部署、管理和监控
# 支持：构建镜像、启动服务、查看状态、日志管理

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("build", "start", "stop", "restart", "status", "logs", "clean", "help")]
    [string]$Action = "help"
)

function Show-Help {
    Write-Host "FreqTrade Docker部署管理脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法: .\docker-deploy.ps1 -Action <action>" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "可用操作:" -ForegroundColor Cyan
    Write-Host "  build   - 构建Docker镜像"
    Write-Host "  start   - 启动所有服务"
    Write-Host "  stop    - 停止所有服务"
    Write-Host "  restart - 重启所有服务"
    Write-Host "  status  - 查看服务状态"
    Write-Host "  logs    - 查看服务日志"
    Write-Host "  clean   - 清理未使用的Docker资源"
    Write-Host "  help    - 显示此帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\docker-deploy.ps1 -Action build"
    Write-Host "  .\docker-deploy.ps1 -Action start"
    Write-Host "  .\docker-deploy.ps1 -Action status"
}

function Build-Images {
    Write-Host "构建FreqTrade Docker镜像..." -ForegroundColor Green

    # 询问用户选择Dockerfile
    Write-Host "选择Dockerfile版本:" -ForegroundColor Yellow
    Write-Host "1. 标准版本 (Dockerfile) - 从源码编译TA-Lib"
    Write-Host "2. 预编译版本 (Dockerfile.precompiled) - 使用预编译TA-Lib包"
    $choice = Read-Host "请输入选择 (1-2，默认为2)"

    $dockerfile = "Dockerfile.precompiled"  # 默认使用预编译版本
    if ($choice -eq "1") {
        $dockerfile = "Dockerfile"
        Write-Host "使用标准版本构建..." -ForegroundColor Yellow
    } else {
        Write-Host "使用预编译版本构建..." -ForegroundColor Yellow
    }

    $env:DOCKERFILE = $dockerfile
    docker-compose build --no-cache
    if ($LASTEXITCODE -eq 0) {
        Write-Host "镜像构建成功!" -ForegroundColor Green
    } else {
        Write-Host "镜像构建失败!" -ForegroundColor Red
        Write-Host "提示：如果标准版本失败，请尝试预编译版本" -ForegroundColor Yellow
        exit 1
    }
}

function Start-Services {
    Write-Host "启动FreqTrade服务..." -ForegroundColor Green
    docker-compose up -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服务启动成功!" -ForegroundColor Green
        Write-Host "Web界面地址: http://localhost:8080" -ForegroundColor Yellow
        Write-Host "备用Web界面: http://localhost:8081" -ForegroundColor Yellow
        Start-Sleep 3
        Show-Status
    } else {
        Write-Host "服务启动失败!" -ForegroundColor Red
        exit 1
    }
}

function Stop-Services {
    Write-Host "停止FreqTrade服务..." -ForegroundColor Green
    docker-compose down
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服务停止成功!" -ForegroundColor Green
    } else {
        Write-Host "服务停止失败!" -ForegroundColor Red
    }
}

function Restart-Services {
    Write-Host "重启FreqTrade服务..." -ForegroundColor Green
    Stop-Services
    Start-Sleep 2
    Start-Services
}

function Show-Status {
    Write-Host "FreqTrade服务状态:" -ForegroundColor Green
    docker-compose ps
    Write-Host ""
    Write-Host "Docker资源使用情况:" -ForegroundColor Cyan
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

function Show-Logs {
    Write-Host "选择要查看日志的服务:" -ForegroundColor Green
    Write-Host "1. ADL策略 (freqtrade-adl)"
    Write-Host "2. MPDO策略 (freqtrade-mpdo)"
    Write-Host "3. Web界面 (freqtrade-ui)"
    Write-Host "4. Redis (freqtrade-redis)"
    Write-Host "5. 数据收集 (freqtrade-data-collector)"
    Write-Host "6. 所有服务"
    
    $choice = Read-Host "请输入选择 (1-6)"
    
    switch ($choice) {
        "1" { docker-compose logs -f freqtrade-adl }
        "2" { docker-compose logs -f freqtrade-mpdo }
        "3" { docker-compose logs -f freqtrade-ui }
        "4" { docker-compose logs -f redis }
        "5" { docker-compose logs -f data-collector }
        "6" { docker-compose logs -f }
        default { Write-Host "无效选择!" -ForegroundColor Red }
    }
}

function Clean-Resources {
    Write-Host "清理Docker资源..." -ForegroundColor Green
    Write-Host "清理未使用的容器..."
    docker container prune -f
    Write-Host "清理未使用的镜像..."
    docker image prune -f
    Write-Host "清理未使用的网络..."
    docker network prune -f
    Write-Host "清理未使用的卷..."
    docker volume prune -f
    Write-Host "清理完成!" -ForegroundColor Green
}

# 检查Docker是否运行
try {
    docker version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行"
    }
} catch {
    Write-Host "错误: Docker未运行或未安装!" -ForegroundColor Red
    Write-Host "请确保Docker Desktop已启动" -ForegroundColor Yellow
    exit 1
}

# 执行相应操作
switch ($Action) {
    "build" { Build-Images }
    "start" { Start-Services }
    "stop" { Stop-Services }
    "restart" { Restart-Services }
    "status" { Show-Status }
    "logs" { Show-Logs }
    "clean" { Clean-Resources }
    "help" { Show-Help }
    default { Show-Help }
}
