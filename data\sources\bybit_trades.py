#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Bybit逐笔交易数据获取器

基于CVD.md文档第5.1章要求，实现Bybit V5 API逐笔交易数据的获取和存储。
用于计算多层CVD指标的基础数据源。
"""

import requests
import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from pathlib import Path
import json

class BybitTradesCollector:
    """
    Bybit逐笔交易数据收集器
    
    基于CVD文档要求，获取tick级别的交易数据：
    - timestamp: 交易时间戳
    - price: 成交价格
    - size: 成交数量
    - side: 交易方向 (Buy/Sell)
    """
    
    def __init__(self, 
                 symbol: str = "BTCUSDT",
                 base_url: str = "https://api.bybit.com",
                 storage_dir: str = "user_data/data/trades"):
        """
        初始化数据收集器
        
        Args:
            symbol: 交易对符号
            base_url: Bybit API基础URL
            storage_dir: 数据存储目录
        """
        self.symbol = symbol
        self.base_url = base_url
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # API限制配置
        self.rate_limit_delay = 0.1  # 100ms延迟
        self.max_retries = 3
        
    def fetch_recent_trades(self, 
                           limit: int = 1000,
                           start_time: Optional[int] = None) -> List[Dict]:
        """
        获取最近的交易数据
        
        Args:
            limit: 返回记录数量限制
            start_time: 开始时间戳(毫秒)
            
        Returns:
            交易记录列表
        """
        endpoint = f"{self.base_url}/v5/market/recent-trade"
        
        params = {
            "category": "linear",  # 线性合约
            "symbol": self.symbol,
            "limit": min(limit, 1000)  # API最大限制1000
        }
        
        if start_time:
            params["startTime"] = start_time
            
        for attempt in range(self.max_retries):
            try:
                response = requests.get(endpoint, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                if data.get("retCode") == 0:
                    return data.get("result", {}).get("list", [])
                else:
                    self.logger.error(f"API错误: {data.get('retMsg')}")
                    return []
                    
            except Exception as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    
        return []
        
    def collect_historical_trades(self, 
                                 start_date: datetime,
                                 end_date: datetime,
                                 save_interval: int = 10000) -> pd.DataFrame:
        """
        收集历史交易数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            save_interval: 保存间隔(记录数)
            
        Returns:
            交易数据DataFrame
        """
        self.logger.info(f"开始收集 {self.symbol} 交易数据: {start_date} 到 {end_date}")
        
        all_trades = []
        current_time = int(start_date.timestamp() * 1000)
        end_timestamp = int(end_date.timestamp() * 1000)
        
        while current_time < end_timestamp:
            trades = self.fetch_recent_trades(start_time=current_time)
            
            if not trades:
                self.logger.warning(f"未获取到数据，时间戳: {current_time}")
                current_time += 60000  # 前进1分钟
                continue
                
            # 处理交易数据
            for trade in trades:
                trade_data = {
                    'timestamp': int(trade['time']),
                    'price': float(trade['price']),
                    'size': float(trade['size']),
                    'side': trade['side'],  # 'Buy' or 'Sell'
                    'trade_id': trade['execId']
                }
                all_trades.append(trade_data)
                
            # 更新时间戳到最后一笔交易
            if trades:
                last_timestamp = int(trades[-1]['time'])
                current_time = last_timestamp + 1
                
            # 定期保存数据
            if len(all_trades) >= save_interval:
                self._save_batch(all_trades)
                all_trades = []
                
            # 遵守API限制
            time.sleep(self.rate_limit_delay)
            
        # 保存剩余数据
        if all_trades:
            self._save_batch(all_trades)
            
        self.logger.info(f"数据收集完成，共收集 {len(all_trades)} 条记录")
        return self._load_all_data()
        
    def _save_batch(self, trades: List[Dict]) -> None:
        """保存交易数据批次"""
        if not trades:
            return
            
        df = pd.DataFrame(trades)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # 按日期分组保存
        for date, group in df.groupby(df['datetime'].dt.date):
            filename = f"{self.symbol}_{date.strftime('%Y%m%d')}_trades.parquet"
            filepath = self.storage_dir / filename
            
            if filepath.exists():
                # 合并现有数据
                existing_df = pd.read_parquet(filepath)
                combined_df = pd.concat([existing_df, group]).drop_duplicates(
                    subset=['trade_id'], keep='last'
                ).sort_values('timestamp')
                combined_df.to_parquet(filepath, index=False)
            else:
                group.to_parquet(filepath, index=False)
                
        self.logger.info(f"保存了 {len(trades)} 条交易记录")
        
    def _load_all_data(self) -> pd.DataFrame:
        """加载所有交易数据"""
        all_files = list(self.storage_dir.glob(f"{self.symbol}_*_trades.parquet"))
        
        if not all_files:
            return pd.DataFrame()
            
        dfs = []
        for file in sorted(all_files):
            df = pd.read_parquet(file)
            dfs.append(df)
            
        combined_df = pd.concat(dfs, ignore_index=True)
        combined_df = combined_df.drop_duplicates(subset=['trade_id'], keep='last')
        combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
        
        return combined_df
        
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要信息"""
        df = self._load_all_data()
        
        if df.empty:
            return {"status": "no_data"}
            
        return {
            "total_trades": len(df),
            "date_range": {
                "start": df['datetime'].min().strftime('%Y-%m-%d %H:%M:%S'),
                "end": df['datetime'].max().strftime('%Y-%m-%d %H:%M:%S')
            },
            "volume_stats": {
                "total_volume": df['size'].sum(),
                "avg_trade_size": df['size'].mean(),
                "median_trade_size": df['size'].median()
            },
            "price_range": {
                "min_price": df['price'].min(),
                "max_price": df['price'].max()
            },
            "side_distribution": df['side'].value_counts().to_dict()
        }
