#!/usr/bin/env python3
"""
FreqTrade Docker健康检查脚本
功能：监控Docker容器健康状态，检测服务可用性
支持：容器状态检查、API连通性测试、资源使用监控
"""

import subprocess
import json
import requests
import time
import sys
from datetime import datetime
from typing import Dict, List, Optional

class DockerHealthChecker:
    def __init__(self):
        self.containers = [
            'freqtrade-adl',
            'freqtrade-mpdo', 
            'freqtrade-ui',
            'freqtrade-redis'
        ]
        self.api_base_url = 'http://localhost:8080/api/v1'
        
    def check_container_status(self) -> Dict[str, str]:
        """检查容器运行状态"""
        status = {}
        try:
            result = subprocess.run(
                ['docker', 'ps', '--format', 'json'],
                capture_output=True,
                text=True,
                check=True
            )
            
            running_containers = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    container_info = json.loads(line)
                    running_containers.append(container_info['Names'])
            
            for container in self.containers:
                status[container] = 'running' if container in running_containers else 'stopped'
                
        except subprocess.CalledProcessError as e:
            print(f"检查容器状态失败: {e}")
            for container in self.containers:
                status[container] = 'unknown'
                
        return status
    
    def check_api_health(self) -> bool:
        """检查FreqTrade API健康状态"""
        try:
            response = requests.get(f"{self.api_base_url}/ping", timeout=5)
            return response.status_code == 200
        except requests.RequestException:
            return False
    
    def get_container_stats(self) -> Dict[str, Dict]:
        """获取容器资源使用统计"""
        stats = {}
        try:
            for container in self.containers:
                result = subprocess.run(
                    ['docker', 'stats', container, '--no-stream', '--format', 
                     '{"cpu":"{{.CPUPerc}}","memory":"{{.MemUsage}}","net":"{{.NetIO}}"}'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0 and result.stdout.strip():
                    try:
                        stats[container] = json.loads(result.stdout.strip())
                    except json.JSONDecodeError:
                        stats[container] = {"error": "解析统计数据失败"}
                else:
                    stats[container] = {"error": "容器未运行或获取统计失败"}
                    
        except subprocess.TimeoutExpired:
            print("获取容器统计超时")
        except Exception as e:
            print(f"获取容器统计失败: {e}")
            
        return stats
    
    def check_log_errors(self) -> Dict[str, List[str]]:
        """检查容器日志中的错误"""
        errors = {}
        
        for container in self.containers:
            try:
                result = subprocess.run(
                    ['docker', 'logs', '--tail', '50', container],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    log_lines = result.stderr.split('\n') + result.stdout.split('\n')
                    error_lines = [
                        line for line in log_lines 
                        if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed', 'critical'])
                    ]
                    errors[container] = error_lines[-5:]  # 最近5个错误
                else:
                    errors[container] = ["无法获取日志"]
                    
            except subprocess.TimeoutExpired:
                errors[container] = ["获取日志超时"]
            except Exception as e:
                errors[container] = [f"获取日志失败: {e}"]
                
        return errors
    
    def generate_health_report(self) -> Dict:
        """生成完整的健康检查报告"""
        print("正在进行健康检查...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'container_status': self.check_container_status(),
            'api_health': self.check_api_health(),
            'container_stats': self.get_container_stats(),
            'recent_errors': self.check_log_errors()
        }
        
        # 计算整体健康状态
        running_containers = sum(1 for status in report['container_status'].values() if status == 'running')
        total_containers = len(self.containers)
        
        report['overall_health'] = {
            'status': 'healthy' if running_containers == total_containers and report['api_health'] else 'unhealthy',
            'running_containers': f"{running_containers}/{total_containers}",
            'api_accessible': report['api_health']
        }
        
        return report
    
    def print_health_report(self, report: Dict):
        """打印健康检查报告"""
        print("\n" + "="*60)
        print("FreqTrade Docker健康检查报告")
        print("="*60)
        print(f"检查时间: {report['timestamp']}")
        print(f"整体状态: {report['overall_health']['status'].upper()}")
        print(f"运行容器: {report['overall_health']['running_containers']}")
        print(f"API可访问: {'是' if report['overall_health']['api_accessible'] else '否'}")
        
        print("\n容器状态:")
        print("-" * 40)
        for container, status in report['container_status'].items():
            status_icon = "✓" if status == "running" else "✗"
            print(f"{status_icon} {container}: {status}")
        
        print("\n资源使用:")
        print("-" * 40)
        for container, stats in report['container_stats'].items():
            if 'error' not in stats:
                print(f"{container}:")
                print(f"  CPU: {stats.get('cpu', 'N/A')}")
                print(f"  内存: {stats.get('memory', 'N/A')}")
                print(f"  网络: {stats.get('net', 'N/A')}")
            else:
                print(f"{container}: {stats['error']}")
        
        print("\n最近错误:")
        print("-" * 40)
        for container, errors in report['recent_errors'].items():
            if errors:
                print(f"{container}:")
                for error in errors:
                    if error.strip():
                        print(f"  • {error.strip()}")
            else:
                print(f"{container}: 无错误")

def main():
    checker = DockerHealthChecker()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--json':
        # JSON格式输出
        report = checker.generate_health_report()
        print(json.dumps(report, indent=2, ensure_ascii=False))
    else:
        # 人类可读格式输出
        report = checker.generate_health_report()
        checker.print_health_report(report)
        
        # 根据健康状态设置退出码
        if report['overall_health']['status'] == 'healthy':
            sys.exit(0)
        else:
            sys.exit(1)

if __name__ == "__main__":
    main()
