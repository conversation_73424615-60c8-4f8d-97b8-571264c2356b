#!/usr/bin/env python3
"""
TFI历史数据下载器
功能：下载币安聚合交易历史数据，计算TFI指标，支持奇美拉策略回测
作者：AriQuantification
版本：1.0.0
基于：奇美拉策略数据需求
"""

import os
import sys
import time
import logging
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import argparse

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from freqtrade_bot.user_data.data_providers.tfi_data_provider import TFIDataProvider, AggTradeData

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('TFIHistoricalDownloader')

class TFIHistoricalDownloader:
    """TFI历史数据下载器"""
    
    def __init__(self, symbols: List[str] = None, days: int = 30):
        """
        初始化下载器
        
        Args:
            symbols: 交易对列表，如['BTCUSDT', 'ETHUSDT']
            days: 下载天数
        """
        self.symbols = symbols or ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'XRPUSDT']
        self.days = days
        self.base_url = "https://fapi.binance.com"
        
        # 初始化TFI数据提供者
        self.tfi_provider = TFIDataProvider()
        
        # 创建输出目录
        self.output_dir = Path(__file__).parent.parent / "user_data" / "data" / "tfi_data"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # API限速参数
        self.rate_limit_delay = 0.2  # 200ms延迟
        self.max_retries = 3
        
        logger.info(f"TFI历史数据下载器初始化完成")
        logger.info(f"  交易对: {self.symbols}")
        logger.info(f"  天数: {self.days}")
        logger.info(f"  输出目录: {self.output_dir}")
    
    def download_symbol_data(self, symbol: str) -> bool:
        """下载单个交易对的历史数据"""
        logger.info(f"开始下载 {symbol} 的历史TFI数据...")
        
        try:
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(days=self.days)
            
            # 转换为毫秒时间戳
            start_ts = int(start_time.timestamp() * 1000)
            end_ts = int(end_time.timestamp() * 1000)
            
            # 分批下载数据（每次最多1000条记录）
            all_trades = []
            current_start = start_ts
            
            while current_start < end_ts:
                # 计算当前批次的结束时间（1小时后或总结束时间）
                batch_end = min(current_start + 3600000, end_ts)  # 1小时 = 3600000ms
                
                batch_trades = self._download_trades_batch(
                    symbol, current_start, batch_end
                )
                
                if batch_trades:
                    all_trades.extend(batch_trades)
                    logger.debug(f"{symbol}: 下载了 {len(batch_trades)} 条交易记录")
                
                current_start = batch_end
                time.sleep(self.rate_limit_delay)
            
            if not all_trades:
                logger.warning(f"{symbol}: 没有下载到任何交易数据")
                return False
            
            logger.info(f"{symbol}: 总共下载了 {len(all_trades)} 条交易记录")
            
            # 计算TFI指标
            tfi_data = self._calculate_tfi_from_trades(all_trades, symbol)
            
            if tfi_data.empty:
                logger.warning(f"{symbol}: TFI计算结果为空")
                return False
            
            # 保存数据
            self._save_tfi_data(symbol, tfi_data)
            
            logger.info(f"{symbol}: TFI数据下载和计算完成，共 {len(tfi_data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"下载 {symbol} 数据失败: {e}")
            return False
    
    def _download_trades_batch(self, symbol: str, start_time: int, 
                             end_time: int) -> List[AggTradeData]:
        """下载单批聚合交易数据"""
        
        for attempt in range(self.max_retries):
            try:
                trades = self.tfi_provider.get_aggregated_trades(
                    symbol=symbol,
                    start_time=start_time,
                    end_time=end_time,
                    limit=1000
                )
                
                if trades:
                    return trades
                else:
                    logger.debug(f"{symbol}: 批次 {start_time}-{end_time} 无数据")
                    return []
                    
            except Exception as e:
                logger.warning(f"{symbol}: 批次下载失败 (尝试 {attempt+1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1 * (attempt + 1))  # 递增延迟
                continue
        
        logger.error(f"{symbol}: 批次 {start_time}-{end_time} 下载失败")
        return []
    
    def _calculate_tfi_from_trades(self, trades: List[AggTradeData], 
                                 symbol: str) -> pd.DataFrame:
        """从交易数据计算TFI指标"""
        
        if not trades:
            return pd.DataFrame()
        
        try:
            # 创建时间范围（1分钟间隔）
            start_time = min(trade.timestamp for trade in trades)
            end_time = max(trade.timestamp for trade in trades)
            
            # 生成1分钟时间戳序列
            time_range = pd.date_range(
                start=pd.to_datetime(start_time, unit='ms'),
                end=pd.to_datetime(end_time, unit='ms'),
                freq='1min'
            )
            
            tfi_records = []
            
            for i, timestamp in enumerate(time_range[:-1]):
                period_start = int(timestamp.timestamp() * 1000)
                period_end = int(time_range[i + 1].timestamp() * 1000)
                
                # 计算当前周期的TFI
                period_tfi = self.tfi_provider.calculate_tfi_for_period(
                    trades, period_start, period_end
                )
                
                tfi_records.append({
                    'timestamp': timestamp,
                    'tfi_raw': period_tfi['tfi_raw'],
                    'taker_buy_volume': period_tfi['taker_buy_volume'],
                    'taker_sell_volume': period_tfi['taker_sell_volume'],
                    'total_taker_volume': period_tfi['total_taker_volume'],
                    'taker_buy_ratio': period_tfi['taker_buy_ratio']
                })
            
            # 创建DataFrame
            tfi_df = pd.DataFrame(tfi_records)
            tfi_df.set_index('timestamp', inplace=True)
            
            # 计算TFI Z-Score
            tfi_df['tfi_z_score'] = self._calculate_rolling_zscore(
                tfi_df['tfi_raw'], window=120
            )
            
            # 添加统计信息
            logger.info(f"{symbol} TFI统计:")
            logger.info(f"  平均TFI: {tfi_df['tfi_raw'].mean():.2f}")
            logger.info(f"  TFI标准差: {tfi_df['tfi_raw'].std():.2f}")
            logger.info(f"  最大TFI Z-Score: {tfi_df['tfi_z_score'].max():.2f}")
            logger.info(f"  最小TFI Z-Score: {tfi_df['tfi_z_score'].min():.2f}")
            
            return tfi_df
            
        except Exception as e:
            logger.error(f"计算 {symbol} TFI指标失败: {e}")
            return pd.DataFrame()
    
    def _calculate_rolling_zscore(self, series: pd.Series, window: int) -> pd.Series:
        """计算滚动Z-Score"""
        rolling_mean = series.rolling(window=window, min_periods=10).mean()
        rolling_std = series.rolling(window=window, min_periods=10).std()
        
        # 避免除零
        rolling_std = rolling_std.fillna(1.0)
        rolling_std = rolling_std.replace(0, 1.0)
        
        zscore = (series - rolling_mean) / rolling_std
        return zscore.fillna(0)
    
    def _save_tfi_data(self, symbol: str, tfi_data: pd.DataFrame):
        """保存TFI数据到文件"""
        try:
            # 保存为feather格式（高性能）
            file_path = self.output_dir / f"{symbol}_tfi_data.feather"
            
            # 重置索引以保存时间戳
            tfi_data_to_save = tfi_data.reset_index()
            tfi_data_to_save.to_feather(file_path)
            
            logger.info(f"保存 {symbol} TFI数据到: {file_path}")
            
            # 同时保存CSV格式用于人工检查
            csv_path = self.output_dir / f"{symbol}_tfi_data.csv"
            tfi_data.to_csv(csv_path)
            
            logger.debug(f"保存 {symbol} CSV数据到: {csv_path}")
            
        except Exception as e:
            logger.error(f"保存 {symbol} TFI数据失败: {e}")
    
    def download_all_symbols(self) -> Dict[str, bool]:
        """下载所有交易对的数据"""
        results = {}
        
        logger.info(f"开始下载 {len(self.symbols)} 个交易对的TFI历史数据...")
        
        for i, symbol in enumerate(self.symbols, 1):
            logger.info(f"进度: {i}/{len(self.symbols)} - 处理 {symbol}")
            
            success = self.download_symbol_data(symbol)
            results[symbol] = success
            
            if success:
                logger.info(f"✅ {symbol} 下载成功")
            else:
                logger.error(f"❌ {symbol} 下载失败")
            
            # 符合间隔避免API限制
            if i < len(self.symbols):
                time.sleep(2)
        
        # 输出总结
        successful = sum(results.values())
        total = len(results)
        
        logger.info(f"\n下载完成！成功: {successful}/{total}")
        
        for symbol, success in results.items():
            status = "✅" if success else "❌"
            logger.info(f"  {status} {symbol}")
        
        return results
    
    def verify_data_quality(self, symbol: str) -> bool:
        """验证下载数据的质量"""
        try:
            file_path = self.output_dir / f"{symbol}_tfi_data.feather"
            
            if not file_path.exists():
                logger.error(f"{symbol}: 数据文件不存在")
                return False
            
            df = pd.read_feather(file_path)
            
            if df.empty:
                logger.error(f"{symbol}: 数据文件为空")
                return False
            
            # 检查必要列
            required_columns = ['tfi_raw', 'tfi_z_score', 'taker_buy_volume', 'taker_sell_volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"{symbol}: 缺少必要列: {missing_columns}")
                return False
            
            # 检查数据完整性
            null_counts = df[required_columns].isnull().sum()
            if null_counts.any():
                logger.warning(f"{symbol}: 发现空值: {null_counts.to_dict()}")
            
            # 检查TFI Z-Score范围
            extreme_count = (df['tfi_z_score'].abs() > 5).sum()
            if extreme_count > len(df) * 0.05:  # 超过5%的极值可能有问题
                logger.warning(f"{symbol}: 过多极端TFI值: {extreme_count}/{len(df)}")
            
            logger.info(f"{symbol}: 数据质量检查通过，共 {len(df)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"{symbol}: 数据质量检查失败: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='下载TFI历史数据')
    parser.add_argument('--symbols', nargs='+', 
                       default=['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'XRPUSDT'],
                       help='交易对列表')
    parser.add_argument('--days', type=int, default=30,
                       help='下载天数')
    parser.add_argument('--verify', action='store_true',
                       help='验证数据质量')
    
    args = parser.parse_args()
    
    # 创建下载器
    downloader = TFIHistoricalDownloader(symbols=args.symbols, days=args.days)
    
    try:
        # 下载数据
        results = downloader.download_all_symbols()
        
        # 验证数据质量
        if args.verify:
            logger.info("\n开始数据质量验证...")
            for symbol in args.symbols:
                if results.get(symbol, False):
                    downloader.verify_data_quality(symbol)
        
        # 生成报告
        successful = sum(results.values())
        total = len(results)
        
        print(f"\n{'='*50}")
        print(f"TFI历史数据下载完成")
        print(f"成功率: {successful}/{total} ({successful/total*100:.1f}%)")
        print(f"数据保存路径: {downloader.output_dir}")
        print(f"{'='*50}")
        
    except KeyboardInterrupt:
        logger.info("用户中断下载")
    except Exception as e:
        logger.error(f"下载过程发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 