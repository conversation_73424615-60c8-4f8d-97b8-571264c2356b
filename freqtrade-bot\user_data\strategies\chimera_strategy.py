#!/usr/bin/env python3
"""
奇美拉策略（Chimera Strategy）
功能：基于TFI（吃单流不平衡）和SPI（情绪压力指数）的高频交易策略
作者：AriQuantification
版本：1.0.0
基于：奇美拉计划核心策略实现

策略核心原理：
1. TFI指标：量化市场攻击性买卖压力
2. SPI指标：衡量市场杠杆情绪和资金拥挤程度
3. 背离检测：TFI和SPI的共振/背离作为核心信号
4. 逆向交易：在极端情绪时进行反向操作
5. 动态管理：基于凯利准则的头寸管理
"""

import numpy as np
import pandas as pd
import talib.abstract as ta
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import logging

from freqtrade.strategy.interface import IStrategy
from freqtrade.strategy import DecimalParameter, IntParameter, BooleanParameter
from freqtrade.persistence import Trade
from pandas import DataFrame

# 导入自定义数据提供者
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_providers.tfi_data_provider import tfi_provider
from data_providers.funding_rate_data_provider import funding_provider

logger = logging.getLogger(__name__)

class ChimeraStrategy(IStrategy):
    """
    奇美拉策略 - 基于TFI和SPI指标的高频交易策略
    
    核心逻辑：
    - 多头入场：TFI < -2.5 (恐慌性抛售) + SPI非极端负值 + 价格下跌
    - 空头入场：TFI > +2.5 (FOMO买盘) + SPI极端正值 + 价格上涨
    - 出场：TFI回归中性区域 (-0.5, +0.5)
    - 风险管理：ATR动态止损 + 凯利准则头寸管理
    """

    # 策略基础配置
    INTERFACE_VERSION = 3
    timeframe = '1m'  # 1分钟高频交易
    startup_candle_count: int = 200  # 启动需要的K线数量
    
    # 风险管理参数
    stoploss = -0.05  # 5%止损
    trailing_stop = False
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True

    # 仓位管理
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_exit_signal = True

    # ROI（快速获利了结）
    minimal_roi = {
        "0": 0.05,   # 5%立即止盈
        "5": 0.03,   # 5分钟后3%止盈
        "15": 0.02,  # 15分钟后2%止盈
        "30": 0.01,  # 30分钟后1%止盈
    }

    # 可优化参数 - TFI相关
    tfi_entry_threshold_long = DecimalParameter(-4.0, -1.5, default=-2.5, decimals=1, space="buy", 
                                              optimize=True, load=True)
    tfi_entry_threshold_short = DecimalParameter(1.5, 4.0, default=2.5, decimals=1, space="sell", 
                                               optimize=True, load=True)
    tfi_exit_threshold = DecimalParameter(0.3, 0.8, default=0.5, decimals=1, space="sell", 
                                        optimize=True, load=True)

    # 可优化参数 - SPI相关
    spi_filter_threshold = DecimalParameter(1.5, 3.0, default=2.0, decimals=1, space="buy", 
                                          optimize=True, load=True)
    
    # 可优化参数 - 价格过滤
    price_filter_period = IntParameter(3, 10, default=5, space="buy", optimize=True, load=True)
    
    # 可优化参数 - 风险管理
    atr_period = IntParameter(10, 20, default=14, space="buy", optimize=True, load=True)
    atr_multiplier = DecimalParameter(1.0, 2.5, default=1.5, decimals=1, space="buy", 
                                    optimize=True, load=True)
    
    # 凯利准则参数
    enable_kelly_sizing = BooleanParameter(default=True, space="buy", optimize=False, load=True)
    kelly_fraction = DecimalParameter(0.1, 0.8, default=0.25, decimals=2, space="buy", 
                                    optimize=True, load=True)
    
    # 时间过滤参数
    max_holding_minutes = IntParameter(15, 60, default=30, space="sell", optimize=True, load=True)

    def informative_pairs(self):
        """定义信息对"""
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: Dict) -> DataFrame:
        """添加技术指标"""
        
        # 获取交易对
        pair = metadata['pair']
        
        # 检测运行模式
        is_backtest = self.dp is None or self.dp.runmode.value in ['backtest', 'plot']
        
        # 1. 添加TFI指标
        try:
            dataframe = tfi_provider.get_tfi_indicators(
                dataframe=dataframe,
                pair=pair,
                is_backtest=is_backtest
            )
            logger.debug(f"TFI指标添加成功 - {pair}")
        except Exception as e:
            logger.warning(f"添加TFI指标失败: {e}")
            self._add_default_tfi_indicators(dataframe)

        # 2. 添加SPI指标（资金费率和持仓量）
        try:
            dataframe = funding_provider.get_funding_indicators(
                dataframe=dataframe,
                pair=pair,
                is_backtest=is_backtest
            )
            logger.debug(f"SPI指标添加成功 - {pair}")
        except Exception as e:
            logger.warning(f"添加SPI指标失败: {e}")
            self._add_default_spi_indicators(dataframe)

        # 3. 基础技术指标
        # ATR (平均真实波幅) - 用于动态止损
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        
        # 移动平均线 - 用于价格趋势过滤
        dataframe[f'sma_{self.price_filter_period.value}'] = ta.SMA(
            dataframe, timeperiod=self.price_filter_period.value
        )
        
        # RSI - 辅助过滤指标
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        # 成交量移动平均 - 用于成交量过滤
        dataframe['volume_ma'] = dataframe['volume'].rolling(window=20).mean()
        
        # 4. 奇美拉策略核心信号计算
        dataframe = self._calculate_chimera_signals(dataframe)
        
        return dataframe

    def _calculate_chimera_signals(self, dataframe: DataFrame) -> DataFrame:
        """计算奇美拉策略核心信号"""
        
        # 1. TFI信号强度
        dataframe['tfi_extreme_sell'] = dataframe['tfi_z_score'] < self.tfi_entry_threshold_long.value
        dataframe['tfi_extreme_buy'] = dataframe['tfi_z_score'] > self.tfi_entry_threshold_short.value
        dataframe['tfi_neutral'] = dataframe['tfi_z_score'].abs() < self.tfi_exit_threshold.value
        
        # 2. SPI背景过滤
        dataframe['spi_not_extreme_negative'] = dataframe['spi'] > -self.spi_filter_threshold.value
        dataframe['spi_extreme_positive'] = dataframe['spi'] > self.spi_filter_threshold.value
        
        # 3. 价格趋势过滤
        sma_col = f'sma_{self.price_filter_period.value}'
        dataframe['price_declining'] = dataframe['close'] < dataframe[sma_col]
        dataframe['price_rising'] = dataframe['close'] > dataframe[sma_col]
        
        # 4. 成交量过滤（确保有足够的交易活动）
        dataframe['volume_sufficient'] = dataframe['volume'] > dataframe['volume_ma'] * 0.5
        
        # 5. 奇美拉核心信号组合
        # 多头信号：恐慌性抛售 + 非极端空头背景 + 价格下跌 + 成交量充足
        dataframe['chimera_long_signal'] = (
            dataframe['tfi_extreme_sell'] &
            dataframe['spi_not_extreme_negative'] &
            dataframe['price_declining'] &
            dataframe['volume_sufficient']
        )
        
        # 空头信号：FOMO买盘 + 多头拥挤背景 + 价格上涨 + 成交量充足
        dataframe['chimera_short_signal'] = (
            dataframe['tfi_extreme_buy'] &
            dataframe['spi_extreme_positive'] &
            dataframe['price_rising'] &
            dataframe['volume_sufficient']
        )
        
        # 6. 出场信号：TFI回归中性
        dataframe['chimera_exit_signal'] = dataframe['tfi_neutral']
        
        # 7. 信号强度评分（用于凯利准则）
        dataframe['signal_strength'] = 0.0
        
        # 多头信号强度
        long_strength = (
            (dataframe['tfi_z_score'] < -3.0).astype(int) * 0.4 +  # TFI极值
            (dataframe['spi'] > -1.0).astype(int) * 0.3 +          # SPI非极端
            (dataframe['rsi'] < 30).astype(int) * 0.3               # RSI超卖
        )
        dataframe.loc[dataframe['chimera_long_signal'], 'signal_strength'] = long_strength
        
        # 空头信号强度
        short_strength = (
            (dataframe['tfi_z_score'] > 3.0).astype(int) * 0.4 +   # TFI极值
            (dataframe['spi'] > 2.5).astype(int) * 0.3 +           # SPI极端正值
            (dataframe['rsi'] > 70).astype(int) * 0.3              # RSI超买
        )
        dataframe.loc[dataframe['chimera_short_signal'], 'signal_strength'] = short_strength
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: Dict) -> DataFrame:
        """入场信号"""
        
        # 多头入场
        dataframe.loc[
            dataframe['chimera_long_signal'],
            ['enter_long', 'enter_tag']
        ] = (1, 'chimera_long_panic_absorption')

        # 空头入场  
        dataframe.loc[
            dataframe['chimera_short_signal'],
            ['enter_short', 'enter_tag']
        ] = (1, 'chimera_short_euphoria_fade')

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: Dict) -> DataFrame:
        """出场信号"""
        
        # 基于TFI中性回归的出场
        dataframe.loc[
            dataframe['chimera_exit_signal'],
            ['exit_long', 'exit_short', 'exit_tag']
        ] = (1, 1, 'chimera_tfi_neutral')

        return dataframe

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """动态止损"""
        
        if not hasattr(self, '_dynamic_stoploss_cache'):
            self._dynamic_stoploss_cache = {}

        # 获取当前ATR值
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        if len(dataframe) > 0:
            current_atr = dataframe.iloc[-1]['atr']
            entry_price = trade.open_rate
            
            # 计算ATR止损距离
            atr_stop_distance = current_atr * self.atr_multiplier.value / entry_price
            
            # 动态止损：ATR止损与固定止损的较宽者
            dynamic_stoploss = max(self.stoploss, -atr_stop_distance)
            
            self._dynamic_stoploss_cache[pair] = dynamic_stoploss
            return dynamic_stoploss
        
        return self.stoploss

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                          proposed_stake: float, min_stake: Optional[float], max_stake: float,
                          leverage: float, entry_tag: Optional[str], side: str,
                          **kwargs) -> float:
        """动态头寸管理 - 凯利准则"""
        
        if not self.enable_kelly_sizing.value:
            return proposed_stake

        try:
            # 获取当前分析数据
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) > 0:
                # 获取信号强度
                signal_strength = dataframe.iloc[-1]['signal_strength']
                
                # 凯利准则调整
                kelly_multiplier = min(signal_strength * self.kelly_fraction.value, 1.0)
                
                # 计算调整后的头寸
                adjusted_stake = proposed_stake * kelly_multiplier
                
                # 确保在允许范围内
                adjusted_stake = max(min_stake or 0, min(adjusted_stake, max_stake))
                
                logger.debug(f"凯利调整 {pair}: 强度={signal_strength:.3f}, "
                           f"乘数={kelly_multiplier:.3f}, 原始={proposed_stake:.2f}, "
                           f"调整后={adjusted_stake:.2f}")
                
                return adjusted_stake
                
        except Exception as e:
            logger.warning(f"凯利头寸计算失败 {pair}: {e}")
        
        return proposed_stake

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime,
                   current_rate: float, current_profit: float, **kwargs) -> Optional[str]:
        """自定义出场条件"""
        
        # 时间止损：持仓时间过长
        if trade.open_date_utc:
            holding_time = (current_time - trade.open_date_utc).total_seconds() / 60
            if holding_time > self.max_holding_minutes.value:
                return 'chimera_time_exit'
        
        return None

    def _add_default_tfi_indicators(self, dataframe: DataFrame) -> DataFrame:
        """添加默认TFI指标（当获取失败时）"""
        dataframe['tfi_raw'] = 0.0
        dataframe['tfi_z_score'] = 0.0
        dataframe['taker_buy_volume'] = 0.0
        dataframe['taker_sell_volume'] = 0.0
        dataframe['total_taker_volume'] = 0.0
        dataframe['taker_buy_ratio'] = 0.5
        return dataframe

    def _add_default_spi_indicators(self, dataframe: DataFrame) -> DataFrame:
        """添加默认SPI指标（当获取失败时）"""
        dataframe['spi'] = 0.0
        dataframe['funding_rate'] = 0.0
        dataframe['oi_total'] = 0.0
        dataframe['spi_extreme_positive'] = False
        dataframe['spi_extreme_negative'] = False
        return dataframe

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                          time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                          side: str, **kwargs) -> bool:
        """确认交易入场"""
        
        # 获取最新数据进行最终确认
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) > 0:
                latest = dataframe.iloc[-1]
                
                # 确认TFI和SPI信号仍然有效
                if side == 'long':
                    return (latest['tfi_z_score'] < self.tfi_entry_threshold_long.value and
                           latest['spi'] > -self.spi_filter_threshold.value)
                elif side == 'short':
                    return (latest['tfi_z_score'] > self.tfi_entry_threshold_short.value and
                           latest['spi'] > self.spi_filter_threshold.value)
            
        except Exception as e:
            logger.warning(f"交易确认失败 {pair}: {e}")
        
        return True  # 默认允许交易

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                side: str, **kwargs) -> float:
        """动态杠杆设置"""
        
        try:
            # 获取信号强度
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            
            if len(dataframe) > 0:
                signal_strength = dataframe.iloc[-1]['signal_strength']
                
                # 基于信号强度调整杠杆：强信号使用更高杠杆
                base_leverage = 5.0  # 基础杠杆
                max_signal_leverage = 20.0  # 最大信号杠杆
                
                dynamic_leverage = base_leverage + (signal_strength * (max_signal_leverage - base_leverage))
                dynamic_leverage = min(dynamic_leverage, max_leverage)
                
                logger.debug(f"动态杠杆 {pair}: 信号强度={signal_strength:.3f}, "
                           f"杠杆={dynamic_leverage:.1f}")
                
                return dynamic_leverage
                
        except Exception as e:
            logger.warning(f"动态杠杆计算失败 {pair}: {e}")
        
        return min(5.0, max_leverage)  # 默认5倍杠杆 