#!/usr/bin/env python3
"""
奇美拉策略性能监控和报告工具
功能：实时监控策略表现，生成详细的性能报告和风险分析
作者：AriQuantification
版本：1.0.0
基于：奇美拉策略监控需求
"""

import os
import sys
import json
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import sqlite3
import argparse
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ChimeraMonitor')

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_profit: float
    total_profit_pct: float
    avg_profit: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    max_drawdown: float
    max_drawdown_duration: int
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    volatility: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    avg_trade_duration: float
    total_fees: float

class ChimeraPerformanceMonitor:
    """奇美拉策略性能监控器"""
    
    def __init__(self, db_path: str = "tradesv3_chimera.sqlite"):
        """
        初始化性能监控器
        
        Args:
            db_path: 交易数据库路径
        """
        self.db_path = Path(db_path)
        self.reports_dir = Path("performance_reports")
        self.reports_dir.mkdir(exist_ok=True)
        
        # 图表样式配置
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        logger.info("奇美拉策略性能监控器初始化完成")
        logger.info(f"数据库路径: {self.db_path}")
        logger.info(f"报告输出目录: {self.reports_dir}")
    
    def load_trades_data(self, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """加载交易数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = """
                SELECT 
                    id, exchange, pair, stake_amount, amount, open_rate, close_rate,
                    open_date, close_date, open_trade_value, close_trade_value,
                    profit_ratio, profit_abs, sell_reason, strategy, timeframe,
                    open_timestamp, close_timestamp, leverage, is_short
                FROM trades 
                WHERE close_date IS NOT NULL
                """
                
                if start_date:
                    query += f" AND close_date >= '{start_date}'"
                if end_date:
                    query += f" AND close_date <= '{end_date}'"
                
                query += " ORDER BY close_date"
                
                df = pd.read_sql_query(query, conn)
                
                if df.empty:
                    logger.warning("没有找到已完成的交易记录")
                    return pd.DataFrame()
                
                # 数据类型转换
                df['open_date'] = pd.to_datetime(df['open_date'])
                df['close_date'] = pd.to_datetime(df['close_date'])
                df['trade_duration'] = (df['close_date'] - df['open_date']).dt.total_seconds() / 60  # 分钟
                df['profit_pct'] = df['profit_ratio'] * 100
                
                logger.info(f"加载了 {len(df)} 条交易记录")
                return df
                
        except Exception as e:
            logger.error(f"加载交易数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_performance_metrics(self, trades_df: pd.DataFrame) -> PerformanceMetrics:
        """计算详细性能指标"""
        
        if trades_df.empty:
            return PerformanceMetrics(
                total_trades=0, winning_trades=0, losing_trades=0, win_rate=0.0,
                total_profit=0.0, total_profit_pct=0.0, avg_profit=0.0,
                avg_win=0.0, avg_loss=0.0, profit_factor=0.0,
                max_drawdown=0.0, max_drawdown_duration=0,
                sharpe_ratio=0.0, sortino_ratio=0.0, calmar_ratio=0.0,
                volatility=0.0, max_consecutive_wins=0, max_consecutive_losses=0,
                avg_trade_duration=0.0, total_fees=0.0
            )
        
        # 基础统计
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit_ratio'] > 0])
        losing_trades = len(trades_df[trades_df['profit_ratio'] < 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 利润统计
        total_profit = trades_df['profit_abs'].sum()
        total_profit_pct = trades_df['profit_ratio'].sum() * 100
        avg_profit = trades_df['profit_ratio'].mean() * 100
        
        wins = trades_df[trades_df['profit_ratio'] > 0]['profit_ratio']
        losses = trades_df[trades_df['profit_ratio'] < 0]['profit_ratio']
        
        avg_win = wins.mean() * 100 if len(wins) > 0 else 0
        avg_loss = losses.mean() * 100 if len(losses) > 0 else 0
        
        # 利润因子
        gross_profit = wins.sum() if len(wins) > 0 else 0
        gross_loss = abs(losses.sum()) if len(losses) > 0 else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # 回撤计算
        trades_df = trades_df.sort_values('close_date')
        trades_df['cumulative_profit'] = trades_df['profit_ratio'].cumsum()
        trades_df['running_max'] = trades_df['cumulative_profit'].cummax()
        trades_df['drawdown'] = trades_df['cumulative_profit'] - trades_df['running_max']
        
        max_drawdown = abs(trades_df['drawdown'].min()) * 100
        
        # 最大回撤持续时间
        max_dd_duration = self._calculate_max_drawdown_duration(trades_df)
        
        # 波动率指标
        returns = trades_df['profit_ratio']
        volatility = returns.std() * np.sqrt(252) * 100  # 年化波动率
        
        # 风险调整收益指标
        excess_returns = returns
        sharpe_ratio = excess_returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        
        # Sortino比率（只考虑下行波动）
        downside_returns = returns[returns < 0]
        downside_volatility = downside_returns.std() if len(downside_returns) > 0 else 0
        sortino_ratio = excess_returns.mean() / downside_volatility * np.sqrt(252) if downside_volatility > 0 else 0
        
        # Calmar比率
        annual_return = returns.mean() * 252
        calmar_ratio = annual_return / (max_drawdown/100) if max_drawdown > 0 else 0
        
        # 连续胜负统计
        max_consecutive_wins, max_consecutive_losses = self._calculate_consecutive_trades(trades_df)
        
        # 平均交易持续时间
        avg_trade_duration = trades_df['trade_duration'].mean()
        
        # 总手续费（估算）
        total_fees = trades_df['open_trade_value'].sum() * 0.001  # 假设0.1%手续费率
        
        return PerformanceMetrics(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            total_profit=total_profit,
            total_profit_pct=total_profit_pct,
            avg_profit=avg_profit,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            max_drawdown=max_drawdown,
            max_drawdown_duration=max_dd_duration,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            volatility=volatility,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            avg_trade_duration=avg_trade_duration,
            total_fees=total_fees
        )
    
    def _calculate_max_drawdown_duration(self, trades_df: pd.DataFrame) -> int:
        """计算最大回撤持续时间（分钟）"""
        if 'drawdown' not in trades_df.columns:
            return 0
        
        in_drawdown = False
        current_duration = 0
        max_duration = 0
        
        for _, row in trades_df.iterrows():
            if row['drawdown'] < 0:
                if not in_drawdown:
                    in_drawdown = True
                    current_duration = 0
                current_duration += row['trade_duration']
            else:
                if in_drawdown:
                    max_duration = max(max_duration, current_duration)
                    in_drawdown = False
                    current_duration = 0
        
        # 如果以回撤结束
        if in_drawdown:
            max_duration = max(max_duration, current_duration)
        
        return int(max_duration)
    
    def _calculate_consecutive_trades(self, trades_df: pd.DataFrame) -> Tuple[int, int]:
        """计算最大连续胜负次数"""
        if trades_df.empty:
            return 0, 0
        
        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0
        
        for _, row in trades_df.iterrows():
            if row['profit_ratio'] > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
        
        return max_wins, max_losses
    
    def generate_performance_charts(self, trades_df: pd.DataFrame, 
                                  output_prefix: str = "chimera") -> List[str]:
        """生成性能图表"""
        
        if trades_df.empty:
            logger.warning("没有交易数据，无法生成图表")
            return []
        
        chart_files = []
        
        # 图表1：累积收益曲线
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
        
        # 累积收益
        trades_df_sorted = trades_df.sort_values('close_date')
        trades_df_sorted['cumulative_profit_pct'] = trades_df_sorted['profit_ratio'].cumsum() * 100
        
        ax1.plot(trades_df_sorted['close_date'], trades_df_sorted['cumulative_profit_pct'], 
                linewidth=2, color='blue', label='累积收益')
        ax1.fill_between(trades_df_sorted['close_date'], 0, trades_df_sorted['cumulative_profit_pct'], 
                        alpha=0.3, color='blue')
        ax1.set_title('奇美拉策略 - 累积收益曲线', fontsize=16, fontweight='bold')
        ax1.set_ylabel('累积收益 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 回撤曲线
        trades_df_sorted['running_max'] = trades_df_sorted['cumulative_profit_pct'].cummax()
        trades_df_sorted['drawdown_pct'] = trades_df_sorted['cumulative_profit_pct'] - trades_df_sorted['running_max']
        
        ax2.fill_between(trades_df_sorted['close_date'], 0, trades_df_sorted['drawdown_pct'], 
                        color='red', alpha=0.5, label='回撤')
        ax2.plot(trades_df_sorted['close_date'], trades_df_sorted['drawdown_pct'], 
                color='red', linewidth=1)
        ax2.set_title('回撤曲线', fontsize=14, fontweight='bold')
        ax2.set_ylabel('回撤 (%)', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        chart_file = self.reports_dir / f"{output_prefix}_equity_curve.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        chart_files.append(str(chart_file))
        
        # 图表2：收益分布和统计
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 收益分布直方图
        profit_pct = trades_df['profit_ratio'] * 100
        ax1.hist(profit_pct, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(profit_pct.mean(), color='red', linestyle='--', linewidth=2, label=f'平均: {profit_pct.mean():.2f}%')
        ax1.axvline(profit_pct.median(), color='green', linestyle='--', linewidth=2, label=f'中位数: {profit_pct.median():.2f}%')
        ax1.set_title('单笔交易收益分布', fontsize=14, fontweight='bold')
        ax1.set_xlabel('收益率 (%)', fontsize=12)
        ax1.set_ylabel('频次', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 胜负分布饼图
        wins = len(trades_df[trades_df['profit_ratio'] > 0])
        losses = len(trades_df[trades_df['profit_ratio'] < 0])
        breakevens = len(trades_df[trades_df['profit_ratio'] == 0])
        
        labels = ['盈利', '亏损', '平手']
        sizes = [wins, losses, breakevens]
        colors = ['green', 'red', 'gray']
        
        ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax2.set_title('交易结果分布', fontsize=14, fontweight='bold')
        
        # 月度收益热力图
        if len(trades_df) > 30:  # 至少有一个月的数据
            trades_df['month'] = trades_df['close_date'].dt.to_period('M')
            monthly_returns = trades_df.groupby('month')['profit_ratio'].sum() * 100
            
            if len(monthly_returns) > 1:
                monthly_returns_df = monthly_returns.reset_index()
                monthly_returns_df['year'] = monthly_returns_df['month'].dt.year
                monthly_returns_df['month_num'] = monthly_returns_df['month'].dt.month
                
                pivot_table = monthly_returns_df.pivot_table(
                    values='profit_ratio', index='year', columns='month_num', fill_value=0
                )
                
                sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='RdYlGn', 
                           center=0, ax=ax3, cbar_kws={'label': '月度收益 (%)'})
                ax3.set_title('月度收益热力图', fontsize=14, fontweight='bold')
                ax3.set_xlabel('月份', fontsize=12)
                ax3.set_ylabel('年份', fontsize=12)
            else:
                ax3.text(0.5, 0.5, '数据不足\n无法生成月度热力图', 
                        ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('月度收益热力图', fontsize=14, fontweight='bold')
        else:
            ax3.text(0.5, 0.5, '数据不足\n无法生成月度热力图', 
                    ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('月度收益热力图', fontsize=14, fontweight='bold')
        
        # 交易持续时间分布
        ax4.hist(trades_df['trade_duration'], bins=30, alpha=0.7, color='orange', edgecolor='black')
        ax4.axvline(trades_df['trade_duration'].mean(), color='red', linestyle='--', 
                   linewidth=2, label=f'平均: {trades_df["trade_duration"].mean():.1f}分钟')
        ax4.set_title('交易持续时间分布', fontsize=14, fontweight='bold')
        ax4.set_xlabel('持续时间 (分钟)', fontsize=12)
        ax4.set_ylabel('频次', fontsize=12)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        chart_file = self.reports_dir / f"{output_prefix}_statistics.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        chart_files.append(str(chart_file))
        
        # 图表3：风险分析
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 滚动夏普比率
        window = min(50, len(trades_df) // 4)  # 动态窗口大小
        if window > 10:
            rolling_returns = trades_df_sorted['profit_ratio'].rolling(window=window)
            rolling_sharpe = rolling_returns.mean() / rolling_returns.std() * np.sqrt(252)
            
            ax1.plot(trades_df_sorted['close_date'].iloc[window-1:], rolling_sharpe, 
                    linewidth=2, color='purple')
            ax1.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Sharpe = 1')
            ax1.axhline(y=2, color='green', linestyle='--', alpha=0.7, label='Sharpe = 2')
            ax1.set_title(f'滚动夏普比率 (窗口: {window})', fontsize=14, fontweight='bold')
            ax1.set_ylabel('夏普比率', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
        else:
            ax1.text(0.5, 0.5, '数据不足\n无法计算滚动夏普比率', 
                    ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('滚动夏普比率', fontsize=14, fontweight='bold')
        
        # 交易对分析
        if 'pair' in trades_df.columns:
            pair_performance = trades_df.groupby('pair').agg({
                'profit_ratio': ['count', 'mean', 'sum']
            }).round(4)
            pair_performance.columns = ['交易次数', '平均收益率', '总收益率']
            pair_performance = pair_performance.sort_values('总收益率', ascending=True)
            
            if len(pair_performance) > 0:
                y_pos = np.arange(len(pair_performance))
                ax2.barh(y_pos, pair_performance['总收益率'] * 100, alpha=0.7)
                ax2.set_yticks(y_pos)
                ax2.set_yticklabels(pair_performance.index)
                ax2.set_xlabel('总收益率 (%)', fontsize=12)
                ax2.set_title('交易对表现', fontsize=14, fontweight='bold')
                ax2.grid(True, alpha=0.3)
                
                # 在柱子上添加交易次数标签
                for i, (trades_count, total_return) in enumerate(zip(pair_performance['交易次数'], 
                                                                   pair_performance['总收益率'] * 100)):
                    ax2.text(total_return + 0.1 if total_return >= 0 else total_return - 0.1, 
                            i, f'{int(trades_count)}', va='center', ha='left' if total_return >= 0 else 'right')
            else:
                ax2.text(0.5, 0.5, '无交易对数据', ha='center', va='center', transform=ax2.transAxes)
                ax2.set_title('交易对表现', fontsize=14, fontweight='bold')
        else:
            ax2.text(0.5, 0.5, '无交易对数据', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('交易对表现', fontsize=14, fontweight='bold')
        
        # 收益率 vs 持续时间散点图
        colors = ['green' if x > 0 else 'red' for x in trades_df['profit_ratio']]
        ax3.scatter(trades_df['trade_duration'], trades_df['profit_ratio'] * 100, 
                   c=colors, alpha=0.6, s=30)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.set_xlabel('交易持续时间 (分钟)', fontsize=12)
        ax3.set_ylabel('收益率 (%)', fontsize=12)
        ax3.set_title('收益率 vs 持续时间', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        
        # 累积频次图（胜负交易）
        wins_sorted = trades_df[trades_df['profit_ratio'] > 0]['profit_ratio'].sort_values() * 100
        losses_sorted = trades_df[trades_df['profit_ratio'] < 0]['profit_ratio'].sort_values() * 100
        
        if len(wins_sorted) > 0:
            ax4.plot(wins_sorted.values, np.arange(1, len(wins_sorted) + 1), 
                    color='green', linewidth=2, label=f'盈利交易 ({len(wins_sorted)})')
        
        if len(losses_sorted) > 0:
            ax4.plot(losses_sorted.values, np.arange(1, len(losses_sorted) + 1), 
                    color='red', linewidth=2, label=f'亏损交易 ({len(losses_sorted)})')
        
        ax4.axvline(x=0, color='black', linestyle='--', alpha=0.5)
        ax4.set_xlabel('收益率 (%)', fontsize=12)
        ax4.set_ylabel('累积频次', fontsize=12)
        ax4.set_title('盈亏交易累积分布', fontsize=14, fontweight='bold')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        chart_file = self.reports_dir / f"{output_prefix}_risk_analysis.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        chart_files.append(str(chart_file))
        
        logger.info(f"生成了 {len(chart_files)} 个图表文件")
        return chart_files
    
    def generate_performance_report(self, trades_df: pd.DataFrame, 
                                  metrics: PerformanceMetrics,
                                  output_file: str = None) -> str:
        """生成详细性能报告"""
        
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = self.reports_dir / f"chimera_performance_report_{timestamp}.txt"
        
        report = []
        report.append("=" * 100)
        report.append("奇美拉策略性能报告")
        report.append("=" * 100)
        report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"分析期间: {trades_df['close_date'].min()} 至 {trades_df['close_date'].max()}")
        report.append("")
        
        # 基础统计
        report.append("基础交易统计:")
        report.append("-" * 50)
        report.append(f"总交易数: {metrics.total_trades}")
        report.append(f"盈利交易: {metrics.winning_trades} ({metrics.win_rate:.1%})")
        report.append(f"亏损交易: {metrics.losing_trades} ({(1-metrics.win_rate):.1%})")
        report.append(f"平均交易持续时间: {metrics.avg_trade_duration:.1f} 分钟")
        report.append("")
        
        # 收益统计
        report.append("收益统计:")
        report.append("-" * 50)
        report.append(f"总收益: {metrics.total_profit:.2f} USDT")
        report.append(f"总收益率: {metrics.total_profit_pct:.2f}%")
        report.append(f"平均收益率: {metrics.avg_profit:.2f}%")
        report.append(f"平均盈利: {metrics.avg_win:.2f}%")
        report.append(f"平均亏损: {metrics.avg_loss:.2f}%")
        report.append(f"利润因子: {metrics.profit_factor:.2f}")
        report.append("")
        
        # 风险指标
        report.append("风险指标:")
        report.append("-" * 50)
        report.append(f"最大回撤: {metrics.max_drawdown:.2f}%")
        report.append(f"最大回撤持续时间: {metrics.max_drawdown_duration:.0f} 分钟")
        report.append(f"收益波动率: {metrics.volatility:.2f}%")
        report.append(f"夏普比率: {metrics.sharpe_ratio:.2f}")
        report.append(f"索提诺比率: {metrics.sortino_ratio:.2f}")
        report.append(f"卡尔玛比率: {metrics.calmar_ratio:.2f}")
        report.append("")
        
        # 交易模式分析
        report.append("交易模式分析:")
        report.append("-" * 50)
        report.append(f"最大连续盈利: {metrics.max_consecutive_wins} 次")
        report.append(f"最大连续亏损: {metrics.max_consecutive_losses} 次")
        
        # 费用分析
        report.append(f"估算总手续费: {metrics.total_fees:.2f} USDT")
        fee_impact = (metrics.total_fees / abs(metrics.total_profit)) * 100 if metrics.total_profit != 0 else 0
        report.append(f"手续费占利润比例: {fee_impact:.1f}%")
        report.append("")
        
        # 交易对分析
        if not trades_df.empty and 'pair' in trades_df.columns:
            report.append("交易对表现:")
            report.append("-" * 50)
            pair_stats = trades_df.groupby('pair').agg({
                'profit_ratio': ['count', 'mean', 'sum'],
                'trade_duration': 'mean'
            }).round(4)
            
            for pair in pair_stats.index:
                count = int(pair_stats.loc[pair, ('profit_ratio', 'count')])
                avg_return = pair_stats.loc[pair, ('profit_ratio', 'mean')] * 100
                total_return = pair_stats.loc[pair, ('profit_ratio', 'sum')] * 100
                avg_duration = pair_stats.loc[pair, ('trade_duration', 'mean')]
                
                report.append(f"{pair}:")
                report.append(f"  交易次数: {count}")
                report.append(f"  平均收益: {avg_return:.2f}%")
                report.append(f"  总收益: {total_return:.2f}%")
                report.append(f"  平均持续时间: {avg_duration:.1f}分钟")
                report.append("")
        
        # 时间分析
        if not trades_df.empty:
            report.append("时间分析:")
            report.append("-" * 50)
            
            # 按小时统计
            trades_df['hour'] = trades_df['close_date'].dt.hour
            hourly_stats = trades_df.groupby('hour')['profit_ratio'].agg(['count', 'mean']).round(4)
            
            best_hour = hourly_stats['mean'].idxmax()
            worst_hour = hourly_stats['mean'].idxmin()
            
            report.append(f"最佳交易时段: {best_hour}:00 (平均收益: {hourly_stats.loc[best_hour, 'mean']*100:.2f}%)")
            report.append(f"最差交易时段: {worst_hour}:00 (平均收益: {hourly_stats.loc[worst_hour, 'mean']*100:.2f}%)")
            
            # 按星期统计
            trades_df['weekday'] = trades_df['close_date'].dt.day_name()
            weekly_stats = trades_df.groupby('weekday')['profit_ratio'].agg(['count', 'mean']).round(4)
            
            best_day = weekly_stats['mean'].idxmax()
            worst_day = weekly_stats['mean'].idxmin()
            
            report.append(f"最佳交易日: {best_day} (平均收益: {weekly_stats.loc[best_day, 'mean']*100:.2f}%)")
            report.append(f"最差交易日: {worst_day} (平均收益: {weekly_stats.loc[worst_day, 'mean']*100:.2f}%)")
            report.append("")
        
        # 策略评估
        report.append("策略评估:")
        report.append("-" * 50)
        
        # 评级系统
        score = 0
        max_score = 100
        
        # 胜率评分 (20分)
        if metrics.win_rate >= 0.6:
            win_rate_score = 20
        elif metrics.win_rate >= 0.5:
            win_rate_score = 15
        elif metrics.win_rate >= 0.4:
            win_rate_score = 10
        else:
            win_rate_score = 5
        score += win_rate_score
        
        # 利润因子评分 (25分)
        if metrics.profit_factor >= 2.0:
            pf_score = 25
        elif metrics.profit_factor >= 1.5:
            pf_score = 20
        elif metrics.profit_factor >= 1.2:
            pf_score = 15
        elif metrics.profit_factor >= 1.0:
            pf_score = 10
        else:
            pf_score = 0
        score += pf_score
        
        # 夏普比率评分 (25分)
        if metrics.sharpe_ratio >= 2.0:
            sharpe_score = 25
        elif metrics.sharpe_ratio >= 1.5:
            sharpe_score = 20
        elif metrics.sharpe_ratio >= 1.0:
            sharpe_score = 15
        elif metrics.sharpe_ratio >= 0.5:
            sharpe_score = 10
        else:
            sharpe_score = 5
        score += sharpe_score
        
        # 最大回撤评分 (20分)
        if metrics.max_drawdown <= 5:
            dd_score = 20
        elif metrics.max_drawdown <= 10:
            dd_score = 15
        elif metrics.max_drawdown <= 15:
            dd_score = 10
        elif metrics.max_drawdown <= 20:
            dd_score = 5
        else:
            dd_score = 0
        score += dd_score
        
        # 交易数量评分 (10分)
        if metrics.total_trades >= 100:
            trade_count_score = 10
        elif metrics.total_trades >= 50:
            trade_count_score = 8
        elif metrics.total_trades >= 20:
            trade_count_score = 6
        elif metrics.total_trades >= 10:
            trade_count_score = 4
        else:
            trade_count_score = 2
        score += trade_count_score
        
        # 评级
        if score >= 85:
            rating = "A+ (优秀)"
        elif score >= 75:
            rating = "A (良好)"
        elif score >= 65:
            rating = "B+ (中上)"
        elif score >= 55:
            rating = "B (中等)"
        elif score >= 45:
            rating = "C+ (中下)"
        elif score >= 35:
            rating = "C (较差)"
        else:
            rating = "D (很差)"
        
        report.append(f"策略评分: {score}/{max_score}")
        report.append(f"策略评级: {rating}")
        report.append("")
        
        # 改进建议
        report.append("改进建议:")
        report.append("-" * 50)
        
        suggestions = []
        
        if metrics.win_rate < 0.5:
            suggestions.append("• 胜率偏低，考虑优化入场条件或增加过滤器")
        
        if metrics.profit_factor < 1.2:
            suggestions.append("• 利润因子较低，需要提高盈利交易的平均收益或减少亏损")
        
        if metrics.max_drawdown > 15:
            suggestions.append("• 最大回撤过大，建议加强风险管理和止损设置")
        
        if metrics.sharpe_ratio < 1.0:
            suggestions.append("• 夏普比率偏低，需要提高收益或降低波动性")
        
        if metrics.avg_trade_duration > 120:  # 超过2小时
            suggestions.append("• 平均持仓时间较长，考虑优化出场条件")
        
        if metrics.total_trades < 30:
            suggestions.append("• 交易样本数较少，需要更多数据验证策略有效性")
        
        if not suggestions:
            suggestions.append("• 策略表现良好，继续监控并考虑适当增加仓位")
        
        for suggestion in suggestions:
            report.append(suggestion)
        
        report.append("")
        report.append("=" * 100)
        
        # 保存报告
        report_text = "\n".join(report)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        logger.info(f"性能报告已保存到: {output_file}")
        return report_text
    
    def monitor_live_performance(self, interval_minutes: int = 60):
        """实时性能监控"""
        logger.info(f"启动实时性能监控，更新间隔: {interval_minutes} 分钟")
        
        while True:
            try:
                # 加载最近的交易数据
                recent_data = self.load_trades_data(
                    start_date=(datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
                )
                
                if not recent_data.empty:
                    metrics = self.calculate_performance_metrics(recent_data)
                    
                    # 简化的监控输出
                    logger.info("=" * 60)
                    logger.info("实时性能监控报告")
                    logger.info("=" * 60)
                    logger.info(f"最近7天交易数: {metrics.total_trades}")
                    logger.info(f"胜率: {metrics.win_rate:.1%}")
                    logger.info(f"总收益率: {metrics.total_profit_pct:.2f}%")
                    logger.info(f"最大回撤: {metrics.max_drawdown:.2f}%")
                    logger.info(f"夏普比率: {metrics.sharpe_ratio:.2f}")
                    logger.info("=" * 60)
                
                time.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                logger.info("用户中断监控")
                break
            except Exception as e:
                logger.error(f"监控过程出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再试

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='奇美拉策略性能监控')
    parser.add_argument('--db-path', '-d', default='tradesv3_chimera.sqlite',
                       help='交易数据库路径')
    parser.add_argument('--start-date', '-s', 
                       help='开始日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--end-date', '-e',
                       help='结束日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--live-monitor', '-l', action='store_true',
                       help='启动实时监控模式')
    parser.add_argument('--interval', '-i', type=int, default=60,
                       help='实时监控更新间隔(分钟)')
    parser.add_argument('--no-charts', action='store_true',
                       help='不生成图表')
    
    args = parser.parse_args()
    
    try:
        # 创建监控器
        monitor = ChimeraPerformanceMonitor(args.db_path)
        
        if args.live_monitor:
            # 实时监控模式
            monitor.monitor_live_performance(args.interval)
        else:
            # 分析模式
            logger.info("开始分析奇美拉策略性能...")
            
            # 加载交易数据
            trades_df = monitor.load_trades_data(args.start_date, args.end_date)
            
            if trades_df.empty:
                logger.warning("没有找到交易数据")
                return
            
            # 计算性能指标
            metrics = monitor.calculate_performance_metrics(trades_df)
            
            # 生成图表
            if not args.no_charts:
                chart_files = monitor.generate_performance_charts(trades_df)
                logger.info(f"生成图表: {chart_files}")
            
            # 生成报告
            report = monitor.generate_performance_report(trades_df, metrics)
            print(report)
            
            logger.info("性能分析完成！")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 