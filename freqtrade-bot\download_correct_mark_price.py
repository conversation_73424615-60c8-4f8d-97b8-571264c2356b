#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
币安官方标记价格数据下载脚本

使用币安期货标记价格API：/dapi/v1/markPriceKlines
确保数据的准确性和时间对齐
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BinanceMarkPriceDownloader:
    def __init__(self):
        self.base_url = "https://dapi.binance.com"
        self.mark_price_endpoint = "/dapi/v1/markPriceKlines"
        
    def download_mark_price_klines(self, symbol: str, interval: str = "3m", 
                                 start_time: int = None, end_time: int = None, limit: int = 1500):
        """
        下载币安期货标记价格K线数据
        
        Args:
            symbol: 期货交易对，如 'BTCUSD_PERP'
            interval: K线间隔，如 '3m'
            start_time: 开始时间戳(毫秒)
            end_time: 结束时间戳(毫秒)
            limit: 数据条数限制(最大1500)
        """
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        url = self.base_url + self.mark_price_endpoint
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"下载失败 {symbol}: {e}")
            return None
    
    def convert_to_freqtrade_format(self, symbol: str, start_date: str = "2024-07-01", 
                                  end_date: str = "2025-07-1"):
        """
        下载并转换为FreqTrade兼容格式
        
        Args:
            symbol: 期货交易对
            start_date: 开始日期 "YYYY-MM-DD"
            end_date: 结束日期 "YYYY-MM-DD"
        """
        logger.info(f"🔄 开始下载 {symbol} 标记价格数据")
        
        # 转换日期为时间戳
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp() * 1000)
        
        all_data = []
        current_start = start_timestamp
        
        while current_start < end_timestamp:
            # 计算当前批次的结束时间（最多1500条3分钟数据 = 75小时）
            current_end = min(current_start + (1500 * 3 * 60 * 1000), end_timestamp)
            
            logger.info(f"📥 下载时间段: {datetime.fromtimestamp(current_start/1000)} 到 {datetime.fromtimestamp(current_end/1000)}")
            
            data = self.download_mark_price_klines(
                symbol=symbol,
                start_time=current_start,
                end_time=current_end,
                limit=1500
            )
            
            if data:
                all_data.extend(data)
                logger.info(f"✅ 获取 {len(data)} 条数据")
            
            # 更新下次起始时间
            current_start = current_end + 1
            
            # 避免API限制
            time.sleep(0.1)
        
        if not all_data:
            logger.error(f"❌ 未获取到任何数据: {symbol}")
            return None
            
        # 转换为DataFrame
        df = pd.DataFrame(all_data, columns=[
            'open_time', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'count', 'taker_buy_volume', 
            'taker_buy_quote_volume', 'ignore'
        ])
        
        # 转换数据类型
        df['date'] = pd.to_datetime(df['open_time'], unit='ms', utc=True)
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col])
        
        # 选择FreqTrade需要的列
        df_final = df[['date', 'open', 'high', 'low', 'close', 'volume']].copy()
        df_final = df_final.sort_values('date').reset_index(drop=True)
        
        logger.info(f"✅ 转换完成: {len(df_final)} 条记录")
        logger.info(f"📅 时间范围: {df_final['date'].iloc[0]} 到 {df_final['date'].iloc[-1]}")
        
        return df_final
    
    def save_to_feather(self, df: pd.DataFrame, symbol: str, output_dir: str = "user_data/data/binance/futures"):
        """保存为feather格式"""
        if df is None:
            return False
            
        # FreqTrade期望的文件名格式
        symbol_clean = symbol.replace('/', '_').replace(':', '_')
        filename = f"{symbol_clean}-3m-mark.feather"
        filepath = f"{output_dir}/{filename}"
        
        try:
            df.to_feather(filepath)
            logger.info(f"💾 保存成功: {filepath}")
            return True
        except Exception as e:
            logger.error(f"❌ 保存失败: {e}")
            return False

def main():
    """主函数"""
    downloader = BinanceMarkPriceDownloader()
    
    # 期货交易对映射
    futures_pairs = {
        'BTC/USDT:USDT': 'BTCUSDT',  # FreqTrade格式 -> 币安API格式
        'ETH/USDT:USDT': 'ETHUSDT',
        'SOL/USDT:USDT': 'SOLUSDT',
        'BNB/USDT:USDT': 'BNBUSDT',
        'XRP/USDT:USDT': 'XRPUSDT',
        'ADA/USDT:USDT': 'ADAUSDT'
    }
    
    logger.info("🚀 开始下载币安官方标记价格数据")
    
    success_count = 0
    for freqtrade_pair, binance_symbol in futures_pairs.items():
        logger.info(f"\n🔄 处理 {freqtrade_pair} (币安: {binance_symbol})")
        
        # 下载和转换数据
        df = downloader.convert_to_freqtrade_format(binance_symbol)
        
        if df is not None:
            # 保存为feather文件
            if downloader.save_to_feather(df, freqtrade_pair):
                success_count += 1
        
        time.sleep(1)  # 避免API限制
    
    logger.info(f"\n🎉 完成! 成功处理 {success_count}/{len(futures_pairs)} 个交易对")

if __name__ == "__main__":
    main() 