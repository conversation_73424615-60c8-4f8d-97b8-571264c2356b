#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ADL-Anticipation策略：利用币安期货市场微观结构无效率

基于ADL策略文档，实现预测自动减仓（ADL）事件的5分钟交易策略。
核心论点：通过预测ADL事件的发生，抢先交易由强制平仓引起的价格瞬间波动。

策略特点：
1. 基于"准-ADL"复合指标：ATR波动性扩张 + Z-Score价格偏离
2. 专注USDC-M合约的0%挂单费率优势
3. 严格的1.5:1风险回报比和基于ATR的动态止损
4. 集成FreqTrade专业风险保护系统
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Optional, Union

from freqtrade.strategy import IStrategy, Trade
from freqtrade.strategy import CategoricalParameter
from freqtrade.persistence import Order
from pandas import DataFrame
import talib.abstract as ta




class ADLAnticipationStrategy(IStrategy):
    """
    ADL-Anticipation策略
    
    基于ADL策略文档第3节的完整实现：
    - 交易对动态筛选：选择波动性与流动性的"甜蜜点"
    - "准-ADL"信号生成：ATR扩张 + Z-Score偏离的复合指标
    - 挂单执行：永远做Maker，利用USDC-M的0%费率优势
    - 风险管理：基于ATR的动态止盈止损，1.5:1风险回报比
    """
    
    # 策略元数据 - FreqTrade接口要求
    INTERFACE_VERSION = 3

    # 执行优化参数
    maker_only = CategoricalParameter([True, False], default=True, space="buy")  # 启用挂单模式（利用0%费率）
    
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)

        # 从配置文件读取ADL策略参数 - 严格模式，不提供默认值
        adl_params = config['adl_strategy_params']

        # 新增：策略基础配置
        strategy_config = adl_params['strategy_config']
        self.timeframe = strategy_config['timeframe']
        self.startup_candle_count = strategy_config['startup_candle_count']
        self.stoploss = strategy_config['stoploss']
        self.use_custom_stoploss = strategy_config['use_custom_stoploss']

        # 新增：技术指标配置
        tech_indicators = adl_params['technical_indicators']
        self.rsi_period = tech_indicators['rsi_period']
        self.ema_short_period = tech_indicators['ema_short_period']
        self.sma_short_period = tech_indicators['sma_short_period']
        self.sma_long_period = tech_indicators['sma_long_period']
        self.clip_min_value = tech_indicators['clip_min_value']
        self.clip_max_value = tech_indicators['clip_max_value']
        self.division_protection = tech_indicators['division_protection']

        # 新增：时间管理配置
        time_mgmt = adl_params['time_management']
        self.quick_loss_time_seconds = time_mgmt['quick_loss_time_seconds']
        self.profit_protection_default_time = time_mgmt['profit_protection_default_time']
        self.rsi_threshold_adjustment = time_mgmt['rsi_threshold_adjustment']
        self.trade_duration_check_minutes = time_mgmt['trade_duration_check_minutes']

        # 核心参数 - 必须在配置文件中定义
        self.atr_period = adl_params['atr_period']
        self.atr_sma_period = adl_params['atr_sma_period']
        self.zscore_period = adl_params['zscore_period']

        # 可优化参数 - 必须在配置文件中定义
        self.atr_multiplier = adl_params['atr_multiplier']
        self.zscore_threshold = adl_params['zscore_threshold']
        self.atr_profit_multiplier = adl_params['atr_profit_multiplier']

        # 风险管理参数
        self.atr_stop_multiplier = adl_params['atr_stop_multiplier']

        # 🚀 第三步优化参数加载 - 严格从配置读取
        step3_config = adl_params['step3_optimization']
        self.step3_enabled = step3_config['enable']
        
        if self.step3_enabled:
            # 信号强度分层系统
            tier_config = step3_config['signal_tier_system']
            self.tier1_strength_min = tier_config['tier1_strength_min']
            self.tier2_strength_min = tier_config['tier2_strength_min']
            self.tier3_strength_min = tier_config['tier3_strength_min']
            self.tier1_max_trades = tier_config['tier1_max_trades']
            self.tier2_max_trades = tier_config['tier2_max_trades']
            self.tier3_max_trades = tier_config['tier3_max_trades']
            
            # 激进时间管理
            time_config = step3_config['aggressive_time_management']
            self.aggressive_time_enabled = time_config['enable']
            self.tier1_time_limits = time_config['tier1_time_limits']
            self.tier2_time_limits = time_config['tier2_time_limits']
            self.tier3_time_limits = time_config['tier3_time_limits']
            self.profit_protection_time = time_config['profit_protection_time']
            
            # 价格动量确认
            momentum_config = step3_config['momentum_confirmation']
            self.momentum_confirmation_enabled = momentum_config['enable']
            self.price_acceleration_periods = momentum_config['price_acceleration_periods']
            self.min_acceleration_ratio = momentum_config['min_acceleration_ratio']
            self.volume_burst_multiplier = momentum_config['volume_burst_multiplier']
            
            # 盈利保护机制
            profit_config = step3_config['profit_protection']
            self.profit_protection_enabled = profit_config['enable']
            self.min_profit_for_protection = profit_config['min_profit_for_protection']
            self.protected_time_limit = profit_config['protected_time_limit']
            self.quick_exit_loss_threshold = profit_config['quick_exit_loss_threshold']
            
            self.logger.info("🚀 第三步优化已启用：信号分层 + 激进时间管理 + 动量确认")
        else:
            self.logger.info("📊 使用第二步优化配置（第三步优化未启用）")

        # 加载简化的核心参数
        momentum_config = adl_params['step3_optimization']['momentum_confirmation']
        self.rsi_oversold_threshold = momentum_config['rsi_oversold_threshold']
        self.rsi_overbought_threshold = momentum_config['rsi_overbought_threshold']
        
        config_roi = adl_params['minimal_roi']
        self.minimal_roi = config_roi
        self.maker_only = adl_params['maker_only']
        
        profit_loss_config = adl_params['profit_loss_thresholds']
        self.quick_loss_threshold = profit_loss_config['quick_loss_threshold']
        self.profit_protection_threshold = profit_loss_config['profit_protection_threshold']
        
        basic_config = adl_params['basic_thresholds']
        self.strong_signal_threshold = basic_config['strong_signal_threshold']
        self.default_fillna_value = basic_config['default_fillna_value']

        # 加载杠杆配置
        leverage_config = adl_params['leverage_revolution']
        self.tier1_leverage = leverage_config['tier1_leverage']
        self.tier2_leverage = leverage_config['tier2_leverage'] 
        self.tier3_leverage = leverage_config['tier3_leverage']
        self.base_leverage = leverage_config['base_leverage']
        
        # 加载成交量配置
        volume_config = adl_params['volume_confirmation']
        self.volume_sma_period = volume_config['volume_sma_period']
        self.volume_multiplier = volume_config['volume_multiplier']
        self.enable_volume_filter = volume_config['enable_volume_filter']

        # 策略状态跟踪
        self.last_signal_time = {}  # 记录最后信号时间，避免频繁交易
        self.consecutive_losses = 0  # 连续亏损计数
        self.daily_signal_count = 0  # 日内信号计数
        self.last_trade_date = None  # 上次交易日期

        # 🔍 简化日志记录（专门针对BTC和ETH）
        self.target_pairs = ['BTC/USDT:USDT', 'BTC/USDC:USDC', 'ETH/USDT:USDT', 'ETH/USDC:USDC']
        self.last_log_time = {}  # 记录每个货币对的最后日志时间

        # 设置从配置读取的类属性（FreqTrade要求）
        self.__class__.timeframe = self.timeframe
        self.__class__.startup_candle_count = self.startup_candle_count
        self.__class__.stoploss = self.stoploss
        self.__class__.use_custom_stoploss = self.use_custom_stoploss
        
        # 从配置文件设置minimal_roi（移除硬编码默认值）
        self.__class__.minimal_roi = self.minimal_roi

        self.logger.info("🚀 ADL-Anticipation策略初始化完成（配置驱动模式）")

    def _get_safe_dataframe(self, pair: str) -> DataFrame:
        """
        安全获取数据框的统一工具方法
        """
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            return dataframe if not dataframe.empty else DataFrame()
        except Exception as e:
            self.logger.warning(f"⚠️ 获取数据框失败 - {pair}: {e}")
            return DataFrame()

    def _create_signal_filter(self, dataframe: DataFrame, signal_type: str, tier: str, signal_strength: pd.Series) -> pd.Series:
        """
        创建统一的信号过滤器方法
        
        Args:
            dataframe: 数据框
            signal_type: 'long' 或 'short'
            tier: 'tier1', 'tier2', 'tier3'
            signal_strength: 信号强度序列
            
        Returns:
            过滤后的信号布尔序列
        """
        # 基础ADL信号
        if signal_type == 'long':
            base_signal = dataframe['adl_long_signal'] == True
            rsi_condition = dataframe['rsi'] < self.rsi_oversold_threshold
        else:  # short
            base_signal = dataframe['adl_short_signal'] == True
            rsi_condition = dataframe['rsi'] > self.rsi_overbought_threshold
        
        # 成交量过滤
        volume_filter = dataframe['volume_confirm'] == True
        
        # 信号强度分层
        if tier == 'tier1':
            strength_condition = signal_strength > self.tier1_strength_min
            # 顶级信号：最严格要求
            rsi_final = rsi_condition
        elif tier == 'tier2':
            strength_condition = (signal_strength > self.tier2_strength_min) & (signal_strength <= self.tier1_strength_min)
            # 二级信号：放宽RSI要求
            if signal_type == 'long':
                rsi_final = rsi_condition | (dataframe['rsi'] < self.rsi_oversold_threshold + self.rsi_threshold_adjustment)
            else:
                rsi_final = rsi_condition | (dataframe['rsi'] > self.rsi_overbought_threshold - self.rsi_threshold_adjustment)
        else:  # tier3
            strength_condition = (signal_strength > self.tier3_strength_min) & (signal_strength <= self.tier2_strength_min)
            # 三级信号：不要求RSI条件
            rsi_final = True
        
        # 动量确认（如果启用）
        if self.step3_enabled and self.momentum_confirmation_enabled:
            momentum_confirmed = dataframe['momentum_confirmed'] == True
        else:
            momentum_confirmed = True
        
        return base_signal & volume_filter & rsi_final & strength_condition & momentum_confirmed


    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标
        
        基于ADL策略文档第3.2节，实现"准-ADL"复合指标计算
        """
        pair = metadata.get('pair', 'UNKNOWN')
        self.logger.info(f"🔄 计算ADL指标 - {pair}")
        
        # 🔧 参数确认：显示实际使用的参数值
        if 'BTC/' in pair and ('USDC' in pair or 'USDT' in pair):  # 只对BTC输出，避免日志过多
            self.logger.info(f"🔧 关键参数确认: ATR倍数={self.atr_multiplier}, Z-Score阈值={self.zscore_threshold}")
        
        # 使用内置ADL计算，确保参数一致性
        dataframe = self._calculate_adl_indicators_builtin(dataframe)
            
        # 基础技术指标（使用配置参数）
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=self.rsi_period)
        dataframe['ema_20'] = ta.EMA(dataframe['close'], timeperiod=self.ema_short_period)

        # 趋势判断指标（用于分层止损）
        dataframe['sma_20'] = ta.SMA(dataframe['close'], timeperiod=self.sma_short_period)
        dataframe['sma_50'] = ta.SMA(dataframe['close'], timeperiod=self.sma_long_period)

        # 🔧 成交量确认 - 使用配置参数
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=self.volume_sma_period).mean()
        if self.enable_volume_filter:
            dataframe['volume_confirm'] = dataframe['volume'] > (dataframe['volume_sma'] * self.volume_multiplier)
        else:
            # 如果禁用成交量过滤，所有信号都通过
            dataframe['volume_confirm'] = True
        
        # 🚀 第三步优化：价格动量确认指标
        if self.step3_enabled and self.momentum_confirmation_enabled:
            # 价格加速度检测（3周期价格变化率的变化率）
            dataframe['price_change_1'] = dataframe['close'].pct_change(1)
            dataframe['price_change_2'] = dataframe['close'].pct_change(2) 
            dataframe['price_change_3'] = dataframe['close'].pct_change(3)
            
            # 计算价格加速度（变化率的增加趋势）
            dataframe['price_acceleration'] = (
                abs(dataframe['price_change_1']) / 
                (abs(dataframe['price_change_2']) + self.division_protection)
            ).fillna(self.default_fillna_value)
            
            # 成交量爆发检测
            dataframe['volume_ratio'] = (
                dataframe['volume'] / dataframe['volume_sma']
            ).fillna(self.default_fillna_value)
            
            # 动量确认条件
            dataframe['momentum_confirmed'] = (
                (dataframe['price_acceleration'] > self.min_acceleration_ratio) &
                (dataframe['volume_ratio'] > self.volume_burst_multiplier)
            )
            
            self.logger.info(f"📈 动量确认指标已计算 - {pair}: 加速度阈值{self.min_acceleration_ratio}, 成交量倍数{self.volume_burst_multiplier}")
        else:
            # 第三步优化未启用时，默认所有信号都通过动量确认
            dataframe['momentum_confirmed'] = True
        
        return dataframe
        
    def _calculate_adl_indicators_builtin(self, dataframe: DataFrame) -> DataFrame:
        """
        内置ADL指标计算（备用方案）

        实现与ADLAnticipationIndicator相同的逻辑
        支持hyperopt参数优化
        """
        # 🔧 修复：强制使用配置文件参数（而非hyperopt默认值）
        # 在普通回测中，直接使用config文件加载的参数
        atr_multiplier = self.atr_multiplier
        zscore_threshold = self.zscore_threshold
        


        # 1. ATR波动性扩张检测（与文档伪代码保持一致）
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period)
        dataframe['sma_atr'] = ta.SMA(dataframe['atr'], timeperiod=self.atr_sma_period)
        dataframe['atr_expansion'] = dataframe['atr'] > (dataframe['sma_atr'] * atr_multiplier)

        # 2. Z-Score价格偏离检测
        dataframe['sma'] = dataframe['close'].rolling(window=self.zscore_period).mean()
        dataframe['stddev'] = dataframe['close'].rolling(window=self.zscore_period).std()
        dataframe['zscore'] = (dataframe['close'] - dataframe['sma']) / dataframe['stddev']

        dataframe['zscore_extreme_low'] = dataframe['zscore'] < -zscore_threshold
        dataframe['zscore_extreme_high'] = dataframe['zscore'] > zscore_threshold
        
        # 3. "准-ADL"复合信号
        dataframe['adl_long_signal'] = dataframe['zscore_extreme_low'] & dataframe['atr_expansion']
        dataframe['adl_short_signal'] = dataframe['zscore_extreme_high'] & dataframe['atr_expansion']
        
        # 4. 信号强度（修复计算逻辑，支持hyperopt优化）
        dataframe['adl_signal_strength'] = (
            (np.abs(dataframe['zscore']) / zscore_threshold) *
            (dataframe['atr'] / dataframe['sma_atr']) / atr_multiplier
        ).clip(self.clip_min_value, self.clip_max_value)  # 使用配置参数
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场信号
        
        🚀 第三步优化：实现信号强度分层机制
        - 顶级信号(>2.0)：最高优先级，正常交易
        - 二级信号(1.5-2.0)：中等优先级，限制交易数量
        - 三级信号(1.2-1.5)：低优先级，严格限制
        """
        pair = metadata.get('pair', 'UNKNOWN')
        
        # 基础ADL信号
        long_adl_signal = dataframe['adl_long_signal'] == True
        short_adl_signal = dataframe['adl_short_signal'] == True

        # 🔧 第四步优化：放宽信号质量过滤器以提高频率
        volume_filter = dataframe['volume_confirm'] == True
        rsi_oversold = dataframe['rsi'] < self.rsi_oversold_threshold  # 从配置文件读取
        rsi_overbought = dataframe['rsi'] > self.rsi_overbought_threshold  # 从配置文件读取
        
        # 🚀 第三步优化：使用统一信号过滤器
        if self.step3_enabled:
            signal_strength = dataframe['adl_signal_strength']
            
            # 使用统一信号过滤器创建各层级信号
            tier1_long_condition = self._create_signal_filter(dataframe, 'long', 'tier1', signal_strength)
            tier1_short_condition = self._create_signal_filter(dataframe, 'short', 'tier1', signal_strength)
            tier2_long_condition = self._create_signal_filter(dataframe, 'long', 'tier2', signal_strength)
            tier2_short_condition = self._create_signal_filter(dataframe, 'short', 'tier2', signal_strength)
            tier3_long_condition = self._create_signal_filter(dataframe, 'long', 'tier3', signal_strength)
            tier3_short_condition = self._create_signal_filter(dataframe, 'short', 'tier3', signal_strength)
            
            # 信号标记：为不同等级的信号设置不同的entry_tag
            dataframe.loc[tier1_long_condition, 'enter_long'] = 1
            dataframe.loc[tier1_long_condition, 'enter_tag'] = 'tier1_long'
            
            dataframe.loc[tier1_short_condition, 'enter_short'] = 1  
            dataframe.loc[tier1_short_condition, 'enter_tag'] = 'tier1_short'
            
            dataframe.loc[tier2_long_condition, 'enter_long'] = 1
            dataframe.loc[tier2_long_condition, 'enter_tag'] = 'tier2_long'
            
            dataframe.loc[tier2_short_condition, 'enter_short'] = 1
            dataframe.loc[tier2_short_condition, 'enter_tag'] = 'tier2_short'
            
            dataframe.loc[tier3_long_condition, 'enter_long'] = 1
            dataframe.loc[tier3_long_condition, 'enter_tag'] = 'tier3_long'
            
            dataframe.loc[tier3_short_condition, 'enter_short'] = 1
            dataframe.loc[tier3_short_condition, 'enter_tag'] = 'tier3_short'
            
            # 统计各层级信号
            tier1_signals = tier1_long_condition.sum() + tier1_short_condition.sum()
            tier2_signals = tier2_long_condition.sum() + tier2_short_condition.sum()
            tier3_signals = tier3_long_condition.sum() + tier3_short_condition.sum()
            total_signals = tier1_signals + tier2_signals + tier3_signals
            
            # 🔍 可解释性增强：计算过滤统计
            total_adl_signals = long_adl_signal.sum() + short_adl_signal.sum()
            total_volume_passed = volume_filter.sum()
            total_rsi_passed = (rsi_oversold.sum() + rsi_overbought.sum())
            total_momentum_passed = dataframe['momentum_confirmed'].sum()
            total_above_tier3 = (signal_strength > self.tier3_strength_min).sum()
            filtered_out = total_adl_signals - total_signals
            
            self.logger.info(f"🎯 分层信号统计 - {pair}: T1={tier1_signals}, T2={tier2_signals}, T3={tier3_signals}, 总计={total_signals}")
            
            # 🔍 详细过滤统计（只对主要币种显示，避免日志过多）
            if 'BTC/' in pair or 'ETH/' in pair:
                filter_ratio = (total_signals / total_adl_signals * 100) if total_adl_signals > 0 else 0
                self.logger.info(f"📊 过滤统计 - {pair}:")
                self.logger.info(f"  └ 原始ADL信号: {total_adl_signals}")
                self.logger.info(f"  └ 强度≥{self.tier3_strength_min}: {total_above_tier3}")
                self.logger.info(f"  └ 成交量通过: {total_volume_passed}")
                self.logger.info(f"  └ RSI通过: {total_rsi_passed}")  
                self.logger.info(f"  └ 动量通过: {total_momentum_passed}")
                self.logger.info(f"  └ 最终信号: {total_signals} ({filter_ratio:.1f}%通过率)")
                self.logger.info(f"  └ 被过滤: {filtered_out} (噪音信号)")
        
        else:
            # 第四步优化：降低信号强度门槛以提高频率
            strong_signal = dataframe['adl_signal_strength'] > self.strong_signal_threshold
            
            long_entry_condition = (
                long_adl_signal & 
                volume_filter & 
                rsi_oversold & 
                strong_signal
            )

            short_entry_condition = (
                short_adl_signal & 
                volume_filter & 
                rsi_overbought & 
                strong_signal
            )
            
            dataframe.loc[long_entry_condition, 'enter_long'] = 1
            dataframe.loc[short_entry_condition, 'enter_short'] = 1
            
            total_signals = long_entry_condition.sum() + short_entry_condition.sum()
            self.logger.info(f"🎯 传统信号统计 - {pair}: 总计={total_signals}")
        
        # 基础统计信息（保持向后兼容）
        adl_long_count = long_adl_signal.sum()
        adl_short_count = short_adl_signal.sum()
        
        self.logger.info(f"🔍 ADL信号检测 - {pair}: ADL多头={adl_long_count}, ADL空头={adl_short_count}")

        # 🔍 简化日志记录（仅针对BTC和ETH，每5分钟一次）
        if pair in self.target_pairs and not dataframe.empty:
            current_time = datetime.now()
            last_log = self.last_log_time.get(pair, datetime.min)

            # 每5分钟记录一次
            if (current_time - last_log).total_seconds() >= 300:  # 5分钟 = 300秒
                self.last_log_time[pair] = current_time
                latest = dataframe.iloc[-1]

                # 基础指标
                atr = latest.get('atr', 0)
                atr_sma = latest.get('sma_atr', 1)
                atr_ratio = atr / atr_sma if atr_sma > 0 else 0
                zscore = latest.get('zscore', 0)
                rsi = latest.get('rsi', 50)
                volume_ratio = latest.get('volume_ratio', 0)
                signal_strength = latest.get('adl_signal_strength', 0)

                # 信号状态
                adl_long = latest.get('adl_long_signal', False)
                adl_short = latest.get('adl_short_signal', False)
                volume_ok = latest.get('volume_confirm', False)
                momentum_ok = latest.get('momentum_confirmed', True)

                # 没有成交的原因
                reasons = []
                if not adl_long and not adl_short:
                    if atr_ratio < self.atr_multiplier:
                        reasons.append(f"ATR未扩张({atr_ratio:.2f}<{self.atr_multiplier})")
                    if abs(zscore) < self.zscore_threshold:
                        reasons.append(f"Z-Score未达极值({zscore:.3f},需±{self.zscore_threshold})")

                if not volume_ok:
                    reasons.append(f"成交量不足({volume_ratio:.2f}<{self.volume_multiplier})")
                if rsi > self.rsi_oversold_threshold and rsi < self.rsi_overbought_threshold:
                    reasons.append(f"RSI中性({rsi:.1f})")
                if not momentum_ok:
                    reasons.append("动量确认失败")
                if signal_strength <= (self.tier3_strength_min if self.step3_enabled else self.strong_signal_threshold):
                    threshold = self.tier3_strength_min if self.step3_enabled else self.strong_signal_threshold
                    reasons.append(f"信号强度低({signal_strength:.3f}<={threshold})")

                # 输出日志
                self.logger.info(f"📊 {pair} 5分钟统计 ({current_time.strftime('%H:%M:%S')})")
                self.logger.info(f"  指标: ATR比率{atr_ratio:.2f} Z-Score{zscore:.3f} RSI{rsi:.1f} 成交量比率{volume_ratio:.2f}")
                self.logger.info(f"  信号: ADL多头{'✅' if adl_long else '❌'} ADL空头{'✅' if adl_short else '❌'} 强度{signal_strength:.3f}")

                if reasons:
                    self.logger.info(f"  ❌ 没有成交原因: {' | '.join(reasons)}")
                else:
                    self.logger.info(f"  ✅ 所有条件满足")

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充出场信号
        
        基于ADL策略文档：主要依赖custom_exit()和ROI/stoploss配置，保持简单
        """
        # 反向ADL信号作为出场确认（可选）
        dataframe.loc[dataframe['adl_short_signal'] == True, 'exit_long'] = 1
        dataframe.loc[dataframe['adl_long_signal'] == True, 'exit_short'] = 1
        
        return dataframe

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: datetime,
                           entry_tag: Optional[str], side: str, **kwargs) -> bool:
        """
        确认交易入场 - 🚀 多仓位优化版本

        ADL策略核心：强制使用挂单（Maker），利用USDC-M的0%费率
        新增：多仓位模式下的智能信号过滤和协调机制
        """
        # ADL策略要求：只交易USDC-M或USDT-M合约（利用0%挂单费率）
        if 'USDC' not in pair and 'USDT' not in pair:
            self.logger.error(f"❌ 拒绝非USDC/USDT合约 - {pair}: ADL策略要求0%挂单费率")
            return False

        if self.maker_only:
            # 检查是否为挂单
            if order_type != 'limit':
                self.logger.warning(f"❌ 拒绝市价单 - {pair}: ADL策略要求挂单执行")
                return False

        contract_type = "USDC-M" if "USDC" in pair else "USDT-M"
        self.logger.info(f"✅ {contract_type}合约挂单确认 - {pair}")
        return True

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        简化的自定义出场逻辑
        """
        dataframe = self._get_safe_dataframe(pair)
        if dataframe.empty:
            return None

        # ATR动态止盈检查
        entry_atr = getattr(trade, 'entry_atr', None)
        if entry_atr is None:
            entry_atr = dataframe.iloc[-1].get('atr', 0)
            trade.entry_atr = entry_atr

        if entry_atr > 0:
            # 计算动态止盈目标
            if trade.is_short == False:  # 多头交易
                target_profit_price = trade.open_rate + (self.atr_profit_multiplier * entry_atr)
                target_profit_pct = (target_profit_price - trade.open_rate) / trade.open_rate
                if current_profit >= target_profit_pct:
                    return "atr_profit_target"
            else:  # 空头交易
                target_profit_price = trade.open_rate - (self.atr_profit_multiplier * entry_atr)
                target_profit_pct = (trade.open_rate - target_profit_price) / trade.open_rate
                if current_profit >= target_profit_pct:
                    return "atr_profit_target"

        # 简化的时间管理
        trade_duration = current_time - trade.open_date_utc
        
        if self.step3_enabled and self.aggressive_time_enabled:
            # 快速亏损止损
            if (self.profit_protection_enabled and 
                current_profit < self.quick_loss_threshold and 
                trade_duration.total_seconds() > self.quick_loss_time_seconds):
                return "quick_loss_exit"
            
            # 根据信号等级确定时间限制
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            if 'tier1' in entry_tag:
                time_limits = self.tier1_time_limits
            elif 'tier2' in entry_tag:
                time_limits = self.tier2_time_limits
            elif 'tier3' in entry_tag:
                time_limits = self.tier3_time_limits
            else:
                time_limits = self.tier2_time_limits
            
            # 盈利保护或根据ATR选择时间限制
            if (self.profit_protection_enabled and 
                current_profit > self.profit_protection_threshold):
                time_limit_minutes = self.profit_protection_default_time  # 使用配置参数
            else:
                # 简化的ATR时间选择
                current_atr = dataframe.iloc[-1].get('atr', 0)
                atr_sma = dataframe.iloc[-1].get('sma_atr', 1)
                atr_ratio = current_atr / atr_sma if atr_sma > 0 else 1.0
                
                if atr_ratio >= 1.8:
                    time_limit_minutes = time_limits[4]
                elif atr_ratio >= 1.3:
                    time_limit_minutes = time_limits[3]
                elif atr_ratio >= 1.0:
                    time_limit_minutes = time_limits[2]
                elif atr_ratio >= 0.7:
                    time_limit_minutes = time_limits[1]
                else:
                    time_limit_minutes = time_limits[0]
            
            # 检查时间限制
            if trade_duration.total_seconds() > time_limit_minutes * 60:
                return "aggressive_time_stop"

        return None

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        简化的杠杆控制
        """
        if entry_tag:
            if 'tier1' in entry_tag:
                leverage = min(self.tier1_leverage, max_leverage)
            elif 'tier2' in entry_tag:
                leverage = min(self.tier2_leverage, max_leverage)
            elif 'tier3' in entry_tag:
                leverage = min(self.tier3_leverage, max_leverage)
            else:
                leverage = min(self.base_leverage, max_leverage)
        else:
            leverage = min(self.base_leverage, max_leverage)
        
        return leverage
