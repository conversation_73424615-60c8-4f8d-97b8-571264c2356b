{"strategy": {"name": "CryptoMomentumStrategy", "description": "5分钟加密货币动量策略 - 基于动量、趋势跟踪和突破交易", "timeframe": "5m", "market_type": "crypto", "trading_style": "high_frequency", "features": ["多层次动量分析", "自适应趋势跟踪", "关键位突破检测", "成交量确认机制", "动态风险管理"], "parameters": {"momentum_fast_period": 2, "momentum_slow_period": 6, "momentum_threshold": 0.001, "trend_fast_ema": 3, "trend_slow_ema": 8, "trend_filter_ema": 13, "breakout_lookback": 3, "breakout_threshold": 0.0008, "volume_ma_period": 5, "volume_threshold": 1.2, "max_trades_per_hour": 10, "signal_cooldown_minutes": 8}}, "freqtrade": {"integration": {"enabled": true, "professional_indicators": {"rsi_period": 14, "macd_fast": 12, "macd_slow": 26, "macd_signal": 9}}, "trading": {"minimal_roi": 0.025, "stoploss": -0.008, "timeframe": "5m", "can_short": true, "startup_candle_count": 100}, "order_types": {"entry": "market", "exit": "market", "stoploss": "market"}}, "risk_management": {"max_position_size": 1.0, "max_open_trades": 3, "max_daily_trades": 50, "max_hourly_trades": 12, "cooldown_periods": {"signal_cooldown": 3, "pair_cooldown": 10, "loss_cooldown": 30}, "stop_loss": {"type": "atr_dynamic", "base_percentage": 0.8, "atr_multiplier": 1.5, "trailing_enabled": true, "trailing_offset": 0.2}, "take_profit": {"type": "dynamic", "base_percentage": 2.5, "scaling_enabled": true, "scaling_levels": [1.2, 2.0, 3.0]}}, "optimization": {"enabled": true, "method": "grid_search", "objective": "sharpe_ratio", "grid": {"momentum_fast_period": [2, 3, 4, 5], "momentum_slow_period": [6, 8, 10, 12], "momentum_threshold": [0.0005, 0.001, 0.0015, 0.002], "trend_fast_ema": [3, 5, 8, 10], "trend_slow_ema": [10, 13, 16, 21], "breakout_lookback": [3, 4, 5, 6], "breakout_threshold": [0.0005, 0.0008, 0.001, 0.0012], "volume_threshold": [1.05, 1.1, 1.15, 1.2], "max_trades_per_hour": [8, 10, 12, 15], "signal_cooldown_minutes": [2, 3, 5, 8]}, "constraints": {"min_trades_per_day": 20, "max_trades_per_day": 100, "min_win_rate": 0.45, "max_drawdown": 0.15, "min_sharpe_ratio": 0.5}}, "signal_quality": {"filters": {"momentum_confirmation": {"enabled": true, "min_acceleration": 0.0002, "trend_alignment": true, "multi_timeframe_check": true}, "volume_confirmation": {"enabled": true, "min_volume_ratio": 1.15, "volume_trend": true, "volume_spike_detection": true}, "breakout_confirmation": {"enabled": true, "min_strength": 0.0008, "follow_through": true, "resistance_support_check": true}}, "scoring": {"momentum_weight": 0.35, "trend_weight": 0.35, "breakout_weight": 0.2, "volume_weight": 0.1, "min_score": 0.6}}, "market_state_adaptive": {"enabled": true, "volatility_periods": {"high_volatility": {"atr_threshold": 0.02, "parameter_adjustments": {"momentum_threshold_multiplier": 0.8, "breakout_threshold_multiplier": 0.75, "volume_threshold_multiplier": 0.9, "signal_quality_min_score": 0.5}}, "normal_volatility": {"atr_threshold": 0.015, "parameter_adjustments": {"momentum_threshold_multiplier": 1.0, "breakout_threshold_multiplier": 1.0, "volume_threshold_multiplier": 1.0, "signal_quality_min_score": 0.6}}, "low_volatility": {"atr_threshold": 0.01, "parameter_adjustments": {"momentum_threshold_multiplier": 1.25, "breakout_threshold_multiplier": 1.3, "volume_threshold_multiplier": 1.1, "signal_quality_min_score": 0.7}}}, "lookback_period": 20, "update_frequency": 5}, "market_conditions": {"volatility_adjustment": {"enabled": true, "low_volatility": {"threshold": 0.01, "parameter_multiplier": 0.8}, "high_volatility": {"threshold": 0.05, "parameter_multiplier": 1.2}}, "trend_strength_adjustment": {"enabled": true, "strong_trend": {"threshold": 0.02, "momentum_boost": 1.2}, "weak_trend": {"threshold": 0.005, "momentum_reduction": 0.8}}}, "performance_targets": {"daily": {"min_trades": 5, "target_trades": 15, "max_trades": 30, "target_return": 0.05, "max_drawdown": 0.03}, "hourly": {"min_trades": 0.2, "target_trades": 0.6, "max_trades": 1.2, "target_return": 0.002, "max_drawdown": 0.005}, "quality": {"min_win_rate": 0.55, "target_win_rate": 0.65, "min_profit_factor": 1.8, "min_sharpe_ratio": 1.5}}, "backtest": {"data_requirements": {"timeframe": "5m", "min_history_days": 30, "required_columns": ["open", "high", "low", "close", "volume"]}, "engine_settings": {"initial_capital": 10000, "commission": 0.001, "slippage": 0.0005, "use_realistic_fills": true}, "analysis": {"metrics": ["total_return", "sharpe_ratio", "max_drawdown", "win_rate", "profit_factor", "avg_trade_duration", "trades_per_day"], "benchmarks": ["buy_and_hold", "random_strategy"]}}, "logging": {"level": "INFO", "detailed_signals": false, "trade_logging": true, "performance_logging": true}}