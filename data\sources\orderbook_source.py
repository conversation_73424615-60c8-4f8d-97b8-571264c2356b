#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
订单簿数据源实现

为WaterFallAlpha策略提供实时订单簿数据，用于计算订单簿不平衡(OBI)指标。
支持多交易所的订单簿数据获取和处理。
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import pandas as pd
import numpy as np
import websockets
import requests
from pathlib import Path

from data.base import DataSource


@dataclass
class OrderBookSnapshot:
    """订单簿快照数据结构"""
    symbol: str
    timestamp: datetime
    bids: List[List[float]]  # [[price, size], ...]
    asks: List[List[float]]  # [[price, size], ...]
    
    def calculate_obi(self, levels: int = 10) -> float:
        """
        计算订单簿不平衡(OBI)
        
        Args:
            levels: 计算的订单簿层数
            
        Returns:
            OBI值 (-1到1之间)
        """
        # 获取指定层数的买卖单量
        bid_volume = sum(bid[1] for bid in self.bids[:levels])
        ask_volume = sum(ask[1] for ask in self.asks[:levels])
        
        total_volume = bid_volume + ask_volume
        if total_volume == 0:
            return 0.0
            
        return (bid_volume - ask_volume) / total_volume


class OrderBookSource(DataSource):
    """
    订单簿数据源
    
    实现多交易所订单簿数据的实时获取和处理，支持：
    1. WebSocket实时订单簿数据流
    2. 订单簿不平衡(OBI)计算
    3. 订单簿深度分析
    """
    
    def __init__(self, exchange: str = 'binance', symbols: List[str] = None, 
                 depth: int = 20, storage_dir: str = None):
        """
        初始化订单簿数据源
        
        Args:
            exchange: 交易所名称 ('binance', 'bybit')
            symbols: 监控的交易对列表
            depth: 订单簿深度
            storage_dir: 数据存储目录
        """
        super().__init__()
        self.exchange = exchange.lower()
        self.symbols = symbols or ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'XRPUSDT']
        self.depth = depth
        self.storage_dir = Path(storage_dir) if storage_dir else Path('data/storage/orderbook')
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 配置不同交易所的WebSocket URL
        self.ws_urls = {
            'binance': 'wss://stream.binance.com:9443/ws',
            'bybit': 'wss://stream.bybit.com/v5/public/linear'
        }
        
        # 实时数据缓存
        self.orderbook_cache: Dict[str, OrderBookSnapshot] = {}
        self.obi_history: Dict[str, List[Dict]] = {symbol: [] for symbol in self.symbols}
        self.cache_lock = asyncio.Lock()
        
        # 回调函数
        self.on_orderbook_callback: Optional[Callable] = None
        self.on_obi_callback: Optional[Callable] = None
        
    def set_orderbook_callback(self, callback: Callable[[OrderBookSnapshot], None]):
        """设置订单簿更新回调函数"""
        self.on_orderbook_callback = callback
        
    def set_obi_callback(self, callback: Callable[[str, float, datetime], None]):
        """设置OBI更新回调函数"""
        self.on_obi_callback = callback
        
    async def start_realtime_stream(self):
        """启动实时订单簿数据流"""
        self.logger.info(f"启动{self.exchange}实时订单簿数据流...")
        
        if self.exchange == 'binance':
            await self._start_binance_stream()
        elif self.exchange == 'bybit':
            await self._start_bybit_stream()
        else:
            raise ValueError(f"不支持的交易所: {self.exchange}")
            
    async def _start_binance_stream(self):
        """启动Binance订单簿数据流"""
        # 构建订阅消息
        streams = [f"{symbol.lower()}@depth{self.depth}@100ms" for symbol in self.symbols]
        ws_url = f"{self.ws_urls['binance']}/{'/'.join(streams)}"
        
        try:
            async with websockets.connect(ws_url) as websocket:
                self.logger.info(f"已连接Binance订单簿流: {self.symbols}")
                
                async for message in websocket:
                    await self._process_binance_message(message)
                    
        except Exception as e:
            self.logger.error(f"Binance WebSocket连接错误: {e}")
            await asyncio.sleep(5)
            await self._start_binance_stream()
            
    async def _start_bybit_stream(self):
        """启动Bybit订单簿数据流"""
        subscribe_msg = {
            "op": "subscribe",
            "args": [f"orderbook.{self.depth}.{symbol}" for symbol in self.symbols]
        }
        
        try:
            async with websockets.connect(self.ws_urls['bybit']) as websocket:
                await websocket.send(json.dumps(subscribe_msg))
                self.logger.info(f"已订阅Bybit订单簿: {self.symbols}")
                
                async for message in websocket:
                    await self._process_bybit_message(message)
                    
        except Exception as e:
            self.logger.error(f"Bybit WebSocket连接错误: {e}")
            await asyncio.sleep(5)
            await self._start_bybit_stream()
            
    async def _process_binance_message(self, message: str):
        """处理Binance订单簿消息"""
        try:
            data = json.loads(message)
            
            if 'stream' in data and 'depth' in data['stream']:
                orderbook_data = data['data']
                symbol = orderbook_data['s']
                
                snapshot = OrderBookSnapshot(
                    symbol=symbol,
                    timestamp=datetime.fromtimestamp(orderbook_data['E'] / 1000),
                    bids=[[float(bid[0]), float(bid[1])] for bid in orderbook_data['bids']],
                    asks=[[float(ask[0]), float(ask[1])] for ask in orderbook_data['asks']]
                )
                
                await self._update_orderbook(snapshot)
                
        except Exception as e:
            self.logger.error(f"处理Binance消息错误: {e}")
            
    async def _process_bybit_message(self, message: str):
        """处理Bybit订单簿消息"""
        try:
            data = json.loads(message)
            
            if data.get('topic', '').startswith('orderbook.'):
                orderbook_data = data.get('data', {})
                symbol = orderbook_data.get('s', '')
                
                snapshot = OrderBookSnapshot(
                    symbol=symbol,
                    timestamp=datetime.fromtimestamp(int(orderbook_data['ts']) / 1000),
                    bids=[[float(bid[0]), float(bid[1])] for bid in orderbook_data.get('b', [])],
                    asks=[[float(ask[0]), float(ask[1])] for ask in orderbook_data.get('a', [])]
                )
                
                await self._update_orderbook(snapshot)
                
        except Exception as e:
            self.logger.error(f"处理Bybit消息错误: {e}")
            
    async def _update_orderbook(self, snapshot: OrderBookSnapshot):
        """更新订单簿缓存并计算OBI"""
        async with self.cache_lock:
            # 更新缓存
            self.orderbook_cache[snapshot.symbol] = snapshot
            
            # 计算OBI
            obi_5 = snapshot.calculate_obi(5)
            obi_10 = snapshot.calculate_obi(10)
            
            # 记录OBI历史
            obi_record = {
                'timestamp': snapshot.timestamp,
                'obi_5': obi_5,
                'obi_10': obi_10,
                'bid_volume_5': sum(bid[1] for bid in snapshot.bids[:5]),
                'ask_volume_5': sum(ask[1] for ask in snapshot.asks[:5]),
                'bid_volume_10': sum(bid[1] for bid in snapshot.bids[:10]),
                'ask_volume_10': sum(ask[1] for ask in snapshot.asks[:10])
            }
            
            self.obi_history[snapshot.symbol].append(obi_record)
            
            # 保持历史记录在合理范围内
            if len(self.obi_history[snapshot.symbol]) > 1000:
                self.obi_history[snapshot.symbol] = self.obi_history[snapshot.symbol][-500:]
                
        # 触发回调
        if self.on_orderbook_callback:
            self.on_orderbook_callback(snapshot)
            
        if self.on_obi_callback:
            self.on_obi_callback(snapshot.symbol, obi_10, snapshot.timestamp)
            
        self.logger.debug(f"订单簿更新: {snapshot.symbol} OBI(10): {obi_10:.4f}")
        
    def get_current_obi(self, symbol: str, levels: int = 10) -> Optional[float]:
        """获取当前OBI值"""
        if symbol in self.orderbook_cache:
            return self.orderbook_cache[symbol].calculate_obi(levels)
        return None
        
    def get_obi_history(self, symbol: str, minutes: int = 60) -> pd.DataFrame:
        """
        获取OBI历史数据
        
        Args:
            symbol: 交易对
            minutes: 历史数据分钟数
            
        Returns:
            包含OBI历史的DataFrame
        """
        if symbol not in self.obi_history:
            return pd.DataFrame()
            
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_data = [
            record for record in self.obi_history[symbol]
            if record['timestamp'] >= cutoff_time
        ]
        
        if recent_data:
            df = pd.DataFrame(recent_data)
            df.set_index('timestamp', inplace=True)
            return df
        else:
            return pd.DataFrame()
            
    def calculate_obi_statistics(self, symbol: str, window_minutes: int = 5) -> Dict[str, float]:
        """
        计算OBI统计指标
        
        Args:
            symbol: 交易对
            window_minutes: 统计窗口(分钟)
            
        Returns:
            包含统计指标的字典
        """
        df = self.get_obi_history(symbol, window_minutes)
        
        if df.empty:
            return {}
            
        return {
            'obi_10_mean': df['obi_10'].mean(),
            'obi_10_std': df['obi_10'].std(),
            'obi_10_min': df['obi_10'].min(),
            'obi_10_max': df['obi_10'].max(),
            'obi_10_current': df['obi_10'].iloc[-1] if len(df) > 0 else 0,
            'obi_volatility': df['obi_10'].rolling(window=10).std().iloc[-1] if len(df) >= 10 else 0
        }
        
    def stop_stream(self):
        """停止数据流"""
        self.logger.info("停止订单簿数据流")
        # 实际实现中需要处理WebSocket连接的关闭

    # 实现DataSource抽象方法
    def fetch_data(self, symbol: str, start_time: datetime = None,
                  end_time: datetime = None, **kwargs) -> pd.DataFrame:
        """获取数据的统一接口"""
        return self.get_obi_history(symbol,
                                  minutes=int((end_time - start_time).total_seconds() / 60)
                                  if start_time and end_time else 60)

    def get_symbols(self) -> List[str]:
        """获取支持的交易对列表"""
        return self.symbols

    def get_timeframes(self) -> List[str]:
        """获取支持的时间框架列表"""
        return ['1m', '5m', '15m', '1h']
