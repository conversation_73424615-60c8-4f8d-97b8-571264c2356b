#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader核心组件

实现基于Backtrader的回测引擎核心功能，继承自回测引擎基类。
"""

import backtrader as bt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, Union, Optional, List, Tuple, Type
import tempfile
import os
from datetime import datetime
import logging

from ..base import BacktestEngine, BacktestResults, Strategy
from .results import BacktraderResults

logger = logging.getLogger(__name__)

class BacktraderStrategy(bt.Strategy):
    """
    Backtrader策略基类
    
    包装通用策略接口到Backtrader策略中。
    """
    
    params = (
        ('smc_strategy', None),  # 通用策略对象
        ('pre_signals', None),   # 预生成的信号
    )
    
    def __init__(self):
        """初始化Backtrader策略"""
        self.order = None
        self.buyprice = None
        self.buycomm = None
        self.signals = None
        
        # 🔍 添加交易记录收集
        self.trade_records = []
        self.current_position = 0
        self.entry_price = 0
        self.entry_bar = 0
        
        logger.info("BacktraderStrategy.__init__() 开始")
        
                # 延迟信号生成到start()方法中
        logger.info("BacktraderStrategy初始化完成，信号将在start()中生成")
    
    def start(self):
        """策略开始时调用，此时数据已完全加载"""
        logger.info("BacktraderStrategy.start() 开始")
        
        # 🎯 优先使用预生成的信号
        if self.p.pre_signals is not None:
            logger.info("使用预生成的信号")
            self.signals = self.p.pre_signals
            
            logger.info(f"预生成信号加载完成:")
            logger.info(f"  信号长度: {len(self.signals)}")
            logger.info(f"  信号列: {list(self.signals.columns)}")
            
            if not self.signals.empty:
                total_entries = self.signals['entries'].sum() if 'entries' in self.signals.columns else 0
                logger.info(f"  总信号数: {total_entries}")
                
                # 显示前几个有信号的行
                signal_rows = self.signals[self.signals['entries'] == True]
                if not signal_rows.empty:
                    logger.info(f"  前5个信号位置: {signal_rows.head().index.tolist()}")
                    
                    # 显示信号分布
                    long_signals = self.signals['entries_long'].sum()
                    short_signals = self.signals['entries_short'].sum()
                    logger.info(f"  多头信号: {long_signals}, 空头信号: {short_signals}")
                else:
                    logger.warning("  ⚠️ 没有找到任何True信号！")
            else:
                logger.warning("  ⚠️ 预生成信号DataFrame为空！")
                
            return
        
        # 降级方案：实时生成信号（如果没有预生成信号）
        if self.p.smc_strategy is not None:
            logger.warning("没有预生成信号，尝试实时生成...")
            try:
                data = self.convert_data_to_df()
                logger.info(f"数据转换完成，长度: {len(data)}")
                
                if len(data) == 0:
                    logger.error("转换后的数据为空！")
                    self.signals = None
                    return
                
                self.signals = self.p.smc_strategy.generate_signals(data)
                logger.info(f"实时信号生成完成")
                    
            except Exception as e:
                logger.error(f"实时信号生成失败: {e}")
                import traceback
                traceback.print_exc()
                self.signals = None
        else:
            logger.warning("没有提供策略对象")
             
    def convert_data_to_df(self) -> pd.DataFrame:
        """将Backtrader数据源转换为pandas DataFrame - 调试版本"""
        try:
            # 🔍 详细调试数据转换过程
            logger.info(f"开始数据转换调试:")
            logger.info(f"  self.data类型: {type(self.data)}")
            logger.info(f"  hasattr(self.data, '__len__'): {hasattr(self.data, '__len__')}")
            
            # 获取数据源长度
            try:
                data_len = len(self.data)
                logger.info(f"  数据长度: {data_len}")
            except Exception as len_e:
                logger.error(f"  获取数据长度失败: {len_e}")
                return pd.DataFrame()
            
            if data_len == 0:
                logger.warning("  数据长度为0")
                return pd.DataFrame()
            
            # 🔍 测试数据访问
            try:
                logger.info(f"  测试数据访问:")
                logger.info(f"    self.data.open[0]: {self.data.open[0]}")
                logger.info(f"    self.data.high[0]: {self.data.high[0]}")
                logger.info(f"    self.data.low[0]: {self.data.low[0]}")
                logger.info(f"    self.data.close[0]: {self.data.close[0]}")
                logger.info(f"    self.data.datetime.datetime(0): {self.data.datetime.datetime(0)}")
            except Exception as access_e:
                logger.error(f"  数据访问测试失败: {access_e}")
                return pd.DataFrame()
            
            # 使用简化的数据提取方式
            logger.info(f"  开始提取数据...")
            
            # 只提取前100行进行测试
            test_len = min(data_len, 100)
            
            open_data = []
            high_data = []
            low_data = []
            close_data = []
            dates = []
            
            for i in range(test_len):
                try:
                    open_data.append(self.data.open[i])
                    high_data.append(self.data.high[i])
                    low_data.append(self.data.low[i])
                    close_data.append(self.data.close[i])
                    dates.append(self.data.datetime.datetime(i))
                except Exception as extract_e:
                    logger.error(f"  提取第{i}行数据失败: {extract_e}")
                    break
            
            if len(open_data) == 0:
                logger.error("  没有成功提取任何数据")
                return pd.DataFrame()
            
            # 创建DataFrame
            df = pd.DataFrame({
                'open': open_data,
                'high': high_data,
                'low': low_data,
                'close': close_data,
                'volume': [1.0] * len(open_data)  # 简化volume处理
            })
            
            df.index = pd.DatetimeIndex(dates)
            
            logger.info(f"  数据转换成功: {len(df)} 行")
            logger.info(f"  DataFrame列: {list(df.columns)}")
            logger.info(f"  前3行数据:")
            logger.info(f"    {df.head(3).to_string()}")
            
            return df
            
        except Exception as e:
            logger.error(f"数据转换失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def next(self):
        """
        Backtrader策略核心逻辑
        
        根据通用策略生成的信号执行交易操作
        """
        # 获取当前位置索引
        pos_idx = len(self) - 1
        
        # 🔍 调试：记录next()调用
        if pos_idx < 5:
            logger.info(f"next() 调用[{pos_idx}]: 策略={self.p.smc_strategy is not None}, 信号={self.signals is not None}")
        
        # 如果策略未指定，则跳过
        if self.p.smc_strategy is None:
            if pos_idx < 5:
                logger.warning(f"策略未指定，跳过[{pos_idx}]")
            return
        
        # 如果信号未生成，则跳过
        if self.signals is None:
            if pos_idx < 5:
                logger.warning(f"信号未生成，跳过[{pos_idx}]")
            return
        
        # 如果当前索引超出信号范围，则跳过
        if pos_idx >= len(self.signals):
            if pos_idx < 5:
                logger.warning(f"索引超出信号范围，跳过[{pos_idx}]: {pos_idx} >= {len(self.signals)}")
            return
        
        # 获取当前信号 - 修复信号获取逻辑
        try:
            current_signals = self.signals.iloc[pos_idx]
            
            # 处理多列信号格式 (SMC策略返回多列)
            if 'entries_long' in current_signals and 'entries_short' in current_signals:
                long_signal = current_signals['entries_long']
                short_signal = current_signals['entries_short']
                exit_signal = current_signals.get('exits', False)
            elif 'entries' in current_signals:
                # 简单信号格式
                entry_signal = current_signals['entries']
                long_signal = entry_signal
                short_signal = False
                exit_signal = current_signals.get('exits', False)
            else:
                # 假设第一列是信号
                signal_value = current_signals.iloc[0] if len(current_signals) > 0 else False
                long_signal = signal_value
                short_signal = False
                exit_signal = False
                
        except Exception as e:
            logger.warning(f"信号获取失败: {e}")
            return
            
        # 当前持仓
        current_position = self.getposition(self.data).size
        
        # 根据信号执行交易
        if long_signal and current_position <= 0:  # 多头信号
            if self.order is None:  # 确保没有待处理订单
                self.order = self.buy()
                logger.info(f'多头信号执行: BUY at {self.data.close[0]:.4f}')
        elif short_signal and current_position >= 0:  # 空头信号
            if self.order is None:  # 确保没有待处理订单
                self.order = self.sell()
                logger.info(f'空头信号执行: SELL at {self.data.close[0]:.4f}')
        elif exit_signal and current_position != 0:  # 出场信号
            if self.order is None:  # 确保没有待处理订单
                if current_position > 0:
                    self.order = self.sell()
                    logger.info(f'多头出场: SELL at {self.data.close[0]:.4f}')
                else:
                    self.order = self.buy()
                    logger.info(f'空头出场: BUY at {self.data.close[0]:.4f}')
        
        # 🔍 调试：记录信号处理情况（仅前10次和有信号时）
        if pos_idx < 10 or long_signal or short_signal or exit_signal:
            logger.info(f"信号处理[{pos_idx}]: long={long_signal}, short={short_signal}, exit={exit_signal}, pos={current_position}, order={self.order is not None}")
    
    def notify_order(self, order):
        """订单状态变更通知"""
        if order.status in [order.Submitted, order.Accepted]:
            # 订单提交或接受状态，不做处理
            return
            
        # 检查订单是否完成
        if order.status in [order.Completed]:
            if order.isbuy():
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
                logger.info(f'BUY EXECUTED, Price: {order.executed.price}, Cost: {order.executed.value}, Comm: {order.executed.comm}')
            else:
                logger.info(f'SELL EXECUTED, Price: {order.executed.price}, Cost: {order.executed.value}, Comm: {order.executed.comm}')
                
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            logger.warning(f'Order Canceled/Margin/Rejected: {order.status}')
            
        # 重置订单
        self.order = None
    
    def notify_trade(self, trade):
        """交易完成通知"""
        if not trade.isclosed:
            return
            
        logger.info(f'OPERATION PROFIT, GROSS: {trade.pnl}, NET: {trade.pnlcomm}')
        
        # 🔍 收集交易记录
        try:
            trade_record = {
                'entry_date': bt.num2date(trade.dtopen),
                'exit_date': bt.num2date(trade.dtclose),
                'entry_price': trade.price,
                'exit_price': trade.price,  # 简化处理
                'size': trade.size,
                'pnl': trade.pnl,
                'pnl_pct': (trade.pnl / (trade.price * abs(trade.size))) if trade.size != 0 else 0
            }
            self.trade_records.append(trade_record)
            logger.info(f"记录交易: 盈亏={trade.pnl:.2f}, 总记录数={len(self.trade_records)}")
        except Exception as e:
            logger.warning(f"记录交易失败: {e}")


class BacktraderEngine(BacktestEngine):
    """
    Backtrader回测引擎实现
    
    基于Backtrader库实现的回测引擎，继承自BacktestEngine基类。
    """
    
    def __init__(self, data: pd.DataFrame, **kwargs):
        """
        初始化Backtrader回测引擎
        
        Parameters
        ----------
        data : pd.DataFrame
            回测数据，必须包含OHLCV列和DatetimeIndex
        **kwargs : dict
            额外参数，如初始资金、佣金等
        """
        super().__init__(data, **kwargs)
        self.cerebro = bt.Cerebro()
        
        # 设置初始资金
        initial_cash = kwargs.get('initial_cash', 100000.0)
        self.cerebro.broker.setcash(initial_cash)
        
        # 设置佣金
        commission = kwargs.get('commission', 0.001)
        self.cerebro.broker.setcommission(commission=commission)
        
        # 添加分析器
        self.cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        self.cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        self.cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        self.cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        # 添加数据源
        self._add_data_from_dataframe(data)
    
    def _add_data_from_dataframe(self, data: pd.DataFrame) -> None:
        """
        从DataFrame添加数据源到cerebro
        
        Parameters
        ----------
        data : pd.DataFrame
            包含OHLCV数据的DataFrame，索引必须是DatetimeIndex
        """
        # 确保数据有正确的索引类型
        if not isinstance(data.index, pd.DatetimeIndex):
            raise ValueError("数据必须有DatetimeIndex索引")
            
        # 确保必要的列存在
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
        # 创建Backtrader的数据源
        bt_data = bt.feeds.PandasData(
            dataname=data,
            open='open',
            high='high',
            low='low',
            close='close',
            volume='volume' if 'volume' in data.columns else None,
            openinterest=None
        )
        
        # 添加到cerebro
        self.cerebro.adddata(bt_data)
    
    def run(self, strategy: Strategy, **kwargs) -> BacktestResults:
        """
        运行回测
        
        Parameters
        ----------
        strategy : Strategy
            通用策略对象，将被包装到Backtrader策略中
        **kwargs : dict
            额外的运行参数
            
        Returns
        -------
        BacktestResults
            回测结果对象
        """
        # 🎯 预先生成信号，避免Backtrader数据加载时序问题
        logger.info("预先生成策略信号...")
        try:
            # 检查策略是否有prepare_data方法
            if hasattr(strategy, 'prepare_data'):
                prepared_data = strategy.prepare_data(self.data.copy())
            else:
                prepared_data = self.data.copy()

            signals = strategy.generate_signals(prepared_data)

            logger.info(f"预生成信号完成:")
            logger.info(f"  信号长度: {len(signals)}")
            logger.info(f"  总信号数: {signals['entries'].sum()}")

            # 将信号传递给Backtrader策略
            self.cerebro.addstrategy(BacktraderStrategy, external_strategy=strategy, pre_signals=signals)

        except Exception as e:
            logger.error(f"预生成信号失败: {e}")
            # 降级方案：不使用预生成信号
            self.cerebro.addstrategy(BacktraderStrategy, external_strategy=strategy)
        
        # 运行回测
        results = self.cerebro.run()
        strat = results[0]
        
        # 获取回测结果
        portfolio_value = pd.Series(strat.observers.broker.lines.value.array)
        # 确保索引长度匹配
        if len(portfolio_value) > 0:
            # 创建一个与portfolio_value长度匹配的新索引
            from_idx = 0
            to_idx = from_idx + len(portfolio_value)
            # 如果数据长度不足，调整结束索引
            if to_idx > len(self.data.index):
                to_idx = len(self.data.index)
                # 可能需要截断portfolio_value
                portfolio_value = portfolio_value[:to_idx-from_idx]
            
            portfolio_value.index = self.data.index[from_idx:to_idx]
        
        # 获取交易记录 - 修复交易记录提取
        trades_list = []
        try:
            # 🔍 从策略对象直接获取交易记录
            if hasattr(strat, 'trade_records') and strat.trade_records:
                logger.info(f"从策略对象获取到 {len(strat.trade_records)} 笔交易记录")
                trades_list = strat.trade_records
            else:
                # 降级方案：从分析器获取
                trade_analysis = strat.analyzers.trades.get_analysis()
                logger.info(f"交易分析器结果: {trade_analysis}")
                
                # Backtrader的交易分析器结构
                total_trades = trade_analysis.get('total', {}).get('total', 0)
                logger.info(f"分析器显示总交易数: {total_trades}")
                
                if total_trades > 0:
                    # 创建简化的交易记录
                    won_trades = trade_analysis.get('won', {}).get('total', 0)
                    lost_trades = trade_analysis.get('lost', {}).get('total', 0)
                    total_pnl = trade_analysis.get('pnl', {}).get('net', {}).get('total', 0)
                    
                    # 创建汇总交易记录
                    trades_list = [{
                        'entry_date': self.data.index[0],
                        'exit_date': self.data.index[-1],
                        'entry_price': 0,
                        'exit_price': 0,
                        'size': 1,
                        'pnl': total_pnl,
                        'pnl_pct': 0,
                        'total_trades': total_trades,
                        'won_trades': won_trades,
                        'lost_trades': lost_trades
                    }]
                        
        except Exception as e:
            logger.warning(f"获取交易记录失败: {e}")
            # 创建空的交易记录
        
        trades_df = pd.DataFrame(trades_list) if trades_list else pd.DataFrame()
        
        # 计算性能指标 - 🔧 修复指标计算错误
        try:
            sharpe = strat.analyzers.sharpe.get_analysis().get('sharperatio', 0)
            if sharpe is None or np.isnan(sharpe) or np.isinf(sharpe):
                sharpe = 0.0
        except:
            sharpe = 0.0
            
        try:
            drawdown = strat.analyzers.drawdown.get_analysis()
            max_drawdown = drawdown.get('max', {}).get('drawdown', 0)
            if max_drawdown is None or np.isnan(max_drawdown) or np.isinf(max_drawdown):
                max_drawdown = 0.0
            # 转换为百分比
            max_drawdown = abs(max_drawdown) / 100.0 if max_drawdown != 0 else 0.0
        except:
            max_drawdown = 0.0
            
        try:
            returns = strat.analyzers.returns.get_analysis()
            total_return = returns.get('rtot', 0)
            annual_return = returns.get('rnorm', 0)
            
            # 处理异常值
            if total_return is None or np.isnan(total_return) or np.isinf(total_return):
                total_return = 0.0
            if annual_return is None or np.isnan(annual_return) or np.isinf(annual_return):
                annual_return = 0.0
        except:
            total_return = 0.0
            annual_return = 0.0
        
        # 🔧 改进胜率计算
        win_rate = 0.0
        total_trades_count = 0
        
        if not trades_df.empty:
            total_trades_count = len(trades_df)
            if 'pnl' in trades_df.columns:
                wins = len(trades_df[trades_df['pnl'] > 0])
                win_rate = wins / total_trades_count if total_trades_count > 0 else 0.0
            elif 'won_trades' in trades_df.columns and 'total_trades' in trades_df.columns:
                # 从汇总数据计算
                won_trades = trades_df['won_trades'].iloc[0] if len(trades_df) > 0 else 0
                total_trades_count = trades_df['total_trades'].iloc[0] if len(trades_df) > 0 else 0
                win_rate = won_trades / total_trades_count if total_trades_count > 0 else 0.0
        
        # 🔧 计算波动率（如果有足够数据）
        volatility = 0.0
        if len(portfolio_value) > 1:
            returns_series = portfolio_value.pct_change().dropna()
            if len(returns_series) > 0:
                volatility = returns_series.std() * np.sqrt(252)  # 年化波动率
                if np.isnan(volatility) or np.isinf(volatility):
                    volatility = 0.0
        
        metrics = {
            'sharpe_ratio': float(sharpe),
            'max_drawdown': float(max_drawdown),
            'total_return': float(total_return),
            'annual_return': float(annual_return),
            'win_rate': float(win_rate),
            'total_trades': int(total_trades_count),
            'volatility': float(volatility)
        }
        
        logger.info(f"计算的性能指标: {metrics}")
        
        # 获取持仓数据（此处简化处理）
        positions = pd.DataFrame(index=self.data.index)
        positions['size'] = 0
        
        # 创建回测结果对象
        self.results = BacktraderResults(
            equity=portfolio_value,
            positions=positions,
            trades=trades_df,
            metrics=metrics,
            returns=portfolio_value.pct_change(),
            strategy=strategy.__class__.__name__,
            params=strategy.params
        )
        
        return self.results
    
    def plot(self, **kwargs) -> None:
        """
        可视化回测结果
        
        Parameters
        ----------
        **kwargs : dict
            可视化参数
            - figsize : tuple, 图表尺寸
            - plot_volume : bool, 是否绘制成交量
            - plot_equity : bool, 是否绘制权益曲线
        """
        if self.results is None:
            logger.warning("没有回测结果可供绘制，请先运行回测")
            return
            
        figsize = kwargs.get('figsize', (12, 8))
        plot_volume = kwargs.get('plot_volume', True)
        plot_equity = kwargs.get('plot_equity', True)
        
        plt.figure(figsize=figsize)
        
        # 绘制价格图表
        plt.subplot(2, 1, 1)
        plt.plot(self.data.index, self.data['close'], label='收盘价')
        
        # 标记交易点
        if not self.results.trades.empty:
            buy_points = self.results.trades[self.results.trades['size'] > 0]
            sell_points = self.results.trades[self.results.trades['size'] < 0]
            
            if not buy_points.empty:
                plt.scatter(buy_points['entry_date'], buy_points['entry_price'], 
                           marker='^', color='green', s=100, label='买入')
            
            if not sell_points.empty:
                plt.scatter(sell_points['exit_date'], sell_points['exit_price'], 
                           marker='v', color='red', s=100, label='卖出')
        
        plt.title('价格和交易点')
        plt.legend()
        plt.grid(True)
        
        # 绘制权益曲线
        if plot_equity:
            plt.subplot(2, 1, 2)
            plt.plot(self.results.equity.index, self.results.equity, label='账户净值')
            plt.title('权益曲线')
            plt.legend()
            plt.grid(True)
        
        plt.tight_layout()
        plt.show() 