#!/usr/bin/env python3
"""
自动更新多机器人资金分配脚本
功能：根据交易所钱包余额动态计算并更新ADL(70%)和MPDO(30%)策略的available_capital
作者：AI Assistant
"""

import json
import ccxt
import os
from typing import Dict, Tuple

# 配置参数
ADL_CONFIG_FILE = "config_adl_production.json"
MPDO_CONFIG_FILE = "config_mpdo_production.json"
ADL_ALLOCATION_RATIO = 0.70  # ADL策略分配70%
MPDO_ALLOCATION_RATIO = 0.30  # MPDO策略分配30%

def load_config(config_file: str) -> Dict:
    """加载配置文件"""
    with open(config_file, 'r') as f:
        return json.load(f)

def save_config(config_file: str, config: Dict) -> None:
    """保存配置文件"""
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

def get_wallet_balance(exchange_config: Dict) -> float:
    """获取交易所USDT余额"""
    exchange_class = getattr(ccxt, exchange_config['name'].lower())
    exchange = exchange_class({
        'apiKey': exchange_config['key'],
        'secret': exchange_config['secret'],
        'password': exchange_config.get('password', ''),
        'sandbox': False,  # 生产环境
        'enableRateLimit': True,
    })
    
    balance = exchange.fetch_balance()
    usdt_balance = balance['USDT']['total']
    return usdt_balance

def calculate_allocations(total_balance: float) -> Tuple[float, float]:
    """计算分配金额"""
    adl_amount = total_balance * ADL_ALLOCATION_RATIO
    mpdo_amount = total_balance * MPDO_ALLOCATION_RATIO
    return adl_amount, mpdo_amount

def update_config_allocation(config_file: str, amount: float) -> None:
    """更新配置文件中的available_capital"""
    config = load_config(config_file)
    config['available_capital'] = round(amount, 2)
    save_config(config_file, config)
    print(f"✅ 已更新 {config_file}: available_capital = {amount:.2f} USDT")

def main():
    try:
        # 加载ADL配置获取交易所信息
        adl_config = load_config(ADL_CONFIG_FILE)
        
        # 获取钱包余额
        total_balance = get_wallet_balance(adl_config['exchange'])
        print(f"📊 当前钱包总余额: {total_balance:.2f} USDT")
        
        # 计算分配
        adl_amount, mpdo_amount = calculate_allocations(total_balance)
        
        print(f"💰 计算分配:")
        print(f"   ADL策略 (70%): {adl_amount:.2f} USDT")
        print(f"   MPDO策略 (30%): {mpdo_amount:.2f} USDT")
        
        # 更新配置文件
        update_config_allocation(ADL_CONFIG_FILE, adl_amount)
        update_config_allocation(MPDO_CONFIG_FILE, mpdo_amount)
        
        print("\n🎉 资金分配更新完成！请重启机器人以应用新配置。")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 