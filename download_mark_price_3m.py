#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
下载币安期货3分钟标记价格数据

使用币安API的markPriceKlines接口下载标记价格数据，
保存为freqtrade兼容的JSON格式
"""

import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import time
import os
from pathlib import Path

# 交易对配置
PAIRS = [
    'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 
    'BNBUSDT', 'XRPUSDT', 'ADAUSDT'
]

# 时间配置
START_DATE = '2024-12-01'
END_DATE = '2025-06-28'
INTERVAL = '3m'

# 输出目录
OUTPUT_DIR = 'user_data/data/binance/futures'

def timestamp_to_ms(date_str):
    """将日期字符串转换为毫秒级时间戳"""
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    return int(dt.timestamp() * 1000)

def download_mark_price_data(symbol, start_ms, end_ms, interval='3m', limit=1500):
    """
    下载标记价格数据
    
    Args:
        symbol: 交易对符号，如 'BTCUSDT'
        start_ms: 开始时间戳（毫秒）
        end_ms: 结束时间戳（毫秒）
        interval: K线间隔
        limit: 每次请求的数据量限制
    
    Returns:
        list: 标记价格数据列表
    """
    url = 'https://fapi.binance.com/fapi/v1/markPriceKlines'
    all_data = []
    current_start = start_ms
    
    print(f"开始下载 {symbol} 标记价格数据...")
    
    while current_start < end_ms:
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': current_start,
            'endTime': end_ms,
            'limit': limit
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if not data:
                print(f"  无更多数据，结束下载")
                break
                
            all_data.extend(data)
            
            # 更新下次请求的开始时间
            last_timestamp = data[-1][0]
            current_start = last_timestamp + 1
            
            print(f"  已获取 {len(data)} 条记录，总计 {len(all_data)} 条")
            
            # 避免API限制
            time.sleep(0.2)
            
        except requests.exceptions.RequestException as e:
            print(f"  请求错误: {e}")
            time.sleep(5)
            continue
        except Exception as e:
            print(f"  数据处理错误: {e}")
            break
    
    print(f"  {symbol} 下载完成，共获取 {len(all_data)} 条记录")
    return all_data

def convert_to_freqtrade_format(raw_data):
    """
    将币安标记价格数据转换为freqtrade兼容格式
    
    Args:
        raw_data: 币安API返回的原始数据
    
    Returns:
        list: freqtrade格式的数据
    """
    freqtrade_data = []
    
    for item in raw_data:
        # 币安标记价格K线数据格式:
        # [timestamp, open, high, low, close, volume, close_time, quote_volume, count, taker_buy_volume, taker_buy_quote_volume, ignore]
        freqtrade_item = [
            item[0],      # timestamp
            float(item[1]),  # open
            float(item[2]),  # high  
            float(item[3]),  # low
            float(item[4]),  # close
            float(item[5])   # volume
        ]
        freqtrade_data.append(freqtrade_item)
    
    return freqtrade_data

def save_data(symbol, data, output_dir):
    """
    保存数据到文件
    
    Args:
        symbol: 交易对符号
        data: 数据列表
        output_dir: 输出目录
    """
    # 转换符号格式：BTCUSDT -> BTC_USDT_USDT-3m-mark.json
    if symbol.endswith('USDT'):
        base = symbol[:-4]
        filename = f"{base}_USDT_USDT-3m-mark.json"
    else:
        filename = f"{symbol}-3m-mark.json"
    
    filepath = Path(output_dir) / filename
    
    # 确保目录存在
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存数据
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, separators=(',', ':'))
    
    print(f"数据已保存到: {filepath}")

def main():
    """主函数"""
    print("=== 币安期货3分钟标记价格数据下载器 ===")
    print(f"时间范围: {START_DATE} 到 {END_DATE}")
    print(f"交易对: {', '.join(PAIRS)}")
    print(f"输出目录: {OUTPUT_DIR}")
    print()
    
    start_ms = timestamp_to_ms(START_DATE)
    end_ms = timestamp_to_ms(END_DATE)
    
    for symbol in PAIRS:
        print(f"\n处理交易对: {symbol}")
        
        try:
            # 下载数据
            raw_data = download_mark_price_data(symbol, start_ms, end_ms, INTERVAL)
            
            if not raw_data:
                print(f"  警告: {symbol} 未获取到数据")
                continue
            
            # 转换格式
            freqtrade_data = convert_to_freqtrade_format(raw_data)
            
            # 保存数据
            save_data(symbol, freqtrade_data, OUTPUT_DIR)
            
        except Exception as e:
            print(f"  错误: 处理 {symbol} 时发生异常: {e}")
            continue
    
    print("\n=== 所有数据下载完成 ===")

if __name__ == "__main__":
    main() 