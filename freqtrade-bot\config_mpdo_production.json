{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "available_capital": 41.0, "dry_run": false, "cancel_open_orders_on_exit": true, "trading_mode": "futures", "margin_mode": "isolated", "max_leverage": 10.0, "liquidation_buffer": 0.05, "timeframe": "3m", "mpdo_strategy": {"lookback_period": 100, "deviation_threshold": 1.8, "volume_threshold": 1.6, "atr_period": 14, "time_stop_minutes": 15, "quick_loss_threshold": -0.015, "enable_price_acceleration": true, "enable_volume_burst": false, "enable_trend_confirmation": true, "minimal_roi": {"0": 0.02, "5": 0.015, "10": 0.01, "15": 0.005, "20": 0.0}, "stoploss": -0.05, "trailing_stop": false, "use_custom_stoploss": true}, "strategy": "MPDOStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 60, "exit": 60, "unit": "seconds", "exit_timeout_count": 0}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "order_types": {"entry": "limit", "exit": "limit", "emergency_exit": "limit", "force_entry": "market", "force_exit": "market", "stoploss": "limit", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "exchange": {"name": "binance", "key": "eTqhJbBEFcQ3Wh1mrgoc2rAwVxTZn7KHtdC2WZ0zuTgDo9VTZd7RmgQdAaOaBLXA", "secret": "sdENnLxeQ3Jn4INGMSu92yRot3aI5FbjnqPmbfRURKe82n1z2f20S5zrP5EmJ2Wd", "password": "", "sandbox": false, "ccxt_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "options": {"defaultType": "future", "recvWindow": 10000}, "proxies": {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "proxies": {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT", "SOL/USDT:USDT", "AVAX/USDT:USDT", "LINK/USDT:USDT", "NEAR/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8082, "verbosity": "info", "enable_openapi": false, "jwt_secret_key": "b4f8a3e2c1d6f9e8a7b2c5d8e1f4a7b0c3e6f9a2b5c8d1e4f7a0b3c6d9e2f5a8", "ws_token": "MPDOStrat-XmWHbYK1l1veeNPL7Dg4A_30E4dKts", "CORS_origins": ["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"], "username": "MPDO_Trader_c96fc782", "password": "Z19E68EogLIjoZbN"}, "bot_name": "MPDO-Production-Live", "db_url": "sqlite:///tradesv3_mpdo.sqlite", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 3}, "dataformat_ohlcv": "json", "dataformat_trades": "json", "position_adjustment_enable": false, "max_entry_position_adjustment": 0}