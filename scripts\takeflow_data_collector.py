#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TakeFlow数据收集器

基于TakeFlow.md文档第3.1节要求，实现独立的数据管道脚本。
定时获取币安期货API数据：指数价格、Taker买卖量、资金费率。
支持历史数据下载和实时数据收集。
"""

import os
import sys
import time
import json
import logging
import requests
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('TakeFlowCollector')


@dataclass
class TakeFlowData:
    """TakeFlow数据结构"""
    timestamp: int
    symbol: str
    last_price: float
    index_price: float
    mark_price: float
    basis: float
    taker_buy_vol: float
    taker_sell_vol: float
    taker_ratio: float
    funding_rate: float
    next_funding_time: int


class BinanceAPIClient:
    """币安API客户端"""
    
    def __init__(self, base_url: str = "https://fapi.binance.com"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'TakeFlow-DataCollector/1.0'
        })
        
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败 {endpoint}: {e}")
            return None
    
    def get_premium_index(self, symbol: str = None) -> Optional[List[Dict]]:
        """
        获取指数价格和标记价格
        
        Args:
            symbol: 交易对符号，如果为None则获取所有
            
        Returns:
            指数价格数据列表
        """
        params = {}
        if symbol:
            params['symbol'] = symbol
            
        data = self._make_request('/fapi/v1/premiumIndex', params)
        return data if isinstance(data, list) else [data] if data else None
    
    def get_taker_long_short_ratio(self, symbol: str, period: str = '5m', limit: int = 500) -> Optional[List[Dict]]:
        """
        获取Taker买卖量比率

        根据币安API文档限制：
        - 最大limit: 500条记录
        - 历史数据: 仅支持最近30天
        - 默认使用最大limit获取更多历史数据

        Args:
            symbol: 交易对符号
            period: 时间周期，默认5分钟
            limit: 获取数量限制，最大500

        Returns:
            Taker买卖量数据列表
        """
        params = {
            'symbol': symbol,
            'period': period,
            'limit': min(limit, 500)  # 确保不超过API限制
        }

        return self._make_request('/futures/data/takerlongshortRatio', params)
    
    def get_funding_rate(self, symbol: str = None, limit: int = 1) -> Optional[List[Dict]]:
        """
        获取资金费率
        
        Args:
            symbol: 交易对符号，如果为None则获取所有
            limit: 获取数量限制
            
        Returns:
            资金费率数据列表
        """
        params = {'limit': limit}
        if symbol:
            params['symbol'] = symbol
            
        return self._make_request('/fapi/v1/fundingRate', params)
    
    def get_ticker_price(self, symbol: str = None) -> Optional[List[Dict]]:
        """
        获取最新价格
        
        Args:
            symbol: 交易对符号，如果为None则获取所有
            
        Returns:
            价格数据列表
        """
        params = {}
        if symbol:
            params['symbol'] = symbol
            
        data = self._make_request('/fapi/v1/ticker/price', params)
        return data if isinstance(data, list) else [data] if data else None


class TakeFlowDataStorage:
    """TakeFlow数据存储管理器"""
    
    def __init__(self, storage_dir: str = "user_data/data/takeflow"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化SQLite数据库
        self.db_path = self.storage_dir / "takeflow_data.db"
        self._init_database()
        
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS takeflow_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp INTEGER NOT NULL,
                    symbol TEXT NOT NULL,
                    last_price REAL,
                    index_price REAL,
                    mark_price REAL,
                    basis REAL,
                    taker_buy_vol REAL,
                    taker_sell_vol REAL,
                    taker_ratio REAL,
                    funding_rate REAL,
                    next_funding_time INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(timestamp, symbol)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_symbol_timestamp ON takeflow_data(symbol, timestamp)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON takeflow_data(timestamp)')
    
    def save_data(self, data_list: List[TakeFlowData]) -> bool:
        """
        保存数据到数据库
        
        Args:
            data_list: TakeFlow数据列表
            
        Returns:
            是否保存成功
        """
        if not data_list:
            return False
            
        try:
            with sqlite3.connect(self.db_path) as conn:
                for data in data_list:
                    conn.execute('''
                        INSERT OR REPLACE INTO takeflow_data 
                        (timestamp, symbol, last_price, index_price, mark_price, basis,
                         taker_buy_vol, taker_sell_vol, taker_ratio, funding_rate, next_funding_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        data.timestamp, data.symbol, data.last_price, data.index_price,
                        data.mark_price, data.basis, data.taker_buy_vol, data.taker_sell_vol,
                        data.taker_ratio, data.funding_rate, data.next_funding_time
                    ))
                conn.commit()
                
            logger.info(f"成功保存 {len(data_list)} 条TakeFlow数据")
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return False
    
    def export_to_csv(self, symbol: str, start_time: datetime = None, end_time: datetime = None) -> str:
        """
        导出数据到CSV文件
        
        Args:
            symbol: 交易对符号
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            CSV文件路径
        """
        query = "SELECT * FROM takeflow_data WHERE symbol = ?"
        params = [symbol]
        
        if start_time:
            query += " AND timestamp >= ?"
            params.append(int(start_time.timestamp() * 1000))
            
        if end_time:
            query += " AND timestamp <= ?"
            params.append(int(end_time.timestamp() * 1000))
            
        query += " ORDER BY timestamp"
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                df = pd.read_sql_query(query, conn, params=params)
                
            if df.empty:
                logger.warning(f"没有找到 {symbol} 的数据")
                return ""
                
            # 转换时间戳为可读格式
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # 导出CSV
            csv_path = self.storage_dir / f"{symbol}_takeflow_data.csv"
            df.to_csv(csv_path, index=False)
            
            logger.info(f"导出 {len(df)} 条数据到 {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return ""
    
    def get_latest_timestamp(self, symbol: str) -> Optional[int]:
        """获取指定交易对的最新时间戳"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT MAX(timestamp) FROM takeflow_data WHERE symbol = ?",
                    (symbol,)
                )
                result = cursor.fetchone()
                return result[0] if result[0] else None
        except Exception as e:
            logger.error(f"获取最新时间戳失败: {e}")
            return None


class TakeFlowDataCollector:
    """TakeFlow数据收集器主类"""
    
    def __init__(self, 
                 symbols: List[str] = None,
                 storage_dir: str = "user_data/data/takeflow",
                 collection_interval: int = 300):  # 5分钟
        """
        初始化数据收集器
        
        Args:
            symbols: 要收集的交易对列表
            storage_dir: 数据存储目录
            collection_interval: 收集间隔（秒）
        """
        # 扩展到10个主要币种，基于市值和流动性选择
        self.symbols = symbols or [
            'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT',
            'BNBUSDT', 'XRPUSDT', 'MATICUSDT', 'LINKUSDT', 'AVAXUSDT'
        ]
        self.collection_interval = collection_interval
        
        self.api_client = BinanceAPIClient()
        self.storage = TakeFlowDataStorage(storage_dir)
        
        logger.info(f"初始化TakeFlow数据收集器，监控交易对: {self.symbols}")
    
    def collect_single_snapshot(self) -> List[TakeFlowData]:
        """收集单次数据快照"""
        current_time = int(datetime.now().timestamp() * 1000)
        collected_data = []
        
        for symbol in self.symbols:
            try:
                # 获取指数价格和标记价格
                premium_data = self.api_client.get_premium_index(symbol)
                if not premium_data:
                    logger.warning(f"未获取到 {symbol} 的指数价格数据")
                    continue
                    
                premium_info = premium_data[0] if isinstance(premium_data, list) else premium_data
                
                # 获取最新价格
                price_data = self.api_client.get_ticker_price(symbol)
                if not price_data:
                    logger.warning(f"未获取到 {symbol} 的最新价格数据")
                    continue
                    
                price_info = price_data[0] if isinstance(price_data, list) else price_data
                
                # 获取Taker买卖量数据
                taker_data = self.api_client.get_taker_long_short_ratio(symbol)
                if not taker_data:
                    logger.warning(f"未获取到 {symbol} 的Taker数据")
                    continue
                    
                taker_info = taker_data[0]
                
                # 获取资金费率
                funding_data = self.api_client.get_funding_rate(symbol)
                funding_info = funding_data[0] if funding_data else {}
                
                # 计算基差
                last_price = float(price_info['price'])
                index_price = float(premium_info['indexPrice'])
                basis = last_price - index_price
                
                # 构建TakeFlow数据
                takeflow_data = TakeFlowData(
                    timestamp=current_time,
                    symbol=symbol,
                    last_price=last_price,
                    index_price=index_price,
                    mark_price=float(premium_info['markPrice']),
                    basis=basis,
                    taker_buy_vol=float(taker_info['buyVol']),
                    taker_sell_vol=float(taker_info['sellVol']),
                    taker_ratio=float(taker_info['buySellRatio']),
                    funding_rate=float(funding_info.get('fundingRate', 0)),
                    next_funding_time=int(funding_info.get('fundingTime', 0))
                )
                
                collected_data.append(takeflow_data)
                logger.debug(f"收集 {symbol} 数据: 基差={basis:.4f}, Taker比率={takeflow_data.taker_ratio:.4f}")
                
            except Exception as e:
                logger.error(f"收集 {symbol} 数据失败: {e}")
                continue
        
        return collected_data
    
    def run_continuous_collection(self):
        """运行连续数据收集"""
        logger.info(f"开始连续数据收集，间隔 {self.collection_interval} 秒")
        
        while True:
            try:
                start_time = time.time()
                
                # 收集数据
                data_list = self.collect_single_snapshot()
                
                if data_list:
                    # 保存数据
                    self.storage.save_data(data_list)
                    logger.info(f"成功收集并保存 {len(data_list)} 个交易对的数据")
                else:
                    logger.warning("本次收集未获取到任何数据")
                
                # 计算等待时间
                elapsed_time = time.time() - start_time
                sleep_time = max(0, self.collection_interval - elapsed_time)
                
                if sleep_time > 0:
                    logger.debug(f"等待 {sleep_time:.1f} 秒后进行下次收集")
                    time.sleep(sleep_time)
                    
            except KeyboardInterrupt:
                logger.info("收到停止信号，退出数据收集")
                break
            except Exception as e:
                logger.error(f"数据收集过程中出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='TakeFlow数据收集器')
    parser.add_argument('--symbols', nargs='+',
                       default=['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT',
                               'BNBUSDT', 'XRPUSDT', 'MATICUSDT', 'LINKUSDT', 'AVAXUSDT'],
                       help='要收集的交易对列表（默认10个主要币种）')
    parser.add_argument('--storage-dir', default='user_data/data/takeflow',
                       help='数据存储目录')
    parser.add_argument('--interval', type=int, default=300,
                       help='收集间隔（秒），默认300秒（5分钟）')
    parser.add_argument('--test', action='store_true',
                       help='运行测试模式，只收集一次数据')
    parser.add_argument('--export', type=str,
                       help='导出指定交易对的数据到CSV')
    
    args = parser.parse_args()
    
    # 创建收集器
    collector = TakeFlowDataCollector(
        symbols=args.symbols,
        storage_dir=args.storage_dir,
        collection_interval=args.interval
    )
    
    if args.export:
        # 导出模式
        csv_path = collector.storage.export_to_csv(args.export)
        if csv_path:
            print(f"数据已导出到: {csv_path}")
        else:
            print(f"导出失败或没有数据: {args.export}")
    elif args.test:
        # 测试模式
        logger.info("运行测试模式...")
        data_list = collector.collect_single_snapshot()
        if data_list:
            collector.storage.save_data(data_list)
            print(f"测试成功，收集到 {len(data_list)} 个交易对的数据")
            for data in data_list:
                print(f"{data.symbol}: 基差={data.basis:.4f}, Taker比率={data.taker_ratio:.4f}")
        else:
            print("测试失败，未收集到数据")
    else:
        # 连续收集模式
        collector.run_continuous_collection()


if __name__ == "__main__":
    main()
