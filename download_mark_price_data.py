# scripts/download_mark_price_data.py
"""
标记价格数据预下载脚本

用于下载币安标记价格历史数据，供MPDO策略回测使用
"""

import requests
import pandas as pd
import os
from datetime import datetime, timedelta
import time

def download_mark_price_klines(symbol: str, interval: str = '3m', days: int = 30):
    """下载指定天数的标记价格K线数据"""
    
    url = "https://fapi.binance.com/fapi/v1/markPriceKlines"
    
    end_time = int(datetime.now().timestamp() * 1000)
    start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
    
    all_data = []
    
    while start_time < end_time:
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': start_time,
            'endTime': min(start_time + 1000 * 1000 * 60 * 3, end_time),  # 每次最多1000根K线
            'limit': 1000
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                all_data.extend(data)
                print(f"下载 {symbol} 数据: {len(data)} 根K线")
                
                if len(data) < 1000:
                    break
                    
                start_time = data[-1][0] + 1  # 下一个时间点
                time.sleep(0.1)  # 避免触发限制
            else:
                print(f"API错误: {response.status_code}")
                break
                
        except Exception as e:
            print(f"下载失败: {e}")
            break
    
    if all_data:
        # 转换为DataFrame并保存
        df = pd.DataFrame(all_data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'count', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df = df.astype({
            'open': float, 'high': float, 'low': float, 'close': float,
            'volume': float
        })
        
        # 保存文件
        filename = f"mark_price_{symbol}_{interval}_{days}days.csv"
        df.to_csv(filename, index=False)
        print(f"保存文件: {filename}, 共 {len(df)} 根K线")
        
        return df
    
    return None

if __name__ == "__main__":
    symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT']
    
    for symbol in symbols:
        print(f"\n开始下载 {symbol} 标记价格数据...")
        download_mark_price_klines(symbol, '3m', 30)
        time.sleep(1)  # 避免触发限制