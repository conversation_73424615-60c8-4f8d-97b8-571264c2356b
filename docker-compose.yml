# FreqTrade量化交易系统Docker Compose配置
# 功能：提供完整的多策略交易系统容器化部署
# 包含：ADL策略、MPDO策略、Web界面、数据持久化

version: '3.8'

services:
  # ADL策略交易服务
  freqtrade-adl:
    build:
      context: .
      dockerfile: ${DOCKERFILE:-Dockerfile}  # 可通过环境变量选择Dockerfile
    container_name: freqtrade-adl
    restart: unless-stopped
    command: freqtrade trade --config /app/freqtrade-bot/config_adl_production.json --strategy ADLAnticipationStrategy
    volumes:
      - ./freqtrade-bot/user_data:/app/freqtrade-bot/user_data
      - ./storage:/app/storage
      - ./logs:/app/logs
      - ./freqtrade-bot/tradesv3.production.sqlite:/app/freqtrade-bot/tradesv3.production.sqlite
    environment:
      - PYTHONPATH=/app
      - FREQTRADE_CONFIG_PATH=/app/freqtrade-bot
    networks:
      - freqtrade-network
    depends_on:
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # MPDO策略交易服务
  freqtrade-mpdo:
    build:
      context: .
      dockerfile: ${DOCKERFILE:-Dockerfile}
    container_name: freqtrade-mpdo
    restart: unless-stopped
    command: freqtrade trade --config /app/freqtrade-bot/config_mpdo_production.json --strategy MPDOStrategy
    volumes:
      - ./freqtrade-bot/user_data:/app/freqtrade-bot/user_data
      - ./storage:/app/storage
      - ./logs:/app/logs
      - ./freqtrade-bot/tradesv3.production.sqlite:/app/freqtrade-bot/tradesv3.production.sqlite
    environment:
      - PYTHONPATH=/app
      - FREQTRADE_CONFIG_PATH=/app/freqtrade-bot
    networks:
      - freqtrade-network
    depends_on:
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # FreqUI Web界面服务
  freqtrade-ui:
    build:
      context: .
      dockerfile: ${DOCKERFILE:-Dockerfile}
    container_name: freqtrade-ui
    restart: unless-stopped
    command: freqtrade webserver --config /app/freqtrade-bot/config_frequi_webserver.json
    ports:
      - "8080:8080"
      - "8081:8081"
    volumes:
      - ./freqtrade-bot/user_data:/app/freqtrade-bot/user_data
      - ./storage:/app/storage
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - FREQTRADE_CONFIG_PATH=/app/freqtrade-bot
    networks:
      - freqtrade-network
    depends_on:
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis缓存服务（用于策略间数据共享）
  redis:
    image: redis:7-alpine
    container_name: freqtrade-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - freqtrade-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 数据收集服务（可选）
  data-collector:
    build:
      context: .
      dockerfile: ${DOCKERFILE:-Dockerfile}
    container_name: freqtrade-data-collector
    restart: unless-stopped
    command: python /app/scripts/collect_cvd_data.py
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    networks:
      - freqtrade-network
    profiles:
      - data-collection
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  redis_data:

networks:
  freqtrade-network:
    driver: bridge
