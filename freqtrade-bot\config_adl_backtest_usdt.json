{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.95, "dry_run": true, "dry_run_wallet": 100000, "cancel_open_orders_on_exit": true, "trading_mode": "futures", "margin_mode": "isolated", "max_leverage": 4.0, "adl_strategy_params": {"atr_period": 14, "atr_sma_period": 50, "atr_multiplier": 1.8, "zscore_period": 50, "zscore_threshold": 1.85, "atr_stop_multiplier": 0.42, "atr_profit_multiplier": 0.55, "volume_confirmation": {"volume_sma_period": 20, "volume_multiplier": 1.0, "enable_volume_filter": true}, "basic_thresholds": {"strong_signal_threshold": 0.4, "high_quality_signal_threshold": 1.0, "signal_strength_decline_threshold": 0.5, "default_fillna_value": 1.0}, "profit_loss_thresholds": {"quick_loss_threshold": -0.06, "profit_protection_threshold": 0.002}, "step3_optimization": {"enable": true, "signal_tier_system": {"tier1_strength_min": 1.2, "tier2_strength_min": 0.8, "tier3_strength_min": 0.5, "tier1_max_trades": 3, "tier2_max_trades": 3, "tier3_max_trades": 4}, "aggressive_time_management": {"enable": true, "tier1_time_limits": [35, 40, 45, 50, 60], "tier2_time_limits": [30, 35, 40, 45, 55], "tier3_time_limits": [27, 32, 37, 42, 50], "profit_protection_time": 8, "profit_protection_threshold": 1.0, "profit_protection_time_limit": 25, "quick_loss_threshold": -2.5, "quick_loss_time_limit": 10}, "momentum_confirmation": {"enable": true, "price_acceleration_periods": 3, "min_acceleration_ratio": 0.9, "volume_burst_multiplier": 0.9, "rsi_oversold_threshold": 50, "rsi_overbought_threshold": 50}, "profit_protection": {"enable": true, "min_profit_for_protection": 0.003, "protected_time_limit": 8, "quick_exit_loss_threshold": -0.008}}, "minimal_roi": {"0": 0.038, "2": 0.018, "4": 0.009, "6": 0}, "maker_only": true, "use_custom_stoploss": false, "leverage_revolution": {"enable": true, "base_leverage": 3.0, "tier1_leverage": 2.8, "tier2_leverage": 3.8, "tier3_leverage": 3.5, "volatility_adjustment": true, "min_leverage": 2, "risk_scaling": true}, "strategy_config": {"timeframe": "5m", "startup_candle_count": 60, "stoploss": -0.095, "use_custom_stoploss": false}, "technical_indicators": {"rsi_period": 14, "ema_short_period": 20, "sma_short_period": 20, "sma_long_period": 50, "clip_min_value": 0, "clip_max_value": 2, "division_protection": 1e-08}, "time_management": {"quick_loss_time_seconds": 480, "profit_protection_default_time": 25, "rsi_threshold_adjustment": 10, "trade_duration_check_minutes": 8}}, "liquidation_buffer": 0.05, "timeframe": "5m", "strategy": "ADLAnticipationStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 60, "exit": 60, "exit_timeout_count": 0, "unit": "seconds"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "password": "", "sandbox": false, "ccxt_config": {"enableRateLimit": true, "rateLimit": 200, "options": {"defaultType": "future"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200}, "pair_whitelist": ["1000BONK/USDT:USDT", "1000PEPE/USDT:USDT", "1000SHIB/USDT:USDT", "AAVE/USDT:USDT", "ADA/USDT:USDT", "ARB/USDT:USDT", "ATOM/USDT:USDT", "AVAX/USDT:USDT", "BANANAS31/USDT:USDT", "BCH/USDT:USDT", "BNB/USDT:USDT", "BR/USDT:USDT", "BTC/USDT:USDT", "CRV/USDT:USDT", "DOGE/USDT:USDT", "DOT/USDT:USDT", "ENA/USDT:USDT", "ETH/USDT:USDT", "ETHFI/USDT:USDT", "FARTCOIN/USDT:USDT", "FIL/USDT:USDT", "GMX/USDT:USDT", "HYPER/USDT:USDT", "LINK/USDT:USDT", "LTC/USDT:USDT", "MAGIC/USDT:USDT", "MOODENG/USDT:USDT", "NEAR/USDT:USDT", "NEIRO/USDT:USDT", "OP/USDT:USDT", "PENGU/USDT:USDT", "PNUT/USDT:USDT", "SEI/USDT:USDT", "SOL/USDT:USDT", "SPX/USDT:USDT", "SUI/USDT:USDT", "TAO/USDT:USDT", "TIA/USDT:USDT", "TRUMP/USDT:USDT", "UNI/USDT:USDT", "VIC/USDT:USDT", "VIRTUAL/USDT:USDT", "WIF/USDT:USDT", "WLD/USDT:USDT", "XLM/USDT:USDT", "XRP/USDT:USDT", "ZEREBRO/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "info", "enable_openapi": true, "jwt_secret_key": "db8c839c7e69d7ed25e43b836505ea340d7f6e496eb76175eae10c323e490988095dc7328577f724bc3ebb28db73dd5d0501447087d5fb6a0cd995c37aaf52fe", "ws_token": "50JKyE6VEqvufqvHpM87wMO90snMCXLeuEj7iKa8Sdg", "CORS_origins": [], "username": "AriFreqtrade", "password": "qq123456"}, "bot_name": "ADL-Anticipation-Backtest-USDT", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}, "order_types": {"entry": "limit", "exit": "limit", "stoploss": "limit", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "dataformat_ohlcv": "json", "dataformat_trades": "json", "position_adjustment_enable": false, "max_entry_position_adjustment": 0}