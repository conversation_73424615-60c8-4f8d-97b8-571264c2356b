{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 8, "stake_currency": "USDC", "stake_amount": "unlimited", "tradable_balance_ratio": 0.95, "dry_run": true, "dry_run_wallet": 50000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "max_leverage": 10.0, "liquidation_buffer": 0.05, "strategy": "OrderFlowImbalanceStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 180, "exit": 120, "unit": "seconds"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "order_types": {"entry": "limit", "exit": "market", "emergency_exit": "market", "force_entry": "market", "force_exit": "market", "stoploss": "market", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "exchange": {"name": "binance", "key": "", "secret": "", "use_public_trades": true, "ccxt_config": {"options": {"defaultType": "future", "createMarketBuyOrderRequiresPrice": false, "fetchOrderBook": true}, "timeout": 30000, "rateLimit": 100, "sandbox": false, "enableRateLimit": true, "verify": true}, "ccxt_async_config": {"options": {"defaultType": "future", "createMarketBuyOrderRequiresPrice": false, "fetchOrderBook": true}, "timeout": 30000, "rateLimit": 100, "sandbox": false, "enableRateLimit": true, "verify": true}, "enable_ws": false, "skip_open_order_update": false, "pair_whitelist": ["BTC/USDC:USDC", "ETH/USDC:USDC", "XRP/USDC:USDC", "ADA/USDC:USDC", "BNB/USDC:USDC", "SOL/USDC:USDC", "LINK/USDC:USDC", "AVAX/USDC:USDC"], "pair_blacklist": [".*DOWN/.*", ".*UP/.*", ".*BEAR/.*", ".*BULL/.*"]}, "orderflow": {"cache_size": 2000, "max_candles": 1000, "scale": 0.1, "stacked_imbalance_range": 5, "imbalance_volume": 2, "imbalance_ratio": 4}, "pairlists": [{"method": "StaticPairList"}], "orderflow_strategy_params": {"cvd_period": 20, "cvd_divergence_threshold": 0.7, "ofi_period": 10, "ofi_threshold": 2.0, "absorption_volume_threshold": 3.0, "absorption_price_tolerance": 0.0005, "long_short_ratio_threshold": 0.6, "signal_confluence_required": 2, "atr_period": 14, "atr_stop_multiplier": 2.0, "atr_profit_multiplier": 1.0, "max_position_time_minutes": 15, "min_volume_filter": 1000000}, "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8082, "verbosity": "info", "enable_openapi": true, "jwt_secret_key": "orderflow_jwt_secret_key_2025", "ws_token": "orderflow_ws_token_2025", "CORS_origins": [], "username": "OrderFlowTrader", "password": "orderflow123"}, "bot_name": "OrderFlowImbalanceBot", "initial_state": "running", "force_entry_enable": true, "internals": {"process_throttle_secs": 2, "heartbeat_interval": 30, "sd_notify": false}, "logging": {"level": "INFO", "disable_warnings": ["urllib3.connectionpool:InsecureRequestWarning", "urllib3.exceptions.InsecureRequestWarning"]}, "dataformat_ohlcv": "feather", "dataformat_trades": "feather", "process_only_new_candles": true}