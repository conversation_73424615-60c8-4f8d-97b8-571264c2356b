{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.95, "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": true, "trading_mode": "futures", "margin_mode": "isolated", "max_leverage": 10.0, "liquidation_buffer": 0.05, "timeframe": "3m", "mpdo_strategy": {"lookback_period": 100, "deviation_threshold": 1.8, "volume_threshold": 1.6, "atr_period": 14, "time_stop_minutes": 15, "quick_loss_threshold": -0.015, "enable_price_acceleration": true, "enable_volume_burst": false, "enable_trend_confirmation": true, "minimal_roi": {"0": 0.02, "5": 0.015, "10": 0.01, "15": 0.005, "20": 0.0}, "stoploss": -0.05, "trailing_stop": false, "use_custom_stoploss": true}, "strategy": "MPDOStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 30, "exit": 30, "unit": "seconds", "exit_timeout_count": 0}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "order_types": {"entry": "limit", "exit": "limit", "emergency_exit": "market", "force_entry": "market", "force_exit": "market", "stoploss": "market", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "exchange": {"name": "binance", "key": "", "secret": "", "password": "", "sandbox": false, "fee": 0.0007, "ccxt_config": {"enableRateLimit": true, "rateLimit": 200, "options": {"defaultType": "future"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT", "SOL/USDT:USDT"], "pair_blacklist": [], "trading_mode": "futures", "markets": {}}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": false, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "somethingrandom", "CORS_origins": [], "username": "", "password": ""}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}, "dataformat_ohlcv": "feather", "dataformat_trades": "feather"}