# FreqTrade量化交易系统Docker部署配置
# 功能：为AriQuantification项目提供完整的Docker容器化部署环境
# 支持：ADL策略、MPDO策略、数据收集、回测等功能

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    wget \
    curl \
    git \
    libffi-dev \
    libssl-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    libffi-dev \
    liblzma-dev \
    && rm -rf /var/lib/apt/lists/*

# 彻底解决TA-Lib安装问题
# 第1步：安装TA-Lib系统库
RUN wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz && \
    tar -xzf ta-lib-0.4.0-src.tar.gz && \
    cd ta-lib/ && \
    ./configure --prefix=/usr --build=x86_64-unknown-linux-gnu && \
    make && \
    make install && \
    cd .. && \
    rm -rf ta-lib ta-lib-0.4.0-src.tar.gz

# 第2步：修复库文件命名问题（关键步骤）
# TA-Lib生成libta_lib.so，但Python包装器寻找libta-lib.so
RUN ldconfig && \
    echo "修复TA-Lib库文件命名问题..." && \
    find /usr -name "libta_lib*" 2>/dev/null && \
    ln -sf /usr/lib/libta_lib.so.0 /usr/lib/libta-lib.so 2>/dev/null || \
    ln -sf /usr/lib/libta_lib.a /usr/lib/libta-lib.a 2>/dev/null || \
    echo "创建符号链接..." && \
    ldconfig && \
    echo "验证库文件:" && \
    ls -la /usr/lib/libta* 2>/dev/null

# 第3步：设置环境变量
ENV LD_LIBRARY_PATH=/usr/lib:/usr/local/lib:$LD_LIBRARY_PATH
ENV CFLAGS="-I/usr/include"
ENV LDFLAGS="-L/usr/lib"

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn && \
    pip config set global.timeout 300 && \
    pip install --upgrade pip

# 第4步：安装TA-Lib Python包装器
RUN pip install --no-cache-dir --timeout=300 TA-Lib

# 复制requirements文件
COPY requirements.txt .

# 安装FreqTrade（不包含所有可选依赖，避免冲突）
RUN pip install --no-cache-dir --timeout=300 freqtrade

# 安装项目其他依赖
RUN pip install --no-cache-dir --timeout=300 -r requirements.txt

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p /app/freqtrade-bot/user_data/logs && \
    mkdir -p /app/freqtrade-bot/user_data/data && \
    mkdir -p /app/storage/logs && \
    mkdir -p /app/logs

# 设置权限
RUN chmod +x /app/freqtrade-bot/scripts/*.py 2>/dev/null || true

# 暴露端口（FreqUI Web界面）
EXPOSE 8080
EXPOSE 8081

# 设置环境变量
ENV PYTHONPATH=/app
ENV FREQTRADE_CONFIG_PATH=/app/freqtrade-bot

# 默认启动命令（可通过docker-compose覆盖）
CMD ["freqtrade", "trade", "--config", "/app/freqtrade-bot/config_adl_production.json", "--strategy", "ADLAnticipationStrategy"]
