#!/usr/bin/env python3
"""
奇美拉策略回测验证脚本
功能：完整的回测流程，包括数据准备、回测执行、结果分析
作者：AriQuantification
版本：1.0.0
基于：奇美拉策略完整验证流程
"""

import os
import sys
import json
import logging
import subprocess
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ChimeraBacktest')

class ChimeraBacktestRunner:
    """奇美拉策略回测运行器"""
    
    def __init__(self, config_path: str = "config_chimera_backtest.json"):
        """
        初始化回测运行器
        
        Args:
            config_path: freqtrade配置文件路径
        """
        self.config_path = Path(config_path)
        self.project_root = Path(__file__).parent.parent
        
        # 验证配置文件
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        # 加载配置
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 设置路径
        self.data_dir = self.project_root / "user_data" / "data"
        self.results_dir = Path("backtest_results")
        self.results_dir.mkdir(exist_ok=True)
        
        logger.info("奇美拉策略回测运行器初始化完成")
        logger.info(f"配置文件: {self.config_path}")
        logger.info(f"数据目录: {self.data_dir}")
        logger.info(f"结果目录: {self.results_dir}")
    
    def check_prerequisites(self) -> bool:
        """检查回测前提条件"""
        logger.info("检查回测前提条件...")
        
        issues = []
        
        # 1. 检查策略文件
        strategy_path = self.project_root / "user_data" / "strategies" / "chimera_strategy.py"
        if not strategy_path.exists():
            issues.append(f"策略文件不存在: {strategy_path}")
        
        # 2. 检查数据提供者
        tfi_provider_path = self.project_root / "user_data" / "data_providers" / "tfi_data_provider.py"
        funding_provider_path = self.project_root / "user_data" / "data_providers" / "funding_rate_data_provider.py"
        
        if not tfi_provider_path.exists():
            issues.append(f"TFI数据提供者不存在: {tfi_provider_path}")
        
        if not funding_provider_path.exists():
            issues.append(f"资金费率数据提供者不存在: {funding_provider_path}")
        
        # 3. 检查基础数据
        pairs = self.config.get('exchange', {}).get('pair_whitelist', [])
        for pair in pairs:
            # 转换为文件名格式
            symbol = pair.replace('/', '_').replace(':', '_')
            
            # 检查OHLCV数据
            timeframe = self.config.get('timeframe', '1m')
            ohlcv_file = self.data_dir / "binance" / f"{symbol}-{timeframe}.feather"
            
            if not ohlcv_file.exists():
                issues.append(f"缺少OHLCV数据: {ohlcv_file}")
        
        # 4. 检查TFI历史数据
        tfi_data_dir = self.data_dir / "tfi_data"
        if tfi_data_dir.exists():
            for pair in pairs:
                symbol = pair.replace('/', '').replace(':', '')
                tfi_file = tfi_data_dir / f"{symbol}_tfi_data.feather"
                if not tfi_file.exists():
                    logger.warning(f"缺少TFI数据: {tfi_file}")
        else:
            logger.warning("TFI数据目录不存在，将使用默认值")
        
        # 5. 检查资金费率数据
        funding_data_dir = self.data_dir / "funding_rates"
        if funding_data_dir.exists():
            for pair in pairs:
                symbol = pair.replace('/', '').replace(':', '')
                funding_file = funding_data_dir / f"{symbol}_funding_rates.feather"
                if not funding_file.exists():
                    logger.warning(f"缺少资金费率数据: {funding_file}")
        else:
            logger.warning("资金费率数据目录不存在，将使用默认值")
        
        # 报告检查结果
        if issues:
            logger.error("发现以下问题:")
            for issue in issues:
                logger.error(f"  - {issue}")
            return False
        else:
            logger.info("所有前提条件检查通过 ✓")
            return True
    
    def prepare_data(self, timerange: str = None, download_days: int = 30) -> bool:
        """准备回测数据"""
        logger.info("准备回测数据...")
        
        try:
            # 1. 下载基础OHLCV数据
            pairs = self.config.get('exchange', {}).get('pair_whitelist', [])
            timeframe = self.config.get('timeframe', '1m')
            
            # 构建freqtrade下载命令
            download_cmd = [
                "freqtrade", "download-data",
                "-c", str(self.config_path),
                "--timeframes", timeframe,
                "--days", str(download_days)
            ]
            
            if timerange:
                download_cmd.extend(["--timerange", timerange])
            
            logger.info(f"下载基础数据: {' '.join(download_cmd)}")
            result = subprocess.run(download_cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                logger.error(f"下载基础数据失败: {result.stderr}")
                return False
            
            logger.info("基础数据下载完成")
            
            # 2. 准备TFI历史数据
            tfi_script = self.project_root / "scripts" / "download_tfi_historical_data.py"
            if tfi_script.exists():
                symbols = [pair.replace('/', '').replace(':', '') for pair in pairs]
                
                tfi_cmd = [
                    "python", str(tfi_script),
                    "--symbols"] + symbols + [
                    "--days", str(download_days)
                ]
                
                logger.info("下载TFI历史数据...")
                result = subprocess.run(tfi_cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
                
                if result.returncode != 0:
                    logger.warning(f"TFI数据下载失败: {result.stderr}")
                    logger.warning("将在回测中使用默认TFI值")
                else:
                    logger.info("TFI历史数据准备完成")
            else:
                logger.warning(f"TFI下载脚本不存在: {tfi_script}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据准备失败: {e}")
            return False
    
    def run_backtest(self, timerange: str = None, 
                    enable_plotting: bool = True) -> Dict[str, Any]:
        """运行回测"""
        logger.info("开始运行奇美拉策略回测...")
        
        try:
            # 构建回测命令
            backtest_cmd = [
                "freqtrade", "backtesting",
                "-c", str(self.config_path),
                "--strategy", "ChimeraStrategy",
                "--export", "trades",
                "--export-filename", str(self.results_dir / "chimera_backtest_result.json"),
                "--breakdown", "day",
                "--breakdown", "week",
                "--breakdown", "month"
            ]
            
            if timerange:
                backtest_cmd.extend(["--timerange", timerange])
            
            # 运行回测
            logger.info(f"执行回测命令: {' '.join(backtest_cmd)}")
            start_time = datetime.now()
            
            result = subprocess.run(backtest_cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if result.returncode != 0:
                logger.error(f"回测失败: {result.stderr}")
                return {
                    'success': False,
                    'error': result.stderr,
                    'duration': duration
                }
            
            logger.info(f"回测完成，耗时: {duration:.1f} 秒")
            
            # 解析回测输出
            backtest_results = self._parse_backtest_results(result.stdout)
            backtest_results['success'] = True
            backtest_results['duration'] = duration
            
            # 生成图表（如果启用）
            if enable_plotting:
                self._generate_backtest_plots(timerange)
            
            return backtest_results
            
        except subprocess.TimeoutExpired:
            logger.error("回测超时")
            return {'success': False, 'error': '回测超时'}
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _parse_backtest_results(self, output: str) -> Dict[str, Any]:
        """解析回测输出"""
        results = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'profit_total_pct': 0.0,
            'profit_total_abs': 0.0,
            'max_drawdown': 0.0,
            'expectancy': 0.0,
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'calmar_ratio': 0.0,
            'raw_output': output
        }
        
        try:
            lines = output.split('\n')
            
            for line in lines:
                line = line.strip()
                
                if "Total trades" in line:
                    results['total_trades'] = int(line.split(':')[1].strip())
                
                elif "│ Win  │" in line and "│" in line:
                    # 解析胜利统计行
                    parts = [p.strip() for p in line.split('│') if p.strip()]
                    if len(parts) >= 3:
                        try:
                            results['winning_trades'] = int(parts[1])
                            if len(parts) >= 4 and '%' in parts[3]:
                                results['win_rate'] = float(parts[3].rstrip('%')) / 100
                        except (ValueError, IndexError):
                            pass
                
                elif "│ Loss │" in line and "│" in line:
                    # 解析失败统计行
                    parts = [p.strip() for p in line.split('│') if p.strip()]
                    if len(parts) >= 2:
                        try:
                            results['losing_trades'] = int(parts[1])
                        except (ValueError, IndexError):
                            pass
                
                elif "Total profit" in line and "%" in line:
                    # 解析总利润
                    try:
                        if "(" in line and ")" in line:
                            # 格式: Total profit  123.45 USDT (12.34%)
                            pct_part = line.split('(')[1].split(')')[0].rstrip('%')
                            results['profit_total_pct'] = float(pct_part)
                            
                            abs_part = line.split()[2]  # 假设是第3个词
                            if abs_part.replace('.', '').replace('-', '').isdigit():
                                results['profit_total_abs'] = float(abs_part)
                    except (ValueError, IndexError):
                        pass
                
                elif "Max Drawdown" in line and "%" in line:
                    try:
                        # 提取百分比
                        pct_start = line.find('(') + 1
                        pct_end = line.find('%)')
                        if pct_start > 0 and pct_end > pct_start:
                            dd_pct = line[pct_start:pct_end]
                            results['max_drawdown'] = float(dd_pct)
                    except (ValueError, IndexError):
                        pass
                
                elif "Expectancy" in line:
                    try:
                        expectancy_str = line.split(':')[1].strip()
                        if expectancy_str.replace('.', '').replace('-', '').isdigit():
                            results['expectancy'] = float(expectancy_str)
                    except (ValueError, IndexError):
                        pass
                
                elif "Sharpe" in line:
                    try:
                        sharpe_str = line.split(':')[1].strip()
                        if sharpe_str.replace('.', '').replace('-', '').isdigit():
                            results['sharpe_ratio'] = float(sharpe_str)
                    except (ValueError, IndexError):
                        pass
                
                elif "Sortino" in line:
                    try:
                        sortino_str = line.split(':')[1].strip()
                        if sortino_str.replace('.', '').replace('-', '').isdigit():
                            results['sortino_ratio'] = float(sortino_str)
                    except (ValueError, IndexError):
                        pass
                
                elif "Calmar" in line:
                    try:
                        calmar_str = line.split(':')[1].strip()
                        if calmar_str.replace('.', '').replace('-', '').isdigit():
                            results['calmar_ratio'] = float(calmar_str)
                    except (ValueError, IndexError):
                        pass
            
            logger.info(f"解析回测结果: {results['total_trades']} 笔交易, "
                       f"胜率 {results['win_rate']:.1%}, "
                       f"总收益 {results['profit_total_pct']:.2f}%")
            
        except Exception as e:
            logger.warning(f"解析回测结果时出错: {e}")
        
        return results
    
    def _generate_backtest_plots(self, timerange: str = None):
        """生成回测图表"""
        try:
            plot_cmd = [
                "freqtrade", "plot-dataframe",
                "-c", str(self.config_path),
                "--strategy", "ChimeraStrategy",
                "--export-filename", str(self.results_dir / "chimera_backtest_result.json")
            ]
            
            if timerange:
                plot_cmd.extend(["--timerange", timerange])
            
            logger.info("生成回测图表...")
            result = subprocess.run(plot_cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("回测图表生成完成")
            else:
                logger.warning(f"图表生成失败: {result.stderr}")
                
        except Exception as e:
            logger.warning(f"生成图表时出错: {e}")
    
    def generate_summary_report(self, backtest_results: Dict[str, Any], 
                              timerange: str = None) -> str:
        """生成回测摘要报告"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.results_dir / f"chimera_backtest_summary_{timestamp}.txt"
        
        report = []
        report.append("=" * 80)
        report.append("奇美拉策略回测摘要报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"回测时间范围: {timerange or '配置文件默认范围'}")
        report.append(f"回测配置文件: {self.config_path}")
        report.append("")
        
        if backtest_results.get('success', False):
            report.append("回测结果:")
            report.append("-" * 40)
            report.append(f"总交易数: {backtest_results.get('total_trades', 0)}")
            report.append(f"盈利交易: {backtest_results.get('winning_trades', 0)}")
            report.append(f"亏损交易: {backtest_results.get('losing_trades', 0)}")
            report.append(f"胜率: {backtest_results.get('win_rate', 0):.1%}")
            report.append(f"总收益率: {backtest_results.get('profit_total_pct', 0):.2f}%")
            report.append(f"总收益: {backtest_results.get('profit_total_abs', 0):.2f} USDT")
            report.append(f"最大回撤: {backtest_results.get('max_drawdown', 0):.2f}%")
            report.append(f"夏普比率: {backtest_results.get('sharpe_ratio', 0):.2f}")
            report.append(f"索提诺比率: {backtest_results.get('sortino_ratio', 0):.2f}")
            report.append(f"卡尔玛比率: {backtest_results.get('calmar_ratio', 0):.2f}")
            report.append(f"回测耗时: {backtest_results.get('duration', 0):.1f} 秒")
            
            # 策略评估
            report.append("")
            report.append("策略评估:")
            report.append("-" * 40)
            
            total_trades = backtest_results.get('total_trades', 0)
            win_rate = backtest_results.get('win_rate', 0)
            profit_pct = backtest_results.get('profit_total_pct', 0)
            max_dd = backtest_results.get('max_drawdown', 0)
            sharpe = backtest_results.get('sharpe_ratio', 0)
            
            # 综合评分
            score = 0
            
            if total_trades >= 20:
                score += 20
            elif total_trades >= 10:
                score += 15
            else:
                score += 5
            
            if win_rate >= 0.6:
                score += 25
            elif win_rate >= 0.5:
                score += 20
            elif win_rate >= 0.4:
                score += 10
            
            if profit_pct >= 20:
                score += 25
            elif profit_pct >= 10:
                score += 20
            elif profit_pct >= 5:
                score += 15
            elif profit_pct > 0:
                score += 10
            
            if max_dd <= 5:
                score += 20
            elif max_dd <= 10:
                score += 15
            elif max_dd <= 15:
                score += 10
            
            if sharpe >= 2:
                score += 10
            elif sharpe >= 1:
                score += 8
            elif sharpe >= 0.5:
                score += 5
            
            if score >= 85:
                rating = "A+ 优秀"
            elif score >= 75:
                rating = "A 良好"
            elif score >= 65:
                rating = "B+ 中上"
            elif score >= 55:
                rating = "B 中等"
            elif score >= 45:
                rating = "C+ 中下"
            elif score >= 35:
                rating = "C 较差"
            else:
                rating = "D 很差"
            
            report.append(f"综合评分: {score}/100")
            report.append(f"策略评级: {rating}")
            
            # 建议
            report.append("")
            report.append("优化建议:")
            report.append("-" * 40)
            
            suggestions = []
            
            if total_trades < 20:
                suggestions.append("• 交易数量较少，建议扩大回测时间范围或降低入场阈值")
            
            if win_rate < 0.5:
                suggestions.append("• 胜率偏低，考虑优化TFI和SPI参数，或增加过滤条件")
            
            if max_dd > 15:
                suggestions.append("• 最大回撤过大，建议加强风险管理，调整止损参数")
            
            if profit_pct < 5:
                suggestions.append("• 收益率较低，可能需要调整头寸管理或凯利参数")
            
            if sharpe < 1:
                suggestions.append("• 夏普比率偏低，需要提高收益稳定性")
            
            if not suggestions:
                suggestions.append("• 策略表现良好，可考虑实盘测试")
            
            for suggestion in suggestions:
                report.append(suggestion)
        else:
            report.append("回测失败:")
            report.append("-" * 40)
            report.append(f"错误信息: {backtest_results.get('error', '未知错误')}")
        
        report.append("")
        report.append("文件路径:")
        report.append("-" * 40)
        report.append(f"配置文件: {self.config_path}")
        report.append(f"结果目录: {self.results_dir}")
        report.append(f"策略文件: user_data/strategies/chimera_strategy.py")
        report.append("")
        report.append("=" * 80)
        
        # 保存报告
        report_text = "\n".join(report)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        logger.info(f"摘要报告已保存到: {report_file}")
        return report_text

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='奇美拉策略回测验证')
    parser.add_argument('--config', '-c', default='config_chimera_backtest.json',
                       help='配置文件路径')
    parser.add_argument('--timerange', '-t',
                       help='时间范围 (格式: 20230101-20231231)')
    parser.add_argument('--days', '-d', type=int, default=30,
                       help='下载数据天数')
    parser.add_argument('--skip-data', action='store_true',
                       help='跳过数据准备步骤')
    parser.add_argument('--skip-plots', action='store_true',
                       help='跳过图表生成')
    parser.add_argument('--check-only', action='store_true',
                       help='仅检查前提条件')
    
    args = parser.parse_args()
    
    try:
        # 创建回测运行器
        runner = ChimeraBacktestRunner(args.config)
        
        # 检查前提条件
        logger.info("开始奇美拉策略回测验证...")
        
        if not runner.check_prerequisites():
            logger.error("前提条件检查失败，无法进行回测")
            sys.exit(1)
        
        if args.check_only:
            logger.info("前提条件检查通过，退出")
            return
        
        # 准备数据
        if not args.skip_data:
            if not runner.prepare_data(args.timerange, args.days):
                logger.error("数据准备失败")
                sys.exit(1)
        else:
            logger.info("跳过数据准备步骤")
        
        # 运行回测
        backtest_results = runner.run_backtest(
            timerange=args.timerange,
            enable_plotting=not args.skip_plots
        )
        
        # 生成报告
        summary_report = runner.generate_summary_report(backtest_results, args.timerange)
        print("\n" + summary_report)
        
        if backtest_results.get('success', False):
            logger.info("✅ 奇美拉策略回测验证完成！")
            
            # 启动性能监控（如果存在）
            monitor_script = runner.project_root / "scripts" / "chimera_performance_monitor.py"
            if monitor_script.exists():
                logger.info("可以运行以下命令进行详细性能分析:")
                logger.info(f"python {monitor_script} --db-path tradesv3_chimera.sqlite")
        else:
            logger.error("❌ 回测失败")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("用户中断回测")
    except Exception as e:
        logger.error(f"回测过程发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 