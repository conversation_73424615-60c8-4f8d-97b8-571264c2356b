"""
CCXT数据源实现

通过CCXT库连接加密货币交易所，获取历史市场数据。
"""

import time
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple, Union

import pandas as pd
import numpy as np
import ccxt

from data.base import DataSource
from data.structures import OHLCVColumns
from data.sources.exchange_utils import (
    normalize_symbol, 
    rate_limit_handler, 
    get_timeframe_milliseconds,
    split_time_range,
    get_exchange_instance
)


class CCXTConfig:
    """
    CCXT配置类
    
    用于配置CCXT交易所连接和行为。
    """
    
    def __init__(self, 
                 exchange_id: str,
                 api_key: Optional[str] = None,
                 secret: Optional[str] = None,
                 timeout: int = 5000,  # 🔥 根本修复：从30秒减少到5秒
                 enable_rate_limit: bool = True,
                 proxy: Optional[str] = None,
                 options: Optional[Dict[str, Any]] = None):
        """
        初始化CCXT配置
        
        Args:
            exchange_id: 交易所ID，如'binance', 'okex'等
            api_key: API Key，用于私有API
            secret: API Secret，用于私有API
            timeout: 请求超时时间（毫秒）
            enable_rate_limit: 是否启用请求频率限制
            proxy: 代理服务器URL字符串（将自动转换为CCXT需要的proxies格式）
            options: 交易所特定选项
        """
        self.exchange_id = exchange_id
        self.api_key = api_key
        self.secret = secret
        self.timeout = timeout
        self.enable_rate_limit = enable_rate_limit
        self.proxy = proxy
        self.options = options if options is not None else {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将配置转换为字典，用于初始化CCXT交易所
        
        Returns:
            配置字典
        """
        config: Dict[str, Any] = {
            'apiKey': self.api_key,
            'secret': self.secret,
            'timeout': self.timeout,
            'enableRateLimit': self.enable_rate_limit
        }
        
        if self.proxy:
            config['proxies'] = {
                'http': self.proxy,
                'https': self.proxy
            }
        
        if self.options:
            config['options'] = self.options
            
        return config


class CCXTDataSource(DataSource):
    """
    CCXT数据源
    
    使用CCXT库从加密货币交易所获取OHLCV数据。
    """
    
    def __init__(self, config: CCXTConfig):
        """
        初始化CCXT数据源
        
        Args:
            config: 交易所配置
        """
        self.config = config
        self.exchange_id = config.exchange_id
        
        # 初始化交易所
        self.exchange = get_exchange_instance(self.exchange_id, config.to_dict())
        
        # 创建API请求处理器
        self.api_handler = rate_limit_handler(self.exchange)
        
        # 配置日志
        self.logger = logging.getLogger(f"CCXTDataSource.{self.exchange_id}")
        
        # 检查交易所是否支持OHLCV数据获取
        if not self.exchange.has['fetchOHLCV']:
            raise ValueError(f"交易所 {self.exchange_id} 不支持获取OHLCV数据")
        
        # 获取支持的时间周期
        self.supported_timeframes = list(self.exchange.timeframes.keys()) if hasattr(self.exchange, 'timeframes') else []
        
    def get_data(self, symbol: str, timeframe: str, 
                start_time: Optional[datetime] = None,
                end_time: Optional[datetime] = None,
                limit: Optional[int] = None) -> pd.DataFrame:
        """
        获取OHLCV数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            limit: 获取的最大数据点数量
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        # 对交易对符号进行标准化
        normalized_symbol = normalize_symbol(symbol, self.exchange_id)
        
        # 检查时间周期是否受支持
        if self.supported_timeframes and timeframe not in self.supported_timeframes:
            self.logger.warning(f"时间周期 {timeframe} 可能不受支持，支持的时间周期有: {self.supported_timeframes}")
        
        # 转换时间
        start_timestamp = int(start_time.timestamp() * 1000) if start_time else None
        end_timestamp = int(end_time.timestamp() * 1000) if end_time else None
        current_time = int(datetime.now(timezone.utc).timestamp() * 1000)
        
        if not start_timestamp:
            # 默认获取最近的数据
            if limit:
                # 如果设置了数量限制，根据时间周期估算开始时间
                tf_ms = get_timeframe_milliseconds(timeframe)
                start_timestamp = current_time - (limit * tf_ms)
            else:
                # 如果没有限制，使用交易所默认值，通常是最近的100条数据
                start_timestamp = None
        
        if not end_timestamp:
            end_timestamp = current_time
        
        # 检查时间范围
        if start_timestamp and end_timestamp and start_timestamp >= end_timestamp:
            return pd.DataFrame()  # 返回空DataFrame
        
        # 设置抓取的数据量限制
        max_limit = 1000  # 大多数交易所的单次请求限制
        if limit and limit < max_limit:
            max_limit = limit
        
        # 计算时间范围
        tf_ms = get_timeframe_milliseconds(timeframe)
        max_range_ms = tf_ms * max_limit
        
        # 分割时间范围以避免超过交易所限制
        data_frames = []
        
        if start_timestamp and end_timestamp:
            time_ranges = split_time_range(start_timestamp, end_timestamp, max_range_ms)
            
            for time_range_start, time_range_end in time_ranges:
                # 获取这个时间范围的数据
                ohlcv_data = self._fetch_ohlcv(normalized_symbol, timeframe, time_range_start, time_range_end, max_limit)
                
                if ohlcv_data.empty:
                    continue
                
                data_frames.append(ohlcv_data)
                
                # 如果设置了数量限制并且已经达到，则停止
                if limit and sum(len(df) for df in data_frames) >= limit:
                    break
        else:
            # 单次请求获取最近的数据
            ohlcv_data = self._fetch_ohlcv(normalized_symbol, timeframe, start_timestamp, end_timestamp, limit)
            if not ohlcv_data.empty:
                data_frames.append(ohlcv_data)
        
        # 合并结果
        if not data_frames:
            return pd.DataFrame()
        
        result = pd.concat(data_frames)
        
        # 删除重复的行
        result = result[~result.index.duplicated(keep='first')]
        
        # 按时间排序
        result = result.sort_index()
        
        # 应用数量限制
        if limit and len(result) > limit:
            result = result.iloc[-limit:]
        
        return result
    
    def _fetch_ohlcv(self, symbol: str, timeframe: str, 
                   since: Optional[int] = None, 
                   until: Optional[int] = None,
                   limit: Optional[int] = None) -> pd.DataFrame:
        """
        从交易所获取OHLCV数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            since: 开始时间戳（毫秒）
            until: 结束时间戳（毫秒）
            limit: 获取的最大数据点数量
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 调用API获取数据
            ohlcv = self.api_handler('fetchOHLCV', symbol, timeframe, since, limit)
            
            if not ohlcv:
                self.logger.warning(f"未找到数据: {symbol}, {timeframe}, {since}, {until}")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv, columns=[
                OHLCVColumns.TIMESTAMP,
                OHLCVColumns.OPEN,
                OHLCVColumns.HIGH,
                OHLCVColumns.LOW,
                OHLCVColumns.CLOSE,
                OHLCVColumns.VOLUME
            ])
            
            # 将时间戳转换为datetime
            df[OHLCVColumns.TIMESTAMP] = pd.to_datetime(df[OHLCVColumns.TIMESTAMP], unit='ms')
            
            # 设置时间戳为索引
            df = df.set_index(OHLCVColumns.TIMESTAMP)
            
            # 过滤结束时间
            if until:
                df = df[df.index <= pd.Timestamp(until, unit='ms')]
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取数据失败: {symbol}, {timeframe}, {since}, {until}, 错误: {str(e)}")
            return pd.DataFrame()
    
    def get_available_symbols(self) -> List[str]:
        """
        获取交易所支持的所有交易对
        
        Returns:
            交易对列表
        """
        try:
            # 确保市场已加载
            if not self.exchange.markets:
                self.exchange.load_markets()
                
            # 返回所有活跃交易对
            if self.exchange.markets:
                return [symbol for symbol, market in self.exchange.markets.items() 
                       if market.get('active', True)]
            else:
                self.logger.warning("交易所markets为None或空")
                return []
        except Exception as e:
            self.logger.error(f"获取交易对列表失败: {str(e)}")
            return []
    
    def get_exchange_info(self) -> Dict[str, Any]:
        """
        获取交易所信息
        
        Returns:
            交易所信息字典
        """
        symbols = self.get_symbols()
        
        # 获取has属性
        has_dict = {'fetchOHLCV': False}
        if hasattr(self.exchange, 'has'):
            if isinstance(self.exchange.has, dict):
                has_dict = self.exchange.has
            else:
                has_dict = {'fetchOHLCV': bool(self.exchange.has)}
        
        # 安全获取API文档URL
        api_docs = None
        if hasattr(self.exchange, 'urls') and self.exchange.urls is not None:
            api_docs = self.exchange.urls.get('api')
        
        return {
            'id': self.exchange_id,
            'name': self.exchange.name if hasattr(self.exchange, 'name') else self.exchange_id,
            'timeframes': self.supported_timeframes,
            'symbols_count': len(symbols),
            'has': has_dict,
            'rate_limit': self.exchange.rateLimit if hasattr(self.exchange, 'rateLimit') else None,
            'api_docs': api_docs,
        }
    
    def fetch_data(self, symbol: str, timeframe: str, 
                   start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        获取指定时间范围的市场数据 - 🔥 根本修复：确保获取最新实时数据，修复API调用错误
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期，如"1m", "1h", "1d"等
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        # 🔥 强制使用UTC时间
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)
        
        # 🔥 记录详细的数据获取信息
        self.logger.info(f"🚀 CCXT正在获取最新数据:")
        self.logger.info(f"   📊 交易对: {symbol}")
        self.logger.info(f"   📈 时间框架: {timeframe}")
        self.logger.info(f"   📅 时间范围: {start_time} - {end_time}")
        
        # 🔥 真实环境：强制获取最新数据，完全重构API调用
        try:
            # 🔥 重要：确保交易所连接是最新的，清除可能的缓存
            normalized_symbol = normalize_symbol(symbol, self.exchange_id)
            
            # 🔧 简化时间戳处理（移除复杂的时间管理器依赖）
            start_timestamp = int(start_time.timestamp() * 1000)
            end_timestamp = int(end_time.timestamp() * 1000)

            # 🔧 计算需要获取的数据量
            timeframe_ms = get_timeframe_milliseconds(timeframe)
            data_points_needed = int((end_timestamp - start_timestamp) / timeframe_ms) + 10
            limit = min(1000, max(100, data_points_needed))  # 限制在合理范围内
            
            self.logger.info(f"🔄 调用交易所API获取数据:")
            self.logger.info(f"   📋 标准化交易对: {normalized_symbol}")
            self.logger.info(f"   🕐 开始时间戳: {start_timestamp}")
            self.logger.info(f"   🕐 结束时间戳: {end_timestamp}")
            self.logger.info(f"   📊 请求数量: {limit}")
            
            # 🔥 根本修复：正确的API调用方式 - fetchOHLCV(symbol, timeframe, since, limit)
            # 移除错误的第5个参数，CCXT标准API只接受4个参数
            ohlcv_data = self.api_handler('fetchOHLCV', normalized_symbol, timeframe, start_timestamp, limit)
            
            if not ohlcv_data:
                self.logger.warning(f"⚠️ 交易所未返回数据: {normalized_symbol}")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(ohlcv_data, columns=[
                OHLCVColumns.TIMESTAMP,
                OHLCVColumns.OPEN,
                OHLCVColumns.HIGH,
                OHLCVColumns.LOW,
                OHLCVColumns.CLOSE,
                OHLCVColumns.VOLUME
            ])
            
            # 🔥 时间戳处理 - 确保使用UTC时间
            df[OHLCVColumns.TIMESTAMP] = pd.to_datetime(df[OHLCVColumns.TIMESTAMP], unit='ms', utc=True)
            # 转换为本地时间但保持UTC信息
            df[OHLCVColumns.TIMESTAMP] = df[OHLCVColumns.TIMESTAMP].dt.tz_convert(None)
            
            # 设置时间戳为索引
            df = df.set_index(OHLCVColumns.TIMESTAMP)
            
            # 过滤到指定的结束时间
            end_time_naive = end_time.replace(tzinfo=None) if end_time.tzinfo else end_time
            df = df[df.index <= end_time_naive]
            
            # 🔥 验证数据质量
            if len(df) > 0:
                latest_time = df.index[-1]
                current_time = datetime.now(timezone.utc).replace(tzinfo=None)
                delay_minutes = (current_time - latest_time).total_seconds() / 60
                
                self.logger.info(f"✅ 成功获取实时数据:")
                self.logger.info(f"   📊 数据条数: {len(df)}")
                self.logger.info(f"   🕐 最新数据时间: {latest_time}")
                self.logger.info(f"   ⏰ 数据延迟: {delay_minutes:.1f}分钟")
                
                if delay_minutes > 10:
                    self.logger.warning(f"⚠️ 数据延迟较大: {delay_minutes:.1f}分钟")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 获取实时数据失败: {symbol}, {timeframe}")
            self.logger.error(f"   错误详情: {str(e)}")
            
            # 🔥 打印详细错误信息
            import traceback
            self.logger.error(f"详细错误堆栈:\n{traceback.format_exc()}")
            
            return pd.DataFrame()
    
    def get_symbols(self) -> List[str]:
        """
        获取支持的交易对列表
        
        Returns:
            交易对代码列表
        """
        # 在测试环境中直接返回exchange.symbols属性（如果已被mock）
        if hasattr(self.exchange, 'symbols') and isinstance(self.exchange.symbols, list):
            return self.exchange.symbols
            
        # 否则使用标准方法获取
        return self.get_available_symbols()
    
    def get_timeframes(self) -> List[str]:
        """
        获取支持的时间周期列表
        
        Returns:
            时间周期列表
        """
        return self.supported_timeframes if hasattr(self, 'supported_timeframes') else [] 