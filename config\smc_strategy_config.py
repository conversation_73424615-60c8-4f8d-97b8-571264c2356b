#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC策略统一参数配置模块

完全重构：实现统一的配置管理、参数优化和动态加载系统
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict, field
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class SMCStrategyParams:
    """SMC策略核心参数 - 5分钟市场重构版"""
    # 🔧 5分钟市场核心参数
    structure_periods: int = 12          # 市场结构识别周期（5分钟*12=1小时）
    trend_strength: float = 0.004        # 趋势强度阈值（适配5分钟波动）
    risk_reward_ratio: float = 2.5       # 风险回报比（5分钟市场适中）

    # Order Block参数 - 5分钟市场特化
    ob_formation_periods: int = 10       # OB形成周期（5分钟*10=50分钟）
    ob_volume_threshold: float = 1.6     # 成交量阈值（5分钟需要更高确认）
    ob_price_reaction: float = 1.0       # 价格反应倍数（ATR倍数）

    # Fair Value Gap参数 - 5分钟级别
    fvg_min_gap_ratio: float = 0.25      # 最小缺口比例（ATR的25%）
    fvg_validity_periods: int = 36       # FVG有效期（5分钟*36=3小时）

    # 流动性区域参数 - 5分钟市场
    liquidity_zone_strength: float = 1.5 # 流动性区域强度

    # 动量确认参数 - 5分钟特化
    momentum_periods: int = 12           # 动量计算周期
    momentum_threshold: float = 0.5      # 动量阈值

    # 信号过滤参数 - 5分钟市场适配
    signal_cooldown_periods: int = 12    # 信号间隔（5分钟*12=1小时）
    max_daily_signals: int = 12          # 每日最大信号数
    min_signal_quality: float = 0.6      # 最小信号质量

    # 技术指标参数
    atr_periods: int = 20                # ATR周期（5分钟用更长周期）
    atr_multiplier: float = 2.0          # ATR倍数

    # 🔧 向后兼容参数（映射到新参数）
    swing_threshold: float = 0.004       # 映射到trend_strength
    bos_threshold: float = 0.002         # 映射到trend_strength的一半
    ob_lookback: int = 10                # 映射到ob_formation_periods
    fvg_threshold: float = 0.25          # 映射到fvg_min_gap_ratio
    htf_bias_periods: int = 36           # 映射到fvg_validity_periods


@dataclass  
class SMCRiskManagerParams:
    """SMC风险管理器参数"""
    stop_loss_pct: float = 2.5
    take_profit_pct: float = 5.0
    max_position_pct: float = 8.0
    max_drawdown_limit: float = 15.0
    kelly_fraction: float = 0.25
    max_trades_per_day: int = 10


@dataclass
class SMCSignalFilterParams:
    """SMC信号过滤器参数"""
    min_volume_ratio: float = 1.5
    max_spread_pct: float = 0.1
    min_atr_ratio: float = 0.8
    rsi_oversold: float = 30
    rsi_overbought: float = 70
    use_session_filter: bool = True


@dataclass
class SMCOptimizationParams:
    """SMC策略优化参数"""
    enabled: bool = True
    target_metric: str = "sharpe_ratio"
    max_combinations: int = 1000
    min_trades_required: int = 50
    max_optimization_time: int = 3600


@dataclass
class SMCMonitoringParams:
    """SMC监控参数"""
    signal_monitor_enabled: bool = True
    max_signals_per_interval: int = 10
    capital_monitor_enabled: bool = True
    max_drawdown_pct: float = 5.0
    price_monitor_enabled: bool = True
    max_short_term_change_pct: float = 3.0


class SMCConfigManager:
    """SMC策略统一配置管理器"""
    
    def __init__(self, config_dir: str = None):
        """初始化配置管理器"""
        if config_dir is None:
            config_dir = os.path.join(os.path.dirname(__file__))
        
        self.config_dir = Path(config_dir)
        self.strategy_params = SMCStrategyParams()
        self.risk_params = SMCRiskManagerParams()
        self.filter_params = SMCSignalFilterParams()
        self.optimization_params = SMCOptimizationParams()
        self.monitoring_params = SMCMonitoringParams()
        
        # 尝试加载现有配置
        self._load_default_configs()
    
    def _load_default_configs(self):
        """加载默认配置文件"""
        # ✅ 优先使用统一配置文件
        unified_config = self.config_dir / 'smc_unified_config.json'
        if unified_config.exists():
            try:
                with open(unified_config, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self._update_unified_config(config_data)
                logger.info(f"已加载统一配置文件: smc_unified_config.json")
                return
            except Exception as e:
                logger.warning(f"加载统一配置文件失败: {e}")
        
        # 降级到旧配置文件
        config_files = [
            'smc_strategy_config.json'
        ]
        
        for config_file in config_files:
            config_path = self.config_dir / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    self._update_params_from_dict(config_data)
                    logger.info(f"已加载配置文件: {config_file}")
                except Exception as e:
                    logger.warning(f"加载配置文件失败 {config_file}: {e}")
    
    def _update_unified_config(self, config_data: Dict[str, Any]):
        """更新统一配置 - 支持5分钟参数映射"""
        # 策略参数
        if 'strategy' in config_data and 'parameters' in config_data['strategy']:
            strategy_params = config_data['strategy']['parameters']

            # 🔧 参数映射：旧参数名 -> 新参数名
            param_mapping = {
                'swing_threshold': 'trend_strength',
                'bos_threshold': 'trend_strength',  # 使用较小值
                'ob_lookback': 'ob_formation_periods',
                'fvg_threshold': 'fvg_min_gap_ratio',
                'htf_bias_periods': 'fvg_validity_periods'
            }

            for key, value in strategy_params.items():
                # 直接映射
                if hasattr(self.strategy_params, key):
                    setattr(self.strategy_params, key, value)
                # 参数映射
                elif key in param_mapping:
                    new_key = param_mapping[key]
                    if hasattr(self.strategy_params, new_key):
                        # 特殊处理某些参数
                        if key == 'fvg_threshold' and value < 1.0:
                            # fvg_threshold通常是小数，需要转换为比例
                            value = value * 100 if value < 0.01 else value
                        elif key == 'htf_bias_periods' and value > 100:
                            # htf_bias_periods可能需要转换为5分钟周期
                            value = value // 5
                        setattr(self.strategy_params, new_key, value)

            # 🔧 设置向后兼容的映射值
            self.strategy_params.swing_threshold = self.strategy_params.trend_strength
            self.strategy_params.bos_threshold = self.strategy_params.trend_strength / 2
            self.strategy_params.ob_lookback = self.strategy_params.ob_formation_periods
            self.strategy_params.fvg_threshold = self.strategy_params.fvg_min_gap_ratio
            self.strategy_params.htf_bias_periods = self.strategy_params.fvg_validity_periods
        
        # 风险管理参数
        if 'risk_management' in config_data:
            risk_params = config_data['risk_management']
            for key, value in risk_params.items():
                if hasattr(self.risk_params, key):
                    setattr(self.risk_params, key, value)
        
        # 信号过滤参数
        if 'signal_filter' in config_data:
            filter_params = config_data['signal_filter']
            for key, value in filter_params.items():
                if hasattr(self.filter_params, key):
                    setattr(self.filter_params, key, value)
        
        # 优化参数
        if 'optimization' in config_data:
            opt_params = config_data['optimization']
            for key, value in opt_params.items():
                if key not in ['grid', 'constraints']:
                    if hasattr(self.optimization_params, key):
                        setattr(self.optimization_params, key, value)
            
            # 约束条件
            if 'constraints' in opt_params:
                constraints = opt_params['constraints']
                for key, value in constraints.items():
                    if hasattr(self.optimization_params, key):
                        setattr(self.optimization_params, key, value)
        
        # 监控参数
        if 'monitoring' in config_data:
            self._update_monitoring_from_unified(config_data['monitoring'])
    
    def _update_monitoring_from_unified(self, monitoring_data: Dict[str, Any]):
        """从统一配置更新监控参数"""
        if 'signal_monitor' in monitoring_data:
            signal_mon = monitoring_data['signal_monitor']
            self.monitoring_params.signal_monitor_enabled = signal_mon.get('enabled', True)
            self.monitoring_params.max_signals_per_interval = signal_mon.get('max_signals_per_interval', 10)
        
        if 'capital_monitor' in monitoring_data:
            capital_mon = monitoring_data['capital_monitor']
            self.monitoring_params.capital_monitor_enabled = capital_mon.get('enabled', True)
            self.monitoring_params.max_drawdown_pct = capital_mon.get('max_drawdown_pct', 5.0)
        
        if 'price_monitor' in monitoring_data:
            price_mon = monitoring_data['price_monitor']
            self.monitoring_params.price_monitor_enabled = price_mon.get('enabled', True)
            self.monitoring_params.max_short_term_change_pct = price_mon.get('max_short_term_change_pct', 3.0)
    
    def _update_params_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新参数"""
        # 更新策略参数
        if 'strategy' in config_data:
            strategy_data = config_data['strategy']
            for key, value in strategy_data.items():
                if hasattr(self.strategy_params, key):
                    setattr(self.strategy_params, key, value)
        
        # 兼容旧格式
        if 'strategy_params' in config_data:
            strategy_data = config_data['strategy_params']
            for key, value in strategy_data.items():
                if hasattr(self.strategy_params, key):
                    setattr(self.strategy_params, key, value)
        
        # 更新风险管理参数
        if 'risk_manager' in config_data:
            risk_data = config_data['risk_manager']
            for key, value in risk_data.items():
                if hasattr(self.risk_params, key):
                    setattr(self.risk_params, key, value)
        
        # 兼容旧格式
        if 'risk_manager_params' in config_data:
            risk_data = config_data['risk_manager_params']
            for key, value in risk_data.items():
                if hasattr(self.risk_params, key):
                    setattr(self.risk_params, key, value)
        
        # 更新信号过滤参数
        if 'signal_filter' in config_data:
            filter_data = config_data['signal_filter']
            for key, value in filter_data.items():
                if hasattr(self.filter_params, key):
                    setattr(self.filter_params, key, value)
        
        # 兼容旧格式
        if 'signal_filter_params' in config_data:
            filter_data = config_data['signal_filter_params']
            for key, value in filter_data.items():
                if hasattr(self.filter_params, key):
                    setattr(self.filter_params, key, value)
        
        # 更新优化参数
        if 'optimization' in config_data:
            opt_data = config_data['optimization']
            for key, value in opt_data.items():
                if key not in ['optimization_grid', 'constraints']:
                    if hasattr(self.optimization_params, key):
                        setattr(self.optimization_params, key, value)
            
            # 处理约束条件
            if 'constraints' in opt_data:
                constraints = opt_data['constraints']
                for key, value in constraints.items():
                    if hasattr(self.optimization_params, key):
                        setattr(self.optimization_params, key, value)
        
        # 更新监控参数
        if 'monitoring' in config_data:
            mon_data = config_data['monitoring']
            
            # 信号监控
            if 'signal_monitor' in mon_data:
                signal_mon = mon_data['signal_monitor']
                self.monitoring_params.signal_monitor_enabled = signal_mon.get('enabled', True)
                self.monitoring_params.max_signals_per_interval = signal_mon.get('max_signals_per_interval', 10)
            
            # 资金监控
            if 'capital_monitor' in mon_data:
                capital_mon = mon_data['capital_monitor']
                self.monitoring_params.capital_monitor_enabled = capital_mon.get('enabled', True)
                self.monitoring_params.max_drawdown_pct = capital_mon.get('max_drawdown_pct', 5.0)
            
            # 价格监控
            if 'price_monitor' in mon_data:
                price_mon = mon_data['price_monitor']
                self.monitoring_params.price_monitor_enabled = price_mon.get('enabled', True)
                self.monitoring_params.max_short_term_change_pct = price_mon.get('max_short_term_change_pct', 3.0)
    
    def get_strategy_params(self) -> Dict[str, Any]:
        """获取策略参数"""
        return asdict(self.strategy_params)
    
    def get_risk_manager_params(self) -> Dict[str, Any]:
        """获取风险管理参数"""
        return asdict(self.risk_params)
    
    def get_signal_filter_params(self) -> Dict[str, Any]:
        """获取信号过滤参数"""
        return asdict(self.filter_params)
    
    def get_optimization_params(self) -> Dict[str, Any]:
        """获取优化参数"""
        return asdict(self.optimization_params)
    
    def get_monitoring_params(self) -> Dict[str, Any]:
        """获取监控参数"""
        return asdict(self.monitoring_params)
    
    def get_optimization_grid(self) -> Dict[str, List[Any]]:
        """获取优化网格"""
        # ✅ 优先从统一配置文件加载
        unified_config = self.config_dir / 'smc_unified_config.json'
        if unified_config.exists():
            try:
                with open(unified_config, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                if 'optimization' in config_data and 'grid' in config_data['optimization']:
                    return config_data['optimization']['grid']
            except Exception as e:
                logger.warning(f"从统一配置加载优化网格失败: {e}")
        
        # 降级到旧配置文件
        config_path = self.config_dir / 'smc_strategy_config.json'
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                if 'optimization' in config_data and 'optimization_grid' in config_data['optimization']:
                    return config_data['optimization']['optimization_grid']
            except Exception as e:
                logger.warning(f"加载优化网格失败: {e}")
        
        # 返回默认优化网格
        return get_smc_optimization_grid()
    
    def update_strategy_params(self, **kwargs):
        """更新策略参数"""
        for key, value in kwargs.items():
            if hasattr(self.strategy_params, key):
                setattr(self.strategy_params, key, value)
            else:
                logger.warning(f"未知的策略参数: {key}")
    
    def update_risk_params(self, **kwargs):
        """更新风险管理参数"""
        for key, value in kwargs.items():
            if hasattr(self.risk_params, key):
                setattr(self.risk_params, key, value)
    
    def update_filter_params(self, **kwargs):
        """更新信号过滤参数"""
        for key, value in kwargs.items():
            if hasattr(self.filter_params, key):
                setattr(self.filter_params, key, value)
    
    def load_optimized_params(self, file_path: str) -> bool:
        """加载优化后的参数"""
        try:
            if not os.path.exists(file_path):
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    optimized_params = json.load(f)
                else:
                    # 处理文本格式的参数文件
                    content = f.read()
                    optimized_params = self._parse_text_params(content)
            
            self._update_params_from_dict(optimized_params)
            logger.info(f"已加载优化参数: {file_path}")
            return True
            
        except Exception as e:
            logger.warning(f"加载优化参数失败 {file_path}: {e}")
            return False
    
    def _parse_text_params(self, content: str) -> Dict[str, Any]:
        """解析文本格式的参数"""
        params = {}
        lines = content.strip().split('\n')
        
        for line in lines:
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # 尝试转换数据类型
                try:
                    if '.' in value:
                        value = float(value)
                    else:
                        value = int(value)
                except ValueError:
                    if value.lower() in ['true', 'false']:
                        value = value.lower() == 'true'
                
                params[key] = value
        
        return {'strategy': params}
    
    def save_config(self, file_path: str = None):
        """保存当前配置"""
        if file_path is None:
            file_path = self.config_dir / 'smc_current_config.json'
        
        config_data = {
            'strategy': self.get_strategy_params(),
            'risk_manager': self.get_risk_manager_params(),
            'signal_filter': self.get_signal_filter_params(),
            'optimization': self.get_optimization_params(),
            'monitoring': self.get_monitoring_params()
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存: {file_path}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")


def get_smc_optimization_grid() -> Dict[str, List[Any]]:
    """获取SMC策略参数优化网格"""
    return {
        # 核心SMC参数
        'swing_threshold': [0.005, 0.008, 0.012, 0.015, 0.020],
        'bos_threshold': [0.002, 0.003, 0.005, 0.008, 0.010],
        'ob_lookback': [5, 8, 10, 12, 15],
        'fvg_threshold': [0.001, 0.003, 0.005, 0.008, 0.010],
        'risk_reward_ratio': [1.5, 2.0, 2.5, 3.0, 3.5],
        
        # 技术指标参数
        'atr_periods': [10, 14, 20, 28],
        'atr_multiplier': [1.5, 2.0, 2.5, 3.0],
        
        # 风险管理参数
        'stop_loss_pct': [1.5, 2.0, 2.5, 3.0],
        'take_profit_pct': [3.0, 4.0, 5.0, 6.0],
        'max_position_pct': [5.0, 8.0, 10.0, 12.0],
        
        # 信号过滤参数
        'min_volume_ratio': [1.2, 1.5, 2.0, 2.5],
        'rsi_oversold': [25, 30, 35],
        'rsi_overbought': [65, 70, 75]
    }


def get_smc_strategy_params() -> Dict[str, Any]:
    """获取默认SMC策略参数"""
    config_manager = SMCConfigManager()
    return config_manager.get_strategy_params()


def get_smc_risk_manager_params() -> Dict[str, Any]:
    """获取默认SMC风险管理参数"""
    config_manager = SMCConfigManager()
    return config_manager.get_risk_manager_params()


def get_smc_signal_filter_params() -> Dict[str, Any]:
    """获取默认SMC信号过滤参数"""
    config_manager = SMCConfigManager()
    return config_manager.get_signal_filter_params()


def get_smc_monitoring_params() -> Dict[str, Any]:
    """获取默认SMC监控参数"""
    config_manager = SMCConfigManager()
    return config_manager.get_monitoring_params()


def load_optimized_params(file_path: str) -> Optional[Dict[str, Any]]:
    """
    从文件加载优化后的参数
    
    Parameters
    ----------
    file_path : str
        参数文件路径
        
    Returns
    -------
    Optional[Dict[str, Any]]
        加载的参数字典，失败时返回None
    """
    if not os.path.exists(file_path):
        return None
    
    try:
        if file_path.endswith('.json'):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data.get('strategy', data)  # 尝试获取strategy部分，否则返回整个数据
        else:
            # 处理文本格式
            params = {}
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if ':' in line:
                        key, value = line.strip().split(':', 1)
                        try:
                            params[key.strip()] = float(value.strip())
                        except ValueError:
                            params[key.strip()] = value.strip()
            return params
    except Exception as e:
        logger.error(f"加载优化参数失败: {e}")
        return None


# 全局配置管理器实例
_global_config_manager = None


def get_global_config_manager() -> SMCConfigManager:
    """获取全局配置管理器实例"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = SMCConfigManager()
    return _global_config_manager


# 🔧 移除重复函数定义 - 避免冗余实现
# 这些函数已在上面定义过，保持代码简洁


# 向后兼容的别名
smc_config = get_global_config_manager() 