---
description: 
globs: 
alwaysApply: true
---
---
description: ALWAYS use when writing or updating Markdown files to ensure consistent formatting and readability
globs: **/*.md
alwaysApply: false
---

# Markdown Documentation Standards

## Context
- When creating or modifying any Markdown documentation
- When establishing documentation structure and style
- When including diagrams, code blocks, or special elements in documentation

## Requirements
- Follow the official Markdown Guide for all basic and extended syntax
- Maintain clear document structure with proper heading hierarchy
- Include appropriate YAML front matter for metadata when required
- Use Mermaid diagrams for visual documentation where appropriate
- Properly format code blocks, tables, and special elements
- Maximum heading depth: 4 levels
- Indent content within XML tags by 2 spaces
- Keep tables simple and readable with proper alignment

## Examples
<example>
# Document Title

## Section Heading

Content with **bold text** and *italics*.

```typescript
function example(): void {
  console.log('Hello, Universe!');
}
```

| Name  | Type   | Description  |
|:------|:------:|-------------:|
| id    | number | Primary key  |
| name  | string | User's name  |

> 💡 **Tip:** Helpful suggestion.
</example>

<example type="invalid">
#Incorrect Heading
content without proper spacing

```
function withoutLanguageSpecified() {
}
```

| No | alignment | markers |
| or | proper | formatting |
</example>

## Critical Rules
  - Use ATX-style headings with space after hash: `# Heading`
  - Maintain proper heading hierarchy (don't skip levels)
  - Add blank line before and after headings and blocks
  - Specify language in code blocks using triple backticks
  - Use blockquotes with emoji for different types of callouts
  - Include clear titles for Mermaid diagrams using the `---` syntax
  - Keep table structure clean with proper alignment indicators
  - Format Mermaid diagrams with descriptive node labels and comments
  - Close XML tags on their own line at the parent indentation level 