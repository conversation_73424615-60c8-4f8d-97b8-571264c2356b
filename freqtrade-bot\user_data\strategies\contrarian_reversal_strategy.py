#!/usr/bin/env python3
"""
逆向反转策略 - 基于资金费率的反向交易策略
功能：利用资金费率和持仓量数据进行逆向交易，捕捉市场极端情绪反转
作者：AriQuantification
版本：1.0.0
"""

import numpy as np
import pandas as pd
import talib.abstract as ta
from datetime import datetime, timedelta
from typing import Dict, Optional
import logging

from freqtrade.strategy.interface import IStrategy
from freqtrade.strategy import DecimalParameter, IntParameter, BooleanParameter
from freqtrade.persistence import Trade
from pandas import DataFrame

# 导入自定义数据提供者
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_providers.funding_rate_data_provider import funding_provider

logger = logging.getLogger(__name__)

class ContrarianReversalStrategy(IStrategy):
    """逆向反转策略"""
    
    # 策略基本配置
    INTERFACE_VERSION = 3
    timeframe = '1m'
    
    # 策略参数将从config加载，不使用默认值
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        
        # 从config加载策略参数
        strategy_params = config.get('strategy_params', {})
        
        # 基础设置
        self.startup_candle_count = strategy_params.get('startup_candle_count', 10080)
        self.max_open_trades = strategy_params.get('max_open_trades', 3)
        self.position_adjustment_enable = strategy_params.get('position_adjustment_enable', True)
        
        # 资金费率参数
        funding_params = strategy_params.get('funding_rate_params', {})
        self.funding_extreme_threshold = funding_params.get('extreme_positive_threshold')
        self.funding_extreme_negative_threshold = funding_params.get('extreme_negative_threshold')
        
        # RSI参数
        rsi_params = strategy_params.get('rsi_params', {})
        self.rsi_period = rsi_params.get('period')
        self.rsi_overbought = rsi_params.get('overbought_threshold')
        self.rsi_oversold = rsi_params.get('oversold_threshold')
        
        # 持仓量参数
        oi_params = strategy_params.get('open_interest_params', {})
        self.oi_decline_threshold = oi_params.get('decline_threshold')
        self.oi_expand_threshold = oi_params.get('expand_threshold')
        
        # 价格动量参数
        momentum_params = strategy_params.get('price_momentum_params', {})
        self.price_momentum_period = momentum_params.get('momentum_period')
        
        # 技术指标参数
        tech_params = strategy_params.get('technical_indicators', {})
        self.bollinger_period = tech_params.get('bollinger_period')
        self.volume_ratio_threshold = tech_params.get('volume_ratio_threshold')
        self.volume_sma_period = tech_params.get('volume_sma_period')
        
        # 风险管理参数
        risk_params = strategy_params.get('risk_management', {})
        self.use_dynamic_stoploss = risk_params.get('use_dynamic_stoploss')
        self.atr_period = risk_params.get('atr_period')
        self.atr_multiplier = risk_params.get('atr_multiplier')
        self.max_hold_hours = risk_params.get('max_hold_hours')
        
        # 信号设置
        signal_params = strategy_params.get('signal_settings', {})
        self.enable_bull_exhaustion = signal_params.get('enable_bull_exhaustion')
        self.enable_bear_capitulation = signal_params.get('enable_bear_capitulation')
        self.enable_short_squeeze = signal_params.get('enable_short_squeeze')
        self.enable_long_squeeze = signal_params.get('enable_long_squeeze')
        
        # 退出条件参数
        exit_params = strategy_params.get('exit_conditions', {})
        self.rsi_exit_overbought = exit_params.get('rsi_exit_overbought')
        self.rsi_exit_oversold = exit_params.get('rsi_exit_oversold')
        self.funding_rate_exit_positive = exit_params.get('funding_rate_exit_positive')
        self.funding_rate_exit_negative = exit_params.get('funding_rate_exit_negative')
        
        # 检查必要参数是否存在
        self._validate_required_params()
    
    def _validate_required_params(self):
        """验证必要参数是否存在"""
        required_params = [
            ('funding_extreme_threshold', self.funding_extreme_threshold),
            ('funding_extreme_negative_threshold', self.funding_extreme_negative_threshold),
            ('rsi_period', self.rsi_period),
            ('rsi_overbought', self.rsi_overbought),
            ('rsi_oversold', self.rsi_oversold),
            ('oi_decline_threshold', self.oi_decline_threshold),
            ('oi_expand_threshold', self.oi_expand_threshold),
            ('price_momentum_period', self.price_momentum_period),
            ('atr_period', self.atr_period),
            ('atr_multiplier', self.atr_multiplier),
            ('max_hold_hours', self.max_hold_hours)
        ]
        
        missing_params = []
        for param_name, param_value in required_params:
            if param_value is None:
                missing_params.append(param_name)
        
        if missing_params:
            raise ValueError(f"策略配置中缺少必要参数: {', '.join(missing_params)}")
    
    def populate_indicators(self, dataframe: DataFrame, metadata: Dict) -> DataFrame:
        """
        添加技术指标
        """
        
        # 获取交易对
        pair = metadata['pair']
        
        # 检测是否为回测模式
        is_backtest = self.dp is None or self.dp.runmode.value in ['backtest', 'plot']
        
        # 添加资金费率指标
        try:
            dataframe = funding_provider.get_funding_indicators(
                dataframe=dataframe,
                pair=pair,
                is_backtest=is_backtest,
                extreme_positive_threshold=self.funding_extreme_threshold,
                extreme_negative_threshold=self.funding_extreme_negative_threshold
            )
        except Exception as e:
            logger.warning(f"添加资金费率指标失败: {e}")
            # 添加默认的空指标
            self._add_default_funding_indicators(dataframe)
        
        # 基础技术指标
        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period)
        
        # 移动平均线
        dataframe['ema_20'] = ta.EMA(dataframe, timeperiod=20)
        dataframe['ema_50'] = ta.EMA(dataframe, timeperiod=50)
        dataframe['sma_200'] = ta.SMA(dataframe, timeperiod=200)
        
        # ATR (平均真实波幅)
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period)
        
        # 布林带
        bollinger = ta.BBANDS(dataframe, timeperiod=self.bollinger_period, nbdevup=2.0, nbdevdn=2.0)
        dataframe['bb_lower'] = bollinger['lowerband']
        dataframe['bb_middle'] = bollinger['middleband']
        dataframe['bb_upper'] = bollinger['upperband']
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        
        # 价格动量指标
        dataframe['price_momentum'] = dataframe['close'].pct_change(self.price_momentum_period)
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=self.volume_sma_period).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # 价格位置
        dataframe['price_vs_ma20'] = (dataframe['close'] / dataframe['ema_20'] - 1) * 100
        dataframe['price_vs_ma50'] = (dataframe['close'] / dataframe['ema_50'] - 1) * 100
        
        # RSI背离指标
        dataframe = self._calculate_rsi_divergence(dataframe)
        
        # 计算核心交易信号
        dataframe = self._calculate_signal_matrix(dataframe)
        
        return dataframe
    
    def _add_default_funding_indicators(self, dataframe: DataFrame):
        """添加默认的资金费率指标（用于回测失败时）"""
        dataframe['funding_rate'] = 0.0
        dataframe['oi_total'] = 0.0
        dataframe['oi_change'] = 0.0
        dataframe['funding_extreme_positive'] = False
        dataframe['funding_extreme_negative'] = False
        dataframe['oi_declining'] = False
        dataframe['oi_expanding'] = False
        dataframe['funding_bullish_exhaustion'] = False
        dataframe['funding_bearish_capitulation'] = False
    
    def _calculate_rsi_divergence(self, dataframe: DataFrame) -> DataFrame:
        """计算RSI背离"""
        
        # 寻找价格高点和低点
        dataframe['price_high'] = dataframe['high'].rolling(window=20).max()
        dataframe['price_low'] = dataframe['low'].rolling(window=20).min()
        
        # 寻找RSI高点和低点
        dataframe['rsi_high'] = dataframe['rsi'].rolling(window=20).max()
        dataframe['rsi_low'] = dataframe['rsi'].rolling(window=20).min()
        
        # 顶背离：价格新高但RSI未新高
        dataframe['rsi_bearish_divergence'] = (
            (dataframe['high'] >= dataframe['price_high'].shift(1)) &
            (dataframe['rsi'] < dataframe['rsi_high'].shift(1)) &
            (dataframe['rsi'] > 60)
        )
        
        # 底背离：价格新低但RSI未新低  
        dataframe['rsi_bullish_divergence'] = (
            (dataframe['low'] <= dataframe['price_low'].shift(1)) &
            (dataframe['rsi'] > dataframe['rsi_low'].shift(1)) &
            (dataframe['rsi'] < 40)
        )
        
        return dataframe
    
    def _calculate_signal_matrix(self, dataframe: DataFrame) -> DataFrame:
        """计算四种核心交易信号"""
        
        # 1. 牛市衰竭信号（做空信号）
        dataframe['bull_exhaustion_signal'] = (
            self.enable_bull_exhaustion &
            dataframe['rsi_bearish_divergence'] &  # RSI顶背离
            (dataframe['funding_rate'] > self.funding_extreme_threshold) &  # 极端正资金费率
            dataframe['oi_declining'] &  # 持仓量下降
            (dataframe['rsi'] > self.rsi_overbought) &  # RSI超买
            (dataframe['close'] > dataframe['ema_20']) &  # 价格在均线上方
            (dataframe['bb_width'] > dataframe['bb_width'].rolling(20).mean())  # 波动率扩张
        )
        
        # 2. 熊市投降信号（做多信号）
        dataframe['bear_capitulation_signal'] = (
            self.enable_bear_capitulation &
            dataframe['rsi_bullish_divergence'] &  # RSI底背离
            (dataframe['funding_rate'] < self.funding_extreme_negative_threshold) &  # 极端负资金费率
            dataframe['oi_declining'] &  # 持仓量下降
            (dataframe['rsi'] < self.rsi_oversold) &  # RSI超卖
            (dataframe['close'] < dataframe['ema_20']) &  # 价格在均线下方
            (dataframe['volume_ratio'] > self.volume_ratio_threshold)  # 成交量放大
        )
        
        # 3. 空头挤压预备信号（做多信号）
        dataframe['short_squeeze_signal'] = (
            self.enable_short_squeeze &
            (dataframe['funding_rate'] < self.funding_extreme_negative_threshold) &  # 极端负资金费率
            dataframe['oi_expanding'] &  # 持仓量上升（空头建仓）
            (dataframe['rsi'] < 40) &  # RSI偏低
            (dataframe['close'] < dataframe['bb_lower']) &  # 价格突破布林带下轨
            (dataframe['price_momentum'] < -0.05)  # 价格下跌动量
        )
        
        # 4. 多头挤压预备信号（做空信号）
        dataframe['long_squeeze_signal'] = (
            self.enable_long_squeeze &
            (dataframe['funding_rate'] > self.funding_extreme_threshold) &  # 极端正资金费率
            dataframe['oi_expanding'] &  # 持仓量上升（多头建仓）
            (dataframe['rsi'] > 60) &  # RSI偏高
            (dataframe['close'] > dataframe['bb_upper']) &  # 价格突破布林带上轨
            (dataframe['price_momentum'] > 0.05)  # 价格上涨动量
        )
        
        # 合并买入信号
        dataframe['buy_signal'] = (
            dataframe['bear_capitulation_signal'] |
            dataframe['short_squeeze_signal']
        )
        
        # 合并卖出信号
        dataframe['sell_signal'] = (
            dataframe['bull_exhaustion_signal'] |
            dataframe['long_squeeze_signal']
        )
        
        # 信号强度评分
        dataframe['signal_strength'] = (
            dataframe['bull_exhaustion_signal'].astype(int) +
            dataframe['bear_capitulation_signal'].astype(int) +
            dataframe['short_squeeze_signal'].astype(int) +
            dataframe['long_squeeze_signal'].astype(int)
        )
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: Dict) -> DataFrame:
        """
        买入信号逻辑
        """
        
        dataframe.loc[
            (
                dataframe['buy_signal'] &
                (dataframe['volume'] > 0)
            ),
            ['enter_long', 'enter_tag']
        ] = (1, 'funding_reversal_long')
        
        dataframe.loc[
            (
                dataframe['sell_signal'] &
                (dataframe['volume'] > 0)
            ),
            ['enter_short', 'enter_tag']
        ] = (1, 'funding_reversal_short')
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: Dict) -> DataFrame:
        """
        卖出信号逻辑
        """
        
        # 多头平仓条件
        dataframe.loc[
            (
                (dataframe['rsi'] > self.rsi_exit_overbought) |  # RSI超买
                (dataframe['funding_rate'] > self.funding_rate_exit_positive) |  # 资金费率转正且较高
                (dataframe['close'] < dataframe['ema_20']) |  # 跌破20均线
                dataframe['bull_exhaustion_signal']  # 出现牛市衰竭信号
            ),
            ['exit_long', 'exit_tag']
        ] = (1, 'long_exit')
        
        # 空头平仓条件
        dataframe.loc[
            (
                (dataframe['rsi'] < self.rsi_exit_oversold) |  # RSI超卖
                (dataframe['funding_rate'] < self.funding_rate_exit_negative) |  # 资金费率转负且较低
                (dataframe['close'] > dataframe['ema_20']) |  # 突破20均线
                dataframe['bear_capitulation_signal']  # 出现熊市投降信号
            ),
            ['exit_short', 'exit_tag']
        ] = (1, 'short_exit')
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        动态止损逻辑
        """
        
        if not self.use_dynamic_stoploss:
            return self.stoploss
        
        # 获取最新数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        latest_candle = dataframe.iloc[-1]
        
        # ATR基础止损
        atr_value = latest_candle['atr']
        if trade.is_short:
            atr_stoploss = (current_rate + atr_value * self.atr_multiplier) / trade.open_rate - 1
        else:
            atr_stoploss = (current_rate - atr_value * self.atr_multiplier) / trade.open_rate - 1
        
        # 时间止损
        hours_open = (current_time - trade.open_date_utc).total_seconds() / 3600
        if hours_open > self.max_hold_hours:
            return 0.01  # 强制平仓
        
        # 逻辑失效止损
        if trade.is_short and latest_candle['bear_capitulation_signal']:
            return 0.01  # 空头遇到熊市投降信号
        elif not trade.is_short and latest_candle['bull_exhaustion_signal']:
            return 0.01  # 多头遇到牛市衰竭信号
        
        return max(atr_stoploss, self.stoploss)
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                side: str, **kwargs) -> float:
        """
        杠杆设置
        """
        return min(proposed_leverage, 3.0)  # 最大3倍杠杆 