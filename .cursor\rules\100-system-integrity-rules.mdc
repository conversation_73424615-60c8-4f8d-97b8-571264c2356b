---
description: 
globs: 
alwaysApply: true
---
# System Integrity and Anti-Duplication Rules

## Context
- When developing new features or solving problems in AriQuantification
- When encountering technical challenges or implementation requests
- When considering architectural changes or extensions
- When working with the quantitative trading system components

## Requirements
- **FIRST** evaluate existing system functionality before any new implementation
- Analyze current modules: TradingEngine, adapters, monitoring, risk management
- Check for existing interfaces: BaseTradeAdapter, TradeSignal, TradeExecution models
- Identify root causes of problems rather than working around them
- Leverage existing standardized components and frameworks
- Extend current architecture rather than rebuilding from scratch

## Examples
<example>
User needs new exchange connection:
1. Check existing CCXTAdapter and FreqtradeAdapter
2. Evaluate BaseTradeAdapter interface compatibility  
3. Extend CCXTAdapter for new exchange rather than creating new adapter
4. Use existing TradingEngine signal processing
</example>

<example type="invalid">
User needs new exchange connection:
1. Create completely new adapter without checking existing ones
2. Duplicate signal processing logic
3. Implement new execution engine for specific exchange
4. Bypass existing monitoring and risk management
</example>

## Critical Rules
- **NEVER** re-implement existing core modules (TradingEngine, BaseTradeAdapter, monitoring system)
- **ALWAYS** check .ai/arch.md for current system capabilities first
- **ALWAYS** solve problems at their root cause, never work around core issues
- **ALWAYS** use existing standardized interfaces and data models
- **ALWAYS** leverage current high-performance components (VectorBT, CCXT, etc.)
- **ALWAYS** extend rather than replace existing functionality
- **ALWAYS** maintain backward compatibility with existing interfaces

