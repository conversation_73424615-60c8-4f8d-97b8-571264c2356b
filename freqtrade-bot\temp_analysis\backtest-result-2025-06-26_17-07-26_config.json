{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 2, "stake_currency": "USDC", "stake_amount": "unlimited", "tradable_balance_ratio": 0.95, "dry_run": true, "dry_run_wallet": 100000, "cancel_open_orders_on_exit": true, "trading_mode": "futures", "margin_mode": "isolated", "max_leverage": 3.0, "adl_strategy_params": {"atr_period": 14, "atr_sma_period": 50, "atr_multiplier": 1.8, "zscore_period": 50, "zscore_threshold": 2.0, "atr_stop_multiplier": 0.5, "atr_profit_multiplier": 0.55, "step3_optimization": {"enable": true, "signal_tier_system": {"tier1_strength_min": 2.0, "tier2_strength_min": 1.65, "tier3_strength_min": 1.3, "tier1_max_trades": 3, "tier2_max_trades": 2, "tier3_max_trades": 1}, "aggressive_time_management": {"enable": true, "tier1_time_limits": [20, 25, 30, 35, 40], "tier2_time_limits": [15, 20, 25, 30, 35], "tier3_time_limits": [10, 15, 20, 25, 30], "profit_protection_threshold": 0.5, "profit_protection_time_limit": 15, "quick_loss_threshold": -1.0, "quick_loss_time_limit": 8}, "momentum_confirmation": {"enable": true, "min_acceleration_ratio": 1.3, "volume_burst_multiplier": 1.2, "rsi_oversold_threshold": 35, "rsi_overbought_threshold": 65}, "profit_protection": {"enable": true, "min_profit_for_protection": 0.003, "protected_time_limit": 8, "quick_exit_loss_threshold": -0.008}, "signal_quality_control": {"consecutive_loss_limit": 3, "daily_signal_limit": 15, "cooldown_after_loss": 2}, "signal_quality_filter": {"min_signal_strength": 0.6, "volume_confirmation": true, "avoid_consecutive_signals": true, "signal_cooldown_minutes": 10}}, "minimal_roi": {"0": 0.08, "5": 0.05, "10": 0.03, "15": 0}, "use_custom_stoploss": false, "leverage_revolution": {"enable": true, "base_leverage": 5, "tier1_leverage": 8, "tier2_leverage": 6, "tier3_leverage": 5, "volatility_adjustment": true, "max_leverage": 8, "min_leverage": 3, "risk_scaling": true}, "enhanced_risk_management": {"enable": true, "leverage_based_stoploss": true, "quick_exit_multiplier": 0.9, "dynamic_position_sizing": true, "volatility_protection": true}, "dynamic_position_management": {"enable": true, "base_position_ratio": 0.25, "tier1_position_ratio": 0.4, "tier2_position_ratio": 0.3, "tier3_position_ratio": 0.25, "max_total_exposure": 0.95, "adaptive_sizing": true}, "multi_exit_system": {"enable": true, "partial_exit_levels": [0.25, 0.5, 0.75], "tier1_profit_targets": [0.01, 0.02, 0.035, 0.055], "tier2_profit_targets": [0.006, 0.012, 0.022, 0.035], "tier3_profit_targets": [0.005, 0.012, 0.02, 0.03], "volatility_adaptive": true, "signal_maturity_tracking": true}, "enhanced_signal_quality": {"enable": true, "negative_signal_filter": true, "signal_momentum_tracking": true, "early_warning_system": true, "adaptive_profit_targets": true}}, "liquidation_buffer": 0.05, "timeframe": "5m", "strategy": "ADLAnticipationStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 60, "exit": 60, "exit_timeout_count": 0, "unit": "seconds"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "REDACTED", "secret": "REDACTED", "password": "REDACTED", "sandbox": false, "ccxt_config": {"enableRateLimit": true, "rateLimit": 200, "options": {"defaultType": "future"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200}, "pair_whitelist": ["BTC/USDC:USDC", "ETH/USDC:USDC", "SOL/USDC:USDC", "BNB/USDC:USDC", "XRP/USDC:USDC", "ADA/USDC:USDC", "AVAX/USDC:USDC", "LINK/USDC:USDC", "NEAR/USDC:USDC", "WLD/USDC:USDC", "ARB/USDC:USDC", "SUI/USDC:USDC", "ENA/USDC:USDC", "WIF/USDC:USDC", "TRUMP/USDC:USDC", "DOGE/USDC:USDC"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "info", "enable_openapi": true, "jwt_secret_key": "db8c839c7e69d7ed25e43b836505ea340d7f6e496eb76175eae10c323e490988095dc7328577f724bc3ebb28db73dd5d0501447087d5fb6a0cd995c37aaf52fe", "ws_token": "50JKyE6VEqvufqvHpM87wMO90snMCXLeuEj7iKa8Sdg", "CORS_origins": [], "username": "AriFreqtrade", "password": "REDACTED"}, "bot_name": "ADL-Anticipation-Backtest", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}, "order_types": {"entry": "limit", "exit": "limit", "stoploss": "limit", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "dataformat_ohlcv": "json", "dataformat_trades": "json", "position_adjustment_enable": true, "max_entry_position_adjustment": 4, "config_files": ["config_adl_backtest.json"]}