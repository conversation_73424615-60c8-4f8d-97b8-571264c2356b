#!/usr/bin/env python3
"""
奇美拉策略参数优化脚本
功能：自动化参数优化，找到最佳TFI和SPI参数组合
作者：AriQuantification
版本：1.0.0
基于：奇美拉策略优化需求
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

# 添加freqtrade路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ChimeraOptimization')

class ChimeraOptimizer:
    """奇美拉策略参数优化器"""
    
    def __init__(self, config_path: str = "config_chimera_backtest.json"):
        """
        初始化优化器
        
        Args:
            config_path: freqtrade配置文件路径
        """
        self.config_path = Path(config_path)
        self.base_config = self._load_config()
        
        # 优化结果存储
        self.results_dir = Path("optimization_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 优化参数范围定义
        self.optimization_space = {
            'tfi_entry_threshold_long': np.arange(-4.0, -1.5, 0.2),
            'tfi_entry_threshold_short': np.arange(1.5, 4.0, 0.2),
            'tfi_exit_threshold': np.arange(0.3, 0.8, 0.1),
            'spi_filter_threshold': np.arange(1.5, 3.0, 0.2),
            'atr_multiplier': np.arange(1.0, 2.5, 0.2),
            'kelly_fraction': np.arange(0.1, 0.8, 0.1),
            'max_holding_minutes': range(15, 61, 5)
        }
        
        logger.info("奇美拉策略优化器初始化完成")
        logger.info(f"配置文件: {self.config_path}")
        logger.info(f"结果目录: {self.results_dir}")
    
    def _load_config(self) -> Dict:
        """加载基础配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def generate_parameter_combinations(self, sample_size: int = None) -> List[Dict]:
        """生成参数组合"""
        
        # 如果设置了采样大小，使用随机采样
        if sample_size:
            combinations = []
            for _ in range(sample_size):
                combination = {}
                for param, values in self.optimization_space.items():
                    combination[param] = np.random.choice(values)
                combinations.append(combination)
        else:
            # 全网格搜索（警告：组合数量巨大）
            param_names = list(self.optimization_space.keys())
            param_values = list(self.optimization_space.values())
            
            combinations = []
            for combination in itertools.product(*param_values):
                param_dict = dict(zip(param_names, combination))
                combinations.append(param_dict)
        
        logger.info(f"生成了 {len(combinations)} 个参数组合")
        return combinations
    
    def create_optimization_config(self, params: Dict) -> Dict:
        """创建包含特定参数的配置"""
        config = self.base_config.copy()
        
        # 更新奇美拉配置部分
        if 'chimera_config' not in config:
            config['chimera_config'] = {}
        
        # 更新参数
        config['chimera_config']['optimization_params'] = params
        
        return config
    
    def run_single_backtest(self, params: Dict, timerange: str = None) -> Dict:
        """运行单次回测"""
        try:
            # 创建临时配置文件
            temp_config = self.create_optimization_config(params)
            temp_config_path = self.results_dir / f"temp_config_{os.getpid()}.json"
            
            with open(temp_config_path, 'w', encoding='utf-8') as f:
                json.dump(temp_config, f, indent=2)
            
            # 构建freqtrade命令
            freqtrade_cmd = [
                "freqtrade", "backtesting",
                "-c", str(temp_config_path),
                "--strategy", "ChimeraStrategy",
                "--export", "trades",
                "--breakdown", "day",
                "--cache", "none"
            ]
            
            if timerange:
                freqtrade_cmd.extend(["--timerange", timerange])
            
            # 运行回测
            import subprocess
            result = subprocess.run(
                freqtrade_cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            # 清理临时文件
            if temp_config_path.exists():
                temp_config_path.unlink()
            
            if result.returncode != 0:
                logger.error(f"回测失败: {result.stderr}")
                return {'params': params, 'error': result.stderr}
            
            # 解析回测结果
            backtest_results = self._parse_backtest_output(result.stdout)
            backtest_results['params'] = params
            
            return backtest_results
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            return {'params': params, 'error': str(e)}
    
    def _parse_backtest_output(self, output: str) -> Dict:
        """解析freqtrade回测输出"""
        try:
            # 这里需要根据freqtrade输出格式解析
            # 简化版本，实际需要更复杂的解析逻辑
            lines = output.split('\n')
            
            results = {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'profit_total': 0.0,
                'profit_total_pct': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'sortino_ratio': 0.0,
                'calmar_ratio': 0.0,
                'avg_profit': 0.0,
                'avg_duration': 0.0
            }
            
            for line in lines:
                if "Total trades" in line:
                    results['total_trades'] = int(line.split(':')[1].strip())
                elif "Win  :" in line:
                    parts = line.split()
                    results['winning_trades'] = int(parts[2])
                    if len(parts) > 3:
                        results['win_rate'] = float(parts[3].rstrip('%')) / 100
                elif "Total profit" in line and "%" in line:
                    # 解析总利润百分比
                    profit_part = line.split('%')[0].split()[-1]
                    results['profit_total_pct'] = float(profit_part)
                elif "Max Drawdown" in line:
                    # 解析最大回撤
                    dd_part = line.split('%')[0].split()[-1]
                    results['max_drawdown'] = float(dd_part)
            
            # 计算失败交易数
            results['losing_trades'] = results['total_trades'] - results['winning_trades']
            
            # 计算平均利润
            if results['total_trades'] > 0:
                results['avg_profit'] = results['profit_total_pct'] / results['total_trades']
            
            return results
            
        except Exception as e:
            logger.warning(f"解析回测输出失败: {e}")
            return {'parsing_error': str(e)}
    
    def calculate_fitness_score(self, results: Dict) -> float:
        """计算适应度分数"""
        if 'error' in results or 'parsing_error' in results:
            return -1000  # 惩罚错误的参数组合
        
        # 多目标优化分数
        # 权重：利润(40%) + 夏普比率(30%) + 胜率(20%) + 最大回撤(10%)
        
        profit_score = results.get('profit_total_pct', 0) * 0.4
        
        # 夏普比率分数（如果有的话）
        sharpe_score = results.get('sharpe_ratio', 0) * 30 * 0.3
        
        # 胜率分数
        win_rate_score = results.get('win_rate', 0) * 100 * 0.2
        
        # 最大回撤惩罚（回撤越大惩罚越重）
        max_dd = abs(results.get('max_drawdown', 0))
        drawdown_penalty = max(0, max_dd - 5) * 2 * 0.1  # 超过5%开始惩罚
        
        # 交易数量过滤（太少的交易不可靠）
        trade_count = results.get('total_trades', 0)
        if trade_count < 10:
            return -500  # 交易太少，不可靠
        
        fitness = profit_score + sharpe_score + win_rate_score - drawdown_penalty
        
        return fitness
    
    def optimize_parameters(self, sample_size: int = 1000, 
                          timerange: str = None, 
                          n_workers: int = None) -> pd.DataFrame:
        """执行参数优化"""
        
        if n_workers is None:
            n_workers = min(multiprocessing.cpu_count() - 1, 8)
        
        logger.info(f"开始参数优化，采样大小: {sample_size}, 工作进程: {n_workers}")
        
        # 生成参数组合
        param_combinations = self.generate_parameter_combinations(sample_size)
        
        # 并行回测
        results = []
        completed_count = 0
        
        with ProcessPoolExecutor(max_workers=n_workers) as executor:
            # 提交所有任务
            future_to_params = {
                executor.submit(self.run_single_backtest, params, timerange): params
                for params in param_combinations
            }
            
            # 收集结果
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    result = future.result()
                    fitness = self.calculate_fitness_score(result)
                    result['fitness_score'] = fitness
                    results.append(result)
                    
                    completed_count += 1
                    if completed_count % 50 == 0:
                        logger.info(f"完成 {completed_count}/{len(param_combinations)} 个回测")
                        
                except Exception as e:
                    logger.error(f"回测任务异常: {e}")
                    results.append({
                        'params': params,
                        'error': str(e),
                        'fitness_score': -1000
                    })
        
        # 转换为DataFrame并排序
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('fitness_score', ascending=False)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"chimera_optimization_results_{timestamp}.csv"
        results_df.to_csv(results_file, index=False)
        
        logger.info(f"优化完成！结果保存到: {results_file}")
        logger.info(f"最佳适应度分数: {results_df.iloc[0]['fitness_score']:.2f}")
        
        return results_df
    
    def generate_optimization_report(self, results_df: pd.DataFrame, 
                                   top_n: int = 10) -> str:
        """生成优化报告"""
        
        report = []
        report.append("=" * 80)
        report.append("奇美拉策略参数优化报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"总测试组合: {len(results_df)}")
        report.append("")
        
        # 成功回测统计
        successful_tests = results_df[results_df['fitness_score'] > -1000]
        report.append(f"成功回测: {len(successful_tests)}/{len(results_df)} "
                     f"({len(successful_tests)/len(results_df)*100:.1f}%)")
        report.append("")
        
        # Top N结果
        report.append(f"Top {top_n} 参数组合:")
        report.append("-" * 80)
        
        for i, (_, row) in enumerate(results_df.head(top_n).iterrows(), 1):
            if 'error' in row:
                continue
                
            report.append(f"\n#{i} - 适应度分数: {row.get('fitness_score', 0):.2f}")
            
            # 参数
            if 'params' in row and isinstance(row['params'], dict):
                report.append("参数:")
                for param, value in row['params'].items():
                    report.append(f"  {param}: {value}")
            
            # 性能指标
            report.append("性能指标:")
            metrics = [
                ('总交易数', 'total_trades', ''),
                ('胜率', 'win_rate', '%'),
                ('总利润', 'profit_total_pct', '%'),
                ('最大回撤', 'max_drawdown', '%'),
                ('平均利润', 'avg_profit', '%')
            ]
            
            for name, key, unit in metrics:
                value = row.get(key, 0)
                if unit == '%':
                    report.append(f"  {name}: {value:.2f}{unit}")
                else:
                    report.append(f"  {name}: {value}")
        
        # 参数敏感性分析
        report.append("\n" + "=" * 80)
        report.append("参数敏感性分析")
        report.append("=" * 80)
        
        successful_df = results_df[results_df['fitness_score'] > -1000].copy()
        
        if len(successful_df) > 0:
            for param in self.optimization_space.keys():
                if 'params' in successful_df.columns:
                    # 这里需要展开params字典
                    param_values = []
                    fitness_values = []
                    
                    for _, row in successful_df.iterrows():
                        if isinstance(row['params'], dict) and param in row['params']:
                            param_values.append(row['params'][param])
                            fitness_values.append(row['fitness_score'])
                    
                    if param_values and fitness_values:
                        correlation = np.corrcoef(param_values, fitness_values)[0, 1]
                        if not np.isnan(correlation):
                            report.append(f"{param}: 相关系数 {correlation:.3f}")
        
        return "\n".join(report)
    
    def save_best_config(self, results_df: pd.DataFrame, 
                        output_path: str = "config_chimera_optimized.json"):
        """保存最佳参数配置"""
        
        if len(results_df) == 0:
            logger.error("没有优化结果可保存")
            return
        
        best_result = results_df.iloc[0]
        
        if 'error' in best_result:
            logger.error("最佳结果包含错误，无法保存配置")
            return
        
        # 创建优化后的配置
        optimized_config = self.base_config.copy()
        
        # 更新参数
        if 'params' in best_result and isinstance(best_result['params'], dict):
            if 'chimera_config' not in optimized_config:
                optimized_config['chimera_config'] = {}
            
            optimized_config['chimera_config']['optimized_params'] = best_result['params']
            optimized_config['chimera_config']['optimization_results'] = {
                'fitness_score': float(best_result['fitness_score']),
                'total_trades': int(best_result.get('total_trades', 0)),
                'win_rate': float(best_result.get('win_rate', 0)),
                'profit_total_pct': float(best_result.get('profit_total_pct', 0)),
                'max_drawdown': float(best_result.get('max_drawdown', 0)),
                'optimization_date': datetime.now().isoformat()
            }
        
        # 保存配置
        output_path = Path(output_path)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(optimized_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"最佳配置已保存到: {output_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='奇美拉策略参数优化')
    parser.add_argument('--config', '-c', default='config_chimera_backtest.json',
                       help='配置文件路径')
    parser.add_argument('--sample-size', '-n', type=int, default=1000,
                       help='采样大小')
    parser.add_argument('--timerange', '-t', 
                       help='时间范围 (格式: 20230101-20231231)')
    parser.add_argument('--workers', '-w', type=int,
                       help='并行工作进程数')
    parser.add_argument('--top-n', type=int, default=10,
                       help='报告中显示的顶部结果数量')
    
    args = parser.parse_args()
    
    try:
        # 创建优化器
        optimizer = ChimeraOptimizer(args.config)
        
        # 执行优化
        logger.info("开始奇美拉策略参数优化...")
        results_df = optimizer.optimize_parameters(
            sample_size=args.sample_size,
            timerange=args.timerange,
            n_workers=args.workers
        )
        
        # 生成报告
        report = optimizer.generate_optimization_report(results_df, args.top_n)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = optimizer.results_dir / f"optimization_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        logger.info(f"详细报告已保存到: {report_file}")
        
        # 保存最佳配置
        optimizer.save_best_config(results_df)
        
        logger.info("参数优化完成！")
        
    except KeyboardInterrupt:
        logger.info("用户中断优化")
    except Exception as e:
        logger.error(f"优化过程发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 