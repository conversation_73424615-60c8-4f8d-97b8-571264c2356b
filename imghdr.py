"""
兼容Python 3.13的imghdr模块替代品
用于修复freqtrade telegram集成问题
"""

import struct
from typing import Optional, BinaryIO


def what(file, h=None) -> Optional[str]:
    """确定图像文件格式"""
    if isinstance(file, (str, bytes)):
        with open(file, 'rb') as f:
            return what(f, h)
    
    if h is None:
        h = file.read(32)
        file.seek(0)
    
    # 检测各种图像格式
    for test_func in [test_jpeg, test_png, test_gif, test_bmp, test_webp]:
        result = test_func(h)
        if result:
            return result
    
    return None


def test_jpeg(h: bytes) -> Optional[str]:
    """检测JPEG格式"""
    if h[:3] == b'\xff\xd8\xff':
        return 'jpeg'
    return None


def test_png(h: bytes) -> Optional[str]:
    """检测PNG格式"""
    if h[:8] == b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a':
        return 'png'
    return None


def test_gif(h: bytes) -> Optional[str]:
    """检测GIF格式"""
    if h[:6] in (b'GIF87a', b'GIF89a'):
        return 'gif'
    return None


def test_bmp(h: bytes) -> Optional[str]:
    """检测BMP格式"""
    if h[:2] == b'BM':
        return 'bmp'
    return None


def test_webp(h: bytes) -> Optional[str]:
    """检测WebP格式"""
    if h[:4] == b'RIFF' and h[8:12] == b'WEBP':
        return 'webp'
    return None 