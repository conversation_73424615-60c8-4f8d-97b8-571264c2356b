#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FreqTrade 通用监控系统

自动检测并监控当前运行的FreqTrade策略，提供实时监控和风险管理功能。
支持任何策略类型，无需手动配置策略名称。
"""

import os
import sys
import json
import logging
import threading
import time
import sqlite3
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入风险监控组件
try:
    from risk.monitoring.real_time import TradingSignalMonitor, CapitalChangeMonitor, PriceVolatilityMonitor
    from risk.monitoring.alerts import AlertMonitor, SeverityBasedAlertRule, ThresholdAlertRule
    from risk.monitoring.notification import LoggingHandler, FileStorageHandler
    RISK_MONITORING_AVAILABLE = True
except ImportError:
    RISK_MONITORING_AVAILABLE = False
    logging.warning("风险监控模块不可用，将使用基础监控功能")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FreqtradeMonitor:
    """
    FreqTrade 通用监控系统

    自动检测并监控当前运行的FreqTrade策略，支持任何策略类型。
    提供实时监控、风险管理和性能分析功能。
    """

    def __init__(self, freqtrade_dir: str = "freqtrade-bot", config_file: str = None):
        """
        初始化监控系统

        Parameters
        ----------
        freqtrade_dir : str
            FreqTrade工作目录路径
        config_file : str, optional
            指定FreqTrade配置文件名，如果不指定则自动检测
        """
        self.freqtrade_dir = freqtrade_dir
        self.config_file = config_file
        self.config = self._detect_freqtrade_config()
        self.current_strategy = None
        self.current_pairs = []

        # 监控组件
        self.signal_monitor = None
        self.capital_monitor = None
        self.price_monitor = None
        self.alert_monitor = None

        # 运行状态
        self.is_running = False
        self.monitor_thread = None

        # 自动检测当前运行的策略
        self._detect_current_strategy()

        logger.info("✅ FreqTrade 通用监控系统初始化完成")
        if self.current_strategy:
            logger.info(f"🎯 检测到当前策略: {self.current_strategy}")
        else:
            logger.warning("⚠️ 未检测到运行中的策略")
    
    def _detect_freqtrade_config(self) -> Dict[str, Any]:
        """自动检测FreqTrade配置"""
        # 如果指定了配置文件，优先使用
        if self.config_file:
            config_path = os.path.join(self.freqtrade_dir, self.config_file)
            if os.path.exists(config_path):
                logger.info(f"📄 使用指定的FreqTrade配置文件: {config_path}")
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    return config
                except Exception as e:
                    logger.error(f"❌ 读取指定配置文件失败: {e}")

        # 自动检测配置文件，优先检测API服务器启用的配置
        config_files = [
            os.path.join(self.freqtrade_dir, "config.json"),
            os.path.join(self.freqtrade_dir, "config_adl_backtest.json"),
            "config.json"
        ]

        # 首先检查哪个配置文件启用了API服务器
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    # 检查API服务器是否启用
                    api_config = config.get("api_server", {})
                    if api_config.get("enabled", False):
                        logger.info(f"📄 检测到启用API服务器的FreqTrade配置文件: {config_file}")
                        return config
                except Exception as e:
                    logger.warning(f"⚠️ 配置文件读取失败 {config_file}: {e}")
                    continue

        # 如果没有找到启用API的配置，使用第一个可用的配置
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    logger.info(f"📄 检测到FreqTrade配置文件: {config_file}")
                    return config
                except Exception as e:
                    logger.warning(f"⚠️ 配置文件读取失败 {config_file}: {e}")
                    continue

        logger.warning("⚠️ 未找到FreqTrade配置文件，使用默认配置")
        return self._get_default_config()

    def _detect_current_strategy(self):
        """自动检测当前运行的策略"""
        try:
            # 方法1: 从API获取当前策略信息
            strategy_from_api = self._get_strategy_from_api()
            if strategy_from_api:
                self.current_strategy = strategy_from_api
                logger.info(f"🔍 从API检测到策略: {self.current_strategy}")
                return

            # 方法2: 从配置文件获取策略信息
            strategy_from_config = self._get_strategy_from_config()
            if strategy_from_config:
                self.current_strategy = strategy_from_config
                logger.info(f"🔍 从配置文件检测到策略: {self.current_strategy}")
                return

            # 方法3: 从数据库获取最近使用的策略
            strategy_from_db = self._get_strategy_from_database()
            if strategy_from_db:
                self.current_strategy = strategy_from_db
                logger.info(f"🔍 从数据库检测到策略: {self.current_strategy}")
                return

            logger.warning("⚠️ 无法自动检测当前策略")

        except Exception as e:
            logger.error(f"❌ 策略检测失败: {e}")

    def _get_strategy_from_api(self) -> Optional[str]:
        """从FreqTrade API获取当前策略"""
        try:
            import requests
            response = requests.get("http://127.0.0.1:8080/api/v1/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                # API返回的状态信息中可能包含策略名称
                if isinstance(data, dict) and 'strategy' in data:
                    return data['strategy']
                elif isinstance(data, list) and len(data) > 0:
                    # 有时状态返回交易列表，从中提取策略信息
                    first_trade = data[0]
                    if 'strategy' in first_trade:
                        return first_trade['strategy']
        except Exception as e:
            logger.debug(f"API策略检测失败: {e}")
        return None

    def _get_strategy_from_config(self) -> Optional[str]:
        """从配置文件获取策略名称"""
        try:
            strategy = self.config.get('strategy')
            if strategy:
                return strategy
        except Exception as e:
            logger.debug(f"配置文件策略检测失败: {e}")
        return None

    def _get_strategy_from_database(self) -> Optional[str]:
        """从数据库获取最近使用的策略"""
        try:
            db_path = os.path.join(self.freqtrade_dir, "tradesv3.dryrun.sqlite")
            if not os.path.exists(db_path):
                db_path = os.path.join(self.freqtrade_dir, "tradesv3.sqlite")

            if os.path.exists(db_path):
                import sqlite3
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # 查询最近的交易记录获取策略名称
                cursor.execute("""
                    SELECT strategy FROM trades
                    WHERE strategy IS NOT NULL
                    ORDER BY open_date DESC
                    LIMIT 1
                """)
                result = cursor.fetchone()
                conn.close()

                if result:
                    return result[0]
        except Exception as e:
            logger.debug(f"数据库策略检测失败: {e}")
        return None

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "monitoring": { 
                "signal_monitor": {
                    "enabled": True,
                    "max_signals_per_interval": 10,
                    "min_signal_interval": 5,
                    "check_interval": 1
                },
                "capital_monitor": {
                    "enabled": True,
                    "max_drawdown_pct": 5.0,
                    "max_hourly_drawdown_pct": 10.0,
                    "check_interval": 5
                },
                "price_monitor": {
                    "enabled": True,
                    "max_short_term_change_pct": 3.0,
                    "max_hourly_change_pct": 8.0,
                    "check_interval": 5
                }
            },
            "data": {
                "storage_dir": "data/storage/data",
                "cache_enabled": True
            },
            "alerts": {
                "log_file": "data/monitoring/freqtrade/alerts.log",
                "storage_dir": "data/monitoring/freqtrade/monitoring_data"
            }
        }

    def setup_monitoring(self):
        """设置监控组件"""
        try:
            logger.info("📡 设置通用监控组件")

            # 如果风险监控模块可用，则设置高级监控
            if RISK_MONITORING_AVAILABLE:
                self._setup_advanced_monitoring()
            else:
                logger.info("� 使用基础监控功能")

            # 设置基础监控
            self._setup_basic_monitoring()

            logger.info("✅ 监控系统设置完成")

        except Exception as e:
            logger.error(f"❌ 设置监控组件失败: {e}")
            raise

    def _setup_advanced_monitoring(self):
        """设置高级监控组件（需要风险监控模块）"""
        try:
            # 创建监控器
            monitor_config = self._get_monitoring_config()

            # 信号监控器
            if monitor_config.get("signal_monitor", {}).get("enabled", True):
                signal_config = monitor_config.get("signal_monitor", {})
                self.signal_monitor = TradingSignalMonitor(
                    max_signals_per_interval=signal_config.get("max_signals_per_interval", 10),
                    min_signal_interval=signal_config.get("min_signal_interval", 5),
                    check_interval=signal_config.get("check_interval", 1)
                )
                logger.info("📡 信号监控器已启用")

            # 资金监控器
            if monitor_config.get("capital_monitor", {}).get("enabled", True):
                capital_config = monitor_config.get("capital_monitor", {})
                self.capital_monitor = CapitalChangeMonitor(
                    max_drawdown_pct=capital_config.get("max_drawdown_pct", 5.0),
                    max_hourly_drawdown_pct=capital_config.get("max_hourly_drawdown_pct", 10.0),
                    check_interval=capital_config.get("check_interval", 5)
                )
                logger.info("💰 资金监控器已启用")

            # 价格监控器
            if monitor_config.get("price_monitor", {}).get("enabled", True):
                price_config = monitor_config.get("price_monitor", {})
                self.price_monitor = PriceVolatilityMonitor(
                    max_short_term_change_pct=price_config.get("max_short_term_change_pct", 3.0),
                    max_hourly_change_pct=price_config.get("max_hourly_change_pct", 8.0),
                    check_interval=price_config.get("check_interval", 5)
                )
                logger.info("📈 价格监控器已启用")

            # 创建预警监控器
            self.alert_monitor = AlertMonitor()

            # 添加预警规则
            severity_rule = SeverityBasedAlertRule(
                name="high_severity_rule",
                min_severity="WARNING"
            )
            self.alert_monitor.add_rule(severity_rule)

            # 添加处理器
            strategy_name = self.current_strategy or "unknown"
            log_dir = f"data/monitoring/{strategy_name}"

            # 日志处理器
            log_file = f"{log_dir}/alerts.log"
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            logging_handler = LoggingHandler(
                name=f"{strategy_name}_logging_handler",
                log_file=log_file
            )
            self.alert_monitor.event_handlers.append(logging_handler)

            # 文件存储处理器
            storage_dir = f"{log_dir}/monitoring_data"
            os.makedirs(storage_dir, exist_ok=True)
            file_handler = FileStorageHandler(
                name=f"{strategy_name}_file_handler",
                storage_dir=storage_dir
            )
            self.alert_monitor.event_handlers.append(file_handler)

            logger.info("🚨 高级预警系统已设置完成")

        except Exception as e:
            logger.error(f"❌ 设置高级监控组件失败: {e}")

    def _setup_basic_monitoring(self):
        """设置基础监控组件"""
        try:
            # 基础监控不依赖外部模块
            logger.info("📊 基础监控组件已启用")
        except Exception as e:
            logger.error(f"❌ 设置基础监控组件失败: {e}")

    def _get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get("monitoring", {
            "signal_monitor": {
                "enabled": True,
                "max_signals_per_interval": 10,
                "min_signal_interval": 5,
                "check_interval": 1
            },
            "capital_monitor": {
                "enabled": True,
                "max_drawdown_pct": 5.0,
                "max_hourly_drawdown_pct": 10.0,
                "check_interval": 5
            },
            "price_monitor": {
                "enabled": True,
                "max_short_term_change_pct": 3.0,
                "max_hourly_change_pct": 8.0,
                "check_interval": 5
            }
        })

    def start_monitoring(self):
        """启动监控"""
        if self.is_running:
            logger.warning("⚠️ 监控系统已在运行")
            return
        
        try:
            # 设置监控组件
            self.setup_monitoring()
            
            # 启动监控线程
            self.is_running = True
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="FreqtradeMonitor"
            )
            self.monitor_thread.start()
            
            logger.info("🚀 FreqTrade 通用监控系统已启动")
            
            # 检查并打开FreqTrade UI
            self._open_freqtrade_ui()
            
        except Exception as e:
            logger.error(f"❌ 启动监控失败: {e}")
            self.is_running = False
            raise
    
    def _open_freqtrade_ui(self):
        """打开FreqTrade UI网页"""
        try:
            import webbrowser
            import requests


            # 从配置文件获取API服务器地址和端口
            api_config = self.config.get("api_server", {})
            listen_ip = api_config.get("listen_ip_address", "127.0.0.1")
            listen_port = api_config.get("listen_port", 8080)
            ui_url = f"http://{listen_ip}:{listen_port}"

            logger.info(f"🔍 检查FreqTrade API服务器: {ui_url}")

            # 检查API服务器是否可用
            api_available = False
            try:
                response = requests.get(f"{ui_url}/api/v1/ping", timeout=3)
                if response.status_code == 200:
                    logger.info("✅ FreqTrade API服务器已就绪")
                    api_available = True
                else:
                    logger.warning(f"⚠️ FreqTrade API响应异常: {response.status_code}")
            except Exception as e:
                logger.warning(f"⚠️ FreqTrade API服务器连接失败: {e}")
                logger.info("💡 请确保FreqTrade已启动并且API服务器已启用")

            # 无论API是否可用，都打开浏览器
            logger.info(f"🌐 正在打开FreqTrade UI: {ui_url}")
            webbrowser.open(ui_url)

            # 显示UI信息
            self._show_ui_info(ui_url, api_available)

        except Exception as e:
            logger.error(f"❌ 打开FreqTrade UI失败: {e}")

    def _show_ui_info(self, ui_url: str, api_available: bool = True):
        """显示UI信息"""
        strategy_name = self.current_strategy or "未知策略"
        api_status = "✅ 已连接" if api_available else "❌ 连接失败"

        print("\n" + "="*80)
        print("🎉 FreqTrade 通用监控系统启动成功")
        print("="*80)
        print(f"🌐 FreqTrade UI 监控界面: {ui_url}")
        print(f"� API服务器状态: {api_status}")
        print()
        print("�📈 功能说明:")
        print("   • Trade页面: 查看实时交易状态和开放订单")
        print("   • Performance页面: 查看策略性能和利润分析")
        print("   • Backtest页面: 进行策略回测")
        print("   • Logs页面: 查看详细日志")
        print("   • Config页面: 修改配置参数")
        print()
        print(f"🎯 当前策略监控: {strategy_name}")
        print("   • 实时策略信号监控")
        print("   • 自动交易执行跟踪")
        print("   • 风险管理和异常检测")
        print("   • 性能指标实时分析")
        print()
        if not api_available:
            print("⚠️ 故障排除:")
            print("   • 确保FreqTrade已启动")
            print("   • 检查配置文件中api_server.enabled=true")
            print("   • 确认端口号配置正确")
            print()
        print("💡 使用提示:")
        print("   • 在Trade页面可以看到当前开放的交易")
        print("   • Performance页面显示策略盈亏统计")
        print("   • 监控系统在后台持续运行")
        print("   • 按Ctrl+C停止监控系统")
        print("="*80)
        print()

    def stop_monitoring(self):
        """停止监控"""
        if not self.is_running:
            logger.warning("⚠️ 监控系统未在运行")
            return
        
        logger.info("🛑 正在停止监控系统...")
        self.is_running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        logger.info("✅ 监控系统已停止")

    def _monitoring_loop(self):
        """监控主循环"""
        logger.info("🔄 监控主循环开始")
        
        while self.is_running:
            try:
                # 模拟监控FreqTrade数据
                # 实际实现中，这里会从FreqTrade API或数据库读取实际数据
                self._check_freqtrade_status()
                
                # 等待下一次检查
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 监控循环中出现错误: {e}")
                time.sleep(5)
    
        logger.info("✅ 监控主循环结束")

    def _check_freqtrade_status(self):
        """检查FreqTrade状态"""
        try:
            current_time = datetime.now()
            logger.debug(f"🔍 检查FreqTrade状态: {current_time}")
            
            freqtrade_config = self.config.get("freqtrade", {})
            
            # 1. 检查FreqTrade进程是否运行
            try:
                import subprocess
                result = subprocess.run(['pgrep', '-f', 'freqtrade'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    logger.debug("✅ FreqTrade进程正在运行")
                else:
                    logger.warning("⚠️ FreqTrade进程未检测到")
            except (subprocess.TimeoutExpired, FileNotFoundError):
                # Windows系统或超时，跳过进程检查
                logger.debug("⏭️ 跳过进程检查")
            
            # 2. 检查FreqTrade配置文件
            config_path = freqtrade_config.get("config_path", "freqtrade-bot/config.json")
            if os.path.exists(config_path):
                logger.debug(f"✅ FreqTrade配置文件存在: {config_path}")
            else:
                logger.warning(f"⚠️ FreqTrade配置文件不存在: {config_path}")
            
            # 3. 检查FreqTrade数据库
            db_path = freqtrade_config.get("database_path", "freqtrade-bot/tradesv3.dryrun.sqlite")
            if os.path.exists(db_path):
                logger.debug(f"✅ FreqTrade数据库存在: {db_path}")
                # 可以在这里添加数据库查询逻辑
                self._check_freqtrade_database(db_path)
            else:
                logger.warning(f"⚠️ FreqTrade数据库不存在: {db_path}")
            
            # 4. 检查FreqTrade日志文件
            log_path = freqtrade_config.get("log_path", "freqtrade-bot/logs/freqtrade.log")
            if os.path.exists(log_path):
                logger.debug(f"✅ FreqTrade日志文件存在: {log_path}")
                # 可以在这里添加日志分析逻辑
                self._check_freqtrade_logs(log_path)
            else:
                logger.warning(f"⚠️ FreqTrade日志文件不存在: {log_path}")
            
            # 5. 检查API端点（如果可用）
            self._check_freqtrade_api()
            
        except Exception as e:
            logger.error(f"❌ 检查FreqTrade状态失败: {e}")
    
    def _check_freqtrade_database(self, db_path: str):
        """检查FreqTrade数据库"""
        try:
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询开放交易数量
            cursor.execute("SELECT COUNT(*) FROM trades WHERE is_open = 1")
            open_trades = cursor.fetchone()[0]
            
            # 查询总交易数量
            cursor.execute("SELECT COUNT(*) FROM trades")
            total_trades = cursor.fetchone()[0]
            
            logger.debug(f"📊 数据库状态: 开放交易 {open_trades}, 总交易 {total_trades}")
            
            conn.close()
            
        except Exception as e:
            logger.debug(f"数据库检查失败: {e}")
    
    def _check_freqtrade_logs(self, log_path: str):
        """检查FreqTrade日志文件"""
        try:
            # 读取最后几行日志
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1].strip()
                    logger.debug(f"📝 最新日志: {last_line[:100]}...")
                    
        except Exception as e:
            logger.debug(f"日志检查失败: {e}")

    def _check_freqtrade_api(self):
        """检查FreqTrade API端点"""
        try:
            import requests
            response = requests.get("http://127.0.0.1:8080/api/v1/ping", timeout=3)
            if response.status_code == 200:
                logger.debug("✅ FreqTrade API响应正常")
            else:
                logger.debug(f"⚠️ FreqTrade API异常响应: {response.status_code}")
        except Exception as e:
            logger.debug(f"API检查失败: {e}")

    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        stats = {
            "is_running": self.is_running,
            "start_time": getattr(self, 'start_time', None),
            "monitors": {
                "signal_monitor": self.signal_monitor is not None,
                "capital_monitor": self.capital_monitor is not None,
                "price_monitor": self.price_monitor is not None
            },
            "alert_monitor": self.alert_monitor is not None
        }
        
        return stats


def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='FreqTrade 通用监控系统')
    parser.add_argument('--config', '-c', help='指定FreqTrade配置文件名 (如: config_adl_backtest.json)')
    parser.add_argument('--freqtrade-dir', '-d', default='freqtrade-bot', help='FreqTrade工作目录 (默认: freqtrade-bot)')

    args = parser.parse_args()

    logger.info("🚀 启动 FreqTrade 通用监控系统")

    # 创建监控系统
    monitor = FreqtradeMonitor(
        freqtrade_dir=args.freqtrade_dir,
        config_file=args.config
    )

    try:
        # 启动监控
        monitor.start_monitoring()

        # 显示状态信息
        logger.info("📊 监控系统状态:")
        stats = monitor.get_monitoring_stats()
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")

        logger.info("💡 监控系统正在运行，按 Ctrl+C 停止")

        # 保持运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("⏹️ 接收到停止信号")
    except Exception as e:
        logger.error(f"❌ 运行时出现错误: {e}")
    finally:
        # 停止监控
        monitor.stop_monitoring()
        logger.info("👋 FreqTrade 通用监控系统已退出")


if __name__ == "__main__":
    main()