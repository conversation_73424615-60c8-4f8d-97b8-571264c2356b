# FreqTrade Docker部署配置（正确的TA-Lib安装）
# 功能：完整的FreqTrade环境，包含正确安装的TA-Lib技术分析库
# 特点：按照官方文档正确安装TA-Lib，适用于生产环境

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV FREQTRADE_CONFIG_PATH=/app/freqtrade-bot
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_TIMEOUT=300

# 安装系统依赖（包含TA-Lib编译所需）
RUN apt-get update && apt-get install -y --fix-missing \
    build-essential \
    wget \
    curl \
    git \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/* || \
    (apt-get update && apt-get install -y --fix-missing \
    build-essential \
    wget \
    curl \
    git \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*)

# 配置pip镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn && \
    pip config set global.timeout 300 && \
    pip install --upgrade pip

# 安装TA-Lib C库（按照官方文档）
RUN wget https://github.com/ta-lib/ta-lib/releases/download/v0.6.4/ta-lib-0.6.4-src.tar.gz && \
    tar -xzf ta-lib-0.6.4-src.tar.gz && \
    cd ta-lib-0.6.4 && \
    ./configure --prefix=/usr && \
    make && \
    make install && \
    cd .. && \
    rm -rf ta-lib-0.6.4 ta-lib-0.6.4-src.tar.gz

# 更新动态链接库缓存
RUN ldconfig

# 安装FreqTrade核心依赖
RUN pip install --no-cache-dir --timeout=300 \
    "ccxt>=4.4.87" \
    "SQLAlchemy>=2.0.6" \
    "python-telegram-bot>=20.1" \
    "humanize>=4.0.0" \
    cachetools \
    requests \
    "httpx>=0.24.1" \
    urllib3 \
    jsonschema \
    "numpy>=1.26.0" \
    "pandas>=2.2.0" \
    tabulate \
    "pycoingecko>=3.2.0" \
    py_find_1st \
    python-rapidjson \
    orjson \
    jinja2 \
    questionary \
    prompt-toolkit \
    "joblib>=1.2.0" \
    rich \
    pyarrow \
    fastapi \
    "pydantic>=2.2.0" \
    pyjwt \
    websockets \
    uvicorn \
    psutil \
    schedule \
    janus \
    ast-comments \
    aiofiles \
    aiohttp \
    cryptography \
    sdnotify \
    python-dateutil \
    pytz \
    packaging

# 安装TA-Lib Python包装器
RUN pip install --no-cache-dir --timeout=300 TA-Lib

# 安装FreqTrade
RUN pip install --no-cache-dir --timeout=300 freqtrade

# 复制requirements文件并安装项目依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --timeout=300 -r requirements.txt || echo "部分依赖安装失败，继续..."

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p /app/freqtrade-bot/user_data/logs && \
    mkdir -p /app/freqtrade-bot/user_data/data && \
    mkdir -p /app/storage/logs && \
    mkdir -p /app/logs

# 验证TA-Lib安装
RUN python -c "import talib; print('TA-Lib安装成功！版本:', talib.__version__)"

# 暴露端口
EXPOSE 8080 8081

# 默认启动命令
CMD ["freqtrade", "trade", "--config", "/app/freqtrade-bot/config_adl_production.json", "--strategy", "ADLAnticipationStrategy"]
