{"meta": {"name": "SMC统一配置", "description": "Smart Money Concepts策略的统一配置文件", "version": "1.0.0", "created": "2024-01-01", "note": "✅ 遵循MyGameNotes.md原则：统一配置管理，避免重复定义"}, "strategy": {"name": "SMCStrategy", "description": "基于ICT方法论的Smart Money Concepts策略 - 5分钟市场专用重构版", "timeframe": "5m", "features": ["Order Blocks识别 - 5分钟级别", "Fair Value Gaps检测 - 5分钟级别", "Market Structure分析 - 5分钟趋势", "Liquidity Zones识别", "Momentum Confirmation"], "parameters": {"structure_periods": 12, "trend_strength": 0.004, "risk_reward_ratio": 2.5, "ob_formation_periods": 10, "ob_volume_threshold": 1.6, "ob_price_reaction": 1.0, "fvg_min_gap_ratio": 0.25, "fvg_validity_periods": 36, "liquidity_zone_strength": 1.5, "momentum_periods": 12, "momentum_threshold": 0.5, "signal_cooldown_periods": 12, "max_daily_signals": 12, "min_signal_quality": 0.6, "atr_periods": 20, "atr_multiplier": 2.0, "swing_threshold": 0.004, "bos_threshold": 0.002, "ob_lookback": 10, "fvg_threshold": 0.25, "htf_bias_periods": 36}}, "freqtrade": {"integration": {"enabled": true, "professional_indicators": {"vfi_length": 26, "stc_fast": 12, "stc_slow": 26, "ichimoku_enable": true}}, "trading": {"minimal_roi": 0.025, "stoploss": -0.02, "timeframe": "5m", "can_short": true, "startup_candle_count": 100}, "order_types": {"entry": "market", "exit": "market", "stoploss": "market"}}, "risk_management": {"stop_loss_pct": 2.0, "take_profit_pct": 4.0, "max_position_pct": 5.0, "max_drawdown_limit": 20.0, "kelly_fraction": 0.2, "max_trades_per_day": 10}, "signal_filter": {"min_volume_ratio": 1.5, "max_spread_pct": 0.1, "min_atr_ratio": 0.8, "rsi_oversold": 30, "rsi_overbought": 70, "use_session_filter": true}, "optimization": {"enabled": true, "target_metric": "sharpe_ratio", "grid": {"structure_periods": [8, 12, 16], "trend_strength": [0.002, 0.004, 0.006], "risk_reward_ratio": [2.0, 2.5, 3.0], "ob_formation_periods": [8, 10, 12], "ob_volume_threshold": [1.4, 1.6, 1.8], "ob_price_reaction": [0.8, 1.0, 1.2], "fvg_min_gap_ratio": [0.2, 0.25, 0.3], "fvg_validity_periods": [24, 36, 48], "liquidity_zone_strength": [1.3, 1.5, 1.7], "momentum_periods": [10, 12, 14], "momentum_threshold": [0.4, 0.5, 0.6], "signal_cooldown_periods": [8, 12, 16], "max_daily_signals": [8, 12, 16], "min_signal_quality": [0.5, 0.6, 0.7], "atr_periods": [16, 20, 24], "atr_multiplier": [1.8, 2.0, 2.2], "stop_loss_pct": [1.8, 2.0, 2.2], "take_profit_pct": [2.0, 2.5, 3.0], "min_volume_ratio": [1.4, 1.6, 1.8], "rsi_oversold": [25, 30, 35], "rsi_overbought": [65, 70, 75], "swing_threshold": [0.003, 0.004, 0.005], "bos_threshold": [0.001, 0.002, 0.003], "ob_lookback": [8, 10, 12], "fvg_threshold": [0.2, 0.25, 0.3], "htf_bias_periods": [24, 36, 48]}, "constraints": {"max_combinations": 1000, "min_trades_required": 10, "max_optimization_time": 3600}}, "monitoring": {"signal_monitor": {"enabled": true, "max_signals_per_interval": 10, "min_signal_interval": 5, "check_interval": 1}, "capital_monitor": {"enabled": true, "max_drawdown_pct": 5.0, "max_hourly_drawdown_pct": 10.0, "check_interval": 5}, "price_monitor": {"enabled": true, "max_short_term_change_pct": 3.0, "max_hourly_change_pct": 8.0, "check_interval": 5}}, "frequi_dashboard": {"alert_rules": [{"name": "SMC_Signal_Generated", "type": "entry_signal", "condition": "enter_long OR enter_short", "severity": "info", "message": "SMC策略生成交易信号"}, {"name": "Structure_Break", "type": "market_structure", "condition": "bos_bullish OR bos_bearish", "severity": "info", "message": "市场结构突破"}], "dashboard_metrics": ["total_trades", "win_rate", "avg_profit_pct", "max_drawdown", "smc_signals_count"], "chart_indicators": ["swing_high", "swing_low", "bullish_ob", "bearish_ob", "bullish_fvg", "bearish_fvg"]}, "data": {"storage_dir": "data/storage/data", "cache_enabled": true, "preferred_pairs": ["BTC_USDT", "ETH_USDT", "BNB_USDT"], "min_volume_filter": 1000000}, "alerts": {"log_file": "data/monitoring/smc/alerts.log", "storage_dir": "data/monitoring/smc/monitoring_data"}, "performance_targets": {"daily_profit_target": 3.0, "max_daily_drawdown": 5.0, "min_win_rate": 60.0, "max_consecutive_losses": 5}}