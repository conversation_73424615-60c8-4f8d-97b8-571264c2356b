{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "dry_run": false, "cancel_open_orders_on_exit": true, "trading_mode": "futures", "margin_mode": "isolated", "max_leverage": 4.0, "adl_strategy_params": {"atr_period": 14, "atr_sma_period": 50, "atr_multiplier": 1.8, "zscore_period": 50, "zscore_threshold": 1.85, "atr_stop_multiplier": 0.42, "atr_profit_multiplier": 0.55, "volume_confirmation": {"volume_sma_period": 20, "volume_multiplier": 1.0, "enable_volume_filter": true}, "basic_thresholds": {"strong_signal_threshold": 0.4, "high_quality_signal_threshold": 1.0, "signal_strength_decline_threshold": 0.5, "default_fillna_value": 1.0}, "profit_loss_thresholds": {"quick_loss_threshold": -0.06, "profit_protection_threshold": 0.002}, "step3_optimization": {"enable": true, "signal_tier_system": {"tier1_strength_min": 1.2, "tier2_strength_min": 0.8, "tier3_strength_min": 0.5, "tier1_max_trades": 3, "tier2_max_trades": 3, "tier3_max_trades": 4}, "aggressive_time_management": {"enable": true, "tier1_time_limits": [35, 40, 45, 50, 60], "tier2_time_limits": [30, 35, 40, 45, 55], "tier3_time_limits": [27, 32, 37, 42, 50], "profit_protection_time": 8, "profit_protection_threshold": 1.0, "profit_protection_time_limit": 25, "quick_loss_threshold": -2.5, "quick_loss_time_limit": 10}, "momentum_confirmation": {"enable": true, "price_acceleration_periods": 3, "min_acceleration_ratio": 0.9, "volume_burst_multiplier": 0.9, "rsi_oversold_threshold": 50, "rsi_overbought_threshold": 50}, "profit_protection": {"enable": true, "min_profit_for_protection": 0.003, "protected_time_limit": 8, "quick_exit_loss_threshold": -0.008}}, "minimal_roi": {"0": 0.038, "2": 0.018, "4": 0.009, "6": 0}, "maker_only": true, "use_custom_stoploss": false, "leverage_revolution": {"enable": true, "base_leverage": 3.0, "tier1_leverage": 2.8, "tier2_leverage": 3.8, "tier3_leverage": 3.5, "volatility_adjustment": true, "min_leverage": 2, "risk_scaling": true}, "strategy_config": {"timeframe": "5m", "startup_candle_count": 60, "stoploss": -0.095, "use_custom_stoploss": false}, "technical_indicators": {"rsi_period": 14, "ema_short_period": 20, "sma_short_period": 20, "sma_long_period": 50, "clip_min_value": 0, "clip_max_value": 2, "division_protection": 1e-08}, "time_management": {"quick_loss_time_seconds": 480, "profit_protection_default_time": 25, "rsi_threshold_adjustment": 10, "trade_duration_check_minutes": 8}}, "liquidation_buffer": 0.05, "timeframe": "5m", "strategy": "ADLAnticipationStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 60, "exit": 60, "exit_timeout_count": 0, "unit": "seconds"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "eTqhJbBEFcQ3Wh1mrgoc2rAwVxTZn7KHtdC2WZ0zuTgDo9VTZd7RmgQdAaOaBLXA", "secret": "sdENnLxeQ3Jn4INGMSu92yRot3aI5FbjnqPmbfRURKe82n1z2f20S5zrP5EmJ2Wd", "password": "", "sandbox": false, "ccxt_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "options": {"defaultType": "future", "recvWindow": 10000}, "proxies": {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "proxies": {"http": "http://127.0.0.1:7890", "https": "http://127.0.0.1:7890"}}, "pair_whitelist": [], "pair_blacklist": []}, "pairlists": [{"method": "VolumePairList", "number_assets": 50, "sort_key": "quoteVolume", "min_value": 10000000, "refresh_period": 3600}], "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8081, "verbosity": "info", "enable_openapi": false, "jwt_secret_key": "69b5b88ea1cea9f64ca02dbcf16df8fccdaee9a9c2626874ce6471dcbf100611", "ws_token": "RTyNPDzSiJLQ-XmWHbYK1l1veeNPL7Dg4A_30E4dKts", "CORS_origins": ["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"], "username": "ADL_Trader_b85eb671", "password": "Y08D57DnfKHinZaM"}, "telegram": {"enabled": true, "token": "**********************************************", "chat_id": "1771388550", "notification_settings": {"status": "silent", "warning": "on", "startup": "on", "entry": "silent", "entry_fill": "on", "entry_cancel": "silent", "exit": "on", "exit_fill": "on", "exit_cancel": "on", "protection_trigger": "on", "protection_trigger_global": "on", "strategy_msg": "on", "show_candle": "off"}, "keyboard": [["/daily", "/profit", "/balance"], ["/status", "/status table", "/performance"], ["/count", "/start", "/stop", "/help"]], "reload": true, "balance_dust_level": 0.01}, "bot_name": "ADL-Production-Live", "db_url": "sqlite:///tradesv3.production.sqlite", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 3}, "order_types": {"entry": "limit", "exit": "limit", "emergency_exit": "limit", "stoploss": "limit", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60}, "order_time_in_force": {"entry": "GTC", "exit": "GTC"}, "dataformat_ohlcv": "json", "dataformat_trades": "json", "position_adjustment_enable": true, "max_entry_position_adjustment": 4}