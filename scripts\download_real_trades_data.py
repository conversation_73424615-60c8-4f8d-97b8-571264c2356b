#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实逐笔交易数据下载器

严格按照CVD.md文档第5.1章要求，从Binance Data Vision下载真实的逐笔交易数据。
禁止任何模拟、推断或权宜之计的数据处理方案。

数据源：https://data.binance.vision/data/futures/um/daily/trades/{symbol}/{symbol}-trades-{date}.zip
数据格式：id, price, qty, quote_qty, time, is_buyer_maker (ZIP压缩的CSV文件)
覆盖率要求：≥70%，时间跨度30天
"""

import requests
import pandas as pd
import numpy as np
import zipfile
import io
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import List, Dict, Optional
import time

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class BinanceRealTradesDownloader:
    """
    Binance真实逐笔交易数据下载器
    
    严格按照CVD.md文档要求，只使用真实的逐笔交易数据：
    - 数据源：Binance Data Vision官方历史数据
    - 格式：timestamp, price, qty, isBuyerMaker
    - 禁止任何模拟或推断数据
    """
    
    def __init__(self, symbol: str = "BTCUSDT"):
        self.symbol = symbol
        # 使用期货逐笔交易数据，严格按照CVD.md文档要求
        self.base_url = "https://data.binance.vision/data/futures/um/daily/trades"
        
        # 数据存储目录
        self.data_dir = Path("user_data/data")
        self.trades_dir = self.data_dir / "real_trades"
        self.cvd_dir = self.data_dir / "cvd"
        
        self.trades_dir.mkdir(parents=True, exist_ok=True)
        self.cvd_dir.mkdir(parents=True, exist_ok=True)
        
        # CVD.md文档第3.3章多层分类阈值
        self.retail_threshold = 1000.0      # 散户: <$1K
        self.professional_threshold = 50000.0  # 专业: $1K-$50K, 巨鲸: >$50K
        
        # 数据质量要求
        self.min_coverage_rate = 0.7  # CVD.md要求≥70%覆盖率
        
        logger.info(f"真实逐笔交易数据下载器初始化")
        logger.info(f"目标交易对: {self.symbol}")
        logger.info(f"数据源: Binance Data Vision - 期货trades数据")
        logger.info(f"数据路径: {self.base_url}")
        logger.info(f"多层CVD阈值: 散户<${self.retail_threshold}, 专业${self.retail_threshold}-${self.professional_threshold}, 巨鲸>${self.professional_threshold}")

    def download_daily_trades(self, date: datetime) -> pd.DataFrame:
        """
        下载指定日期的真实逐笔交易数据

        Args:
            date: 目标日期

        Returns:
            包含真实逐笔交易数据的DataFrame
        """
        date_str = date.strftime('%Y-%m-%d')
        filename = f"{self.symbol}-trades-{date_str}.zip"
        url = f"{self.base_url}/{self.symbol}/{filename}"

        logger.info(f"下载 {date_str} 的真实交易数据: {url}")

        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            # 解压ZIP数据
            with zipfile.ZipFile(io.BytesIO(response.content)) as zf:
                file_list = zf.namelist()
                if not file_list:
                    logger.warning(f"{date_str} ZIP文件为空")
                    return pd.DataFrame()

                # 读取第一个CSV文件
                csv_file = file_list[0]
                with zf.open(csv_file) as f:
                    df = pd.read_csv(f)

            if df.empty:
                logger.warning(f"{date_str} 无交易数据")
                return pd.DataFrame()

            # 验证列名格式：id, price, qty, quote_qty, time, is_buyer_maker
            expected_columns = ['id', 'price', 'qty', 'quote_qty', 'time', 'is_buyer_maker']
            if len(df.columns) >= 6:
                df.columns = expected_columns[:len(df.columns)]

            # 转换为CVD.md文档要求的格式
            df_trades = pd.DataFrame({
                'timestamp': pd.to_datetime(df['time'], unit='ms'),
                'price': df['price'].astype(float),
                'size': df['qty'].astype(float),
                'side': df['is_buyer_maker'].map({True: 'Sell', False: 'Buy'}),  # is_buyer_maker=True表示卖方是maker
                'trade_id': df['id'],
                'quote_qty': df['quote_qty'].astype(float)
            })

            # 计算交易金额用于多层分类
            df_trades['trade_value'] = df_trades['price'] * df_trades['size']

            logger.info(f"{date_str} 下载完成: {len(df_trades)} 条真实交易记录")
            logger.info(f"  价格范围: ${df_trades['price'].min():.2f} - ${df_trades['price'].max():.2f}")
            logger.info(f"  交易量范围: {df_trades['size'].min():.6f} - {df_trades['size'].max():.6f}")
            logger.info(f"  交易金额范围: ${df_trades['trade_value'].min():.2f} - ${df_trades['trade_value'].max():.2f}")

            return df_trades

        except requests.exceptions.RequestException as e:
            logger.error(f"下载 {date_str} 数据失败: {e}")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"处理 {date_str} 数据失败: {e}")
            return pd.DataFrame()

    def download_30days_trades(self) -> pd.DataFrame:
        """
        下载最近30天的真实逐笔交易数据

        由于Binance Data Vision可能有数据延迟，我们从较早的日期开始下载

        Returns:
            包含30天真实交易数据的DataFrame
        """
        logger.info("开始下载真实逐笔交易数据")

        # 基于测试成功的2025年6月数据，使用最新可用数据
        end_date = datetime(2025, 6, 19).date()  # 测试确认可用的最新日期
        start_date = end_date - timedelta(days=30)
        
        all_trades = []
        successful_days = 0
        total_days = 30
        
        current_date = start_date
        while current_date <= end_date:
            # 跳过未来日期
            if current_date > datetime.now().date():
                current_date += timedelta(days=1)
                continue
                
            daily_trades = self.download_daily_trades(datetime.combine(current_date, datetime.min.time()))
            
            if not daily_trades.empty:
                all_trades.append(daily_trades)
                successful_days += 1
                
            current_date += timedelta(days=1)
            
            # API限制延迟
            time.sleep(0.1)
        
        if not all_trades:
            logger.error("未能下载到任何真实交易数据")
            return pd.DataFrame()
        
        # 合并所有数据
        combined_df = pd.concat(all_trades, ignore_index=True)
        combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
        
        # 验证数据覆盖率
        coverage_rate = successful_days / total_days
        
        logger.info(f"真实逐笔交易数据下载完成")
        logger.info(f"  总交易记录: {len(combined_df):,}")
        logger.info(f"  时间范围: {combined_df['timestamp'].min()} 到 {combined_df['timestamp'].max()}")
        logger.info(f"  数据覆盖率: {coverage_rate:.1%} (要求: ≥{self.min_coverage_rate:.0%})")
        logger.info(f"  成功下载天数: {successful_days}/{total_days}")
        
        if coverage_rate < self.min_coverage_rate:
            logger.error(f"数据覆盖率不足！实际: {coverage_rate:.1%}, 要求: ≥{self.min_coverage_rate:.0%}")
            logger.error("CVD.md文档要求数据覆盖率≥70%，当前数据不符合要求")
            return pd.DataFrame()
        
        # 保存原始交易数据
        self.save_raw_trades(combined_df)
        
        return combined_df

    def calculate_real_multilayer_cvd(self, trades_df: pd.DataFrame) -> pd.DataFrame:
        """
        基于真实逐笔交易数据计算多层CVD
        
        严格按照CVD.md文档第3.3章要求：
        - CVD_Retail: 散户CVD (<$1K)
        - CVD_Pro: 专业交易者CVD ($1K-$50K)  
        - CVD_Whale: 巨鲸CVD (>$50K)
        
        Args:
            trades_df: 真实逐笔交易数据
            
        Returns:
            包含多层CVD的5分钟OHLCV数据
        """
        logger.info("基于真实逐笔交易数据计算多层CVD...")
        
        if trades_df.empty:
            logger.error("交易数据为空，无法计算CVD")
            return pd.DataFrame()
        
        # 转换side为数值：Buy = +1, Sell = -1
        trades_df = trades_df.copy()
        trades_df['side_multiplier'] = trades_df['side'].map({'Buy': 1, 'Sell': -1})
        trades_df['volume_delta'] = trades_df['size'] * trades_df['side_multiplier']
        
        # 按CVD.md文档第3.3章进行多层分类
        retail_mask = trades_df['trade_value'] < self.retail_threshold
        professional_mask = (trades_df['trade_value'] >= self.retail_threshold) & (trades_df['trade_value'] < self.professional_threshold)
        whale_mask = trades_df['trade_value'] >= self.professional_threshold
        
        # 分层计算成交量差值
        trades_df['retail_delta'] = trades_df['volume_delta'].where(retail_mask, 0)
        trades_df['professional_delta'] = trades_df['volume_delta'].where(professional_mask, 0)
        trades_df['whale_delta'] = trades_df['volume_delta'].where(whale_mask, 0)
        
        # 设置时间索引
        trades_df.set_index('timestamp', inplace=True)
        
        # 重采样到5分钟并计算OHLCV + CVD
        logger.info("重采样到5分钟K线并计算CVD...")
        
        resampled = trades_df.resample('5min').agg({
            'price': ['first', 'max', 'min', 'last'],  # OHLC
            'size': 'sum',  # 总成交量
            'trade_value': 'sum',  # 总成交额
            'retail_delta': 'sum',
            'professional_delta': 'sum', 
            'whale_delta': 'sum',
            'volume_delta': 'sum'
        })
        
        # 展平列名
        resampled.columns = ['_'.join(col).strip() for col in resampled.columns.values]
        resampled = resampled.rename(columns={
            'price_first': 'open',
            'price_max': 'high', 
            'price_min': 'low',
            'price_last': 'close',
            'size_sum': 'volume',
            'trade_value_sum': 'volume_usd'
        })
        
        # 计算累积CVD - CVD.md文档核心指标
        resampled['cvd_retail'] = resampled['retail_delta_sum'].cumsum()
        resampled['cvd_professional'] = resampled['professional_delta_sum'].cumsum()
        resampled['cvd_whale'] = resampled['whale_delta_sum'].cumsum()
        resampled['cvd_total'] = resampled['volume_delta_sum'].cumsum()
        
        # 移除空数据行
        resampled = resampled.dropna(subset=['open', 'high', 'low', 'close'])
        
        # 统计各层级交易分布
        total_trades = len(trades_df)
        retail_trades = retail_mask.sum()
        professional_trades = professional_mask.sum()
        whale_trades = whale_mask.sum()
        
        logger.info(f"真实多层CVD计算完成，生成 {len(resampled)} 个5分钟周期")
        logger.info(f"真实交易分布统计:")
        logger.info(f"  散户交易 (<${self.retail_threshold}): {retail_trades:,} ({retail_trades/total_trades*100:.1f}%)")
        logger.info(f"  专业交易 (${self.retail_threshold}-${self.professional_threshold}): {professional_trades:,} ({professional_trades/total_trades*100:.1f}%)")
        logger.info(f"  巨鲸交易 (>${self.professional_threshold}): {whale_trades:,} ({whale_trades/total_trades*100:.1f}%)")
        
        return resampled

    def detect_real_cvd_divergences(self, cvd_df: pd.DataFrame, lookback_period: int = 20) -> pd.DataFrame:
        """
        基于真实CVD数据检测背离信号

        严格按照CVD.md文档第4.2章规则矩阵实现：
        - 看涨背离：价格创新低，CVD创更高低点
        - 看跌背离：价格创新高，CVD创更低高点
        """
        logger.info("基于真实CVD数据检测背离信号...")

        if cvd_df.empty:
            return cvd_df

        df = cvd_df.copy()

        # 计算价格滚动高低点
        df['price_rolling_low'] = df['low'].rolling(lookback_period).min()
        df['price_rolling_high'] = df['high'].rolling(lookback_period).max()

        # 各层CVD滚动高低点
        for layer in ['retail', 'professional', 'whale']:
            cvd_col = f'cvd_{layer}'
            if cvd_col in df.columns:
                df[f'{cvd_col}_rolling_low'] = df[cvd_col].rolling(lookback_period).min()
                df[f'{cvd_col}_rolling_high'] = df[cvd_col].rolling(lookback_period).max()

        # 检测价格新高新低
        df['price_new_low'] = df['low'] == df['price_rolling_low']
        df['price_new_high'] = df['high'] == df['price_rolling_high']

        # 检测各层CVD背离 - 基于真实数据
        for layer in ['retail', 'professional', 'whale']:
            cvd_col = f'cvd_{layer}'
            if cvd_col in df.columns:
                # 看涨背离：价格新低 + CVD更高低点
                df[f'{layer}_bullish_divergence'] = (
                    df['price_new_low'] &
                    (df[cvd_col] > df[f'{cvd_col}_rolling_low'])
                )

                # 看跌背离：价格新高 + CVD更低高点
                df[f'{layer}_bearish_divergence'] = (
                    df['price_new_high'] &
                    (df[cvd_col] < df[f'{cvd_col}_rolling_high'])
                )

        # CVD.md文档第3.3章核心信号组合
        # 恐慌吸收信号：散户恐慌抛售 + 巨鲸吸收
        df['panic_absorption_signal'] = (
            df['retail_bearish_divergence'] &
            df['whale_bullish_divergence']
        )

        # FOMO派发信号：散户FOMO追高 + 巨鲸派发
        df['fomo_distribution_signal'] = (
            df['retail_bullish_divergence'] &
            df['whale_bearish_divergence']
        )

        # 统计真实CVD背离信号
        panic_signals = df['panic_absorption_signal'].sum()
        fomo_signals = df['fomo_distribution_signal'].sum()

        logger.info(f"真实CVD背离检测完成:")
        logger.info(f"  恐慌吸收信号: {panic_signals}")
        logger.info(f"  FOMO派发信号: {fomo_signals}")

        return df

    def save_raw_trades(self, trades_df: pd.DataFrame) -> bool:
        """保存原始交易数据"""
        try:
            output_file = self.trades_dir / f"{self.symbol}_real_trades_30days.feather"
            trades_df.to_feather(output_file)

            logger.info(f"原始交易数据保存完成:")
            logger.info(f"  文件: {output_file}")
            logger.info(f"  记录数: {len(trades_df):,}")

            return True
        except Exception as e:
            logger.error(f"保存原始交易数据失败: {e}")
            return False

    def save_real_cvd_data(self, cvd_df: pd.DataFrame) -> bool:
        """保存真实CVD数据"""
        try:
            # 选择需要保存的列
            cvd_columns = [
                'open', 'high', 'low', 'close', 'volume', 'volume_usd',
                'cvd_retail', 'cvd_professional', 'cvd_whale', 'cvd_total',
                'retail_bullish_divergence', 'retail_bearish_divergence',
                'professional_bullish_divergence', 'professional_bearish_divergence',
                'whale_bullish_divergence', 'whale_bearish_divergence',
                'panic_absorption_signal', 'fomo_distribution_signal'
            ]

            available_columns = [col for col in cvd_columns if col in cvd_df.columns]
            final_df = cvd_df[available_columns].copy()

            # 保存完整CVD数据
            full_output_file = self.cvd_dir / f"{self.symbol}_real_cvd_full.feather"
            final_df.to_feather(full_output_file)

            # 保存FreqTrade兼容格式
            freqtrade_columns = [
                'open', 'high', 'low', 'close', 'volume',
                'cvd_retail', 'cvd_professional', 'cvd_whale', 'cvd_total',
                'panic_absorption_signal', 'fomo_distribution_signal'
            ]

            freqtrade_available = [col for col in freqtrade_columns if col in final_df.columns]
            freqtrade_df = final_df[freqtrade_available].copy()

            freqtrade_output_file = self.cvd_dir / f"{self.symbol}_cvd_freqtrade.feather"
            freqtrade_df.to_feather(freqtrade_output_file)

            logger.info(f"真实CVD数据保存完成:")
            logger.info(f"  完整数据: {full_output_file} ({len(final_df)} 条记录)")
            logger.info(f"  FreqTrade格式: {freqtrade_output_file} ({len(freqtrade_df)} 条记录)")
            logger.info(f"  数据时间范围: {final_df.index.min()} 到 {final_df.index.max()}")

            return True

        except Exception as e:
            logger.error(f"保存真实CVD数据失败: {e}")
            return False

    def collect_real_cvd_data(self) -> bool:
        """
        收集真实CVD数据的主方法

        严格按照CVD.md文档第5.1章要求的完整流程：
        1. 从Binance Data Vision下载真实逐笔交易数据
        2. 验证数据覆盖率≥70%
        3. 计算真实多层CVD指标
        4. 检测真实CVD背离信号
        5. 保存FreqTrade兼容格式

        禁止任何模拟、推断或权宜之计
        """
        logger.info("开始收集真实CVD数据流程")

        # 1. 下载真实逐笔交易数据
        logger.info("步骤1: 下载真实逐笔交易数据")
        trades_df = self.download_30days_trades()

        if trades_df.empty:
            logger.error("❌ 真实逐笔交易数据下载失败")
            logger.error("CVD.md文档要求使用真实逐笔交易数据，禁止使用模拟数据")
            return False

        # 2. 计算真实多层CVD
        logger.info("步骤2: 计算真实多层CVD指标")
        cvd_df = self.calculate_real_multilayer_cvd(trades_df)

        if cvd_df.empty:
            logger.error("❌ 真实多层CVD计算失败")
            return False

        # 3. 检测真实CVD背离信号
        logger.info("步骤3: 检测真实CVD背离信号")
        cvd_df = self.detect_real_cvd_divergences(cvd_df)

        # 4. 保存真实CVD数据
        logger.info("步骤4: 保存真实CVD数据")
        success = self.save_real_cvd_data(cvd_df)

        if success:
            logger.info("✅ 真实CVD数据收集流程完成！")

            # 数据质量报告
            total_periods = len(cvd_df)
            panic_signals = cvd_df['panic_absorption_signal'].sum() if 'panic_absorption_signal' in cvd_df.columns else 0
            fomo_signals = cvd_df['fomo_distribution_signal'].sum() if 'fomo_distribution_signal' in cvd_df.columns else 0

            logger.info(f"真实数据质量报告:")
            logger.info(f"  总5分钟周期: {total_periods}")
            logger.info(f"  恐慌吸收信号: {panic_signals} ({panic_signals/total_periods*100:.2f}%)")
            logger.info(f"  FOMO派发信号: {fomo_signals} ({fomo_signals/total_periods*100:.2f}%)")
            logger.info(f"  数据来源: 100%真实逐笔交易数据")

            logger.info("\n现在可以运行真实CVD策略回测:")
            logger.info("  python scripts/cvd_real_data_backtest.py --days 30")

            return True
        else:
            logger.error("❌ 真实CVD数据保存失败")
            return False


def main():
    """主函数 - 严格按照CVD.md文档要求收集真实CVD数据"""
    downloader = BinanceRealTradesDownloader("BTCUSDT")

    success = downloader.collect_real_cvd_data()

    if success:
        logger.info("✅ 真实CVD数据收集完成！")
        logger.info("数据完全符合CVD.md文档要求：")
        logger.info("  - 100%真实逐笔交易数据")
        logger.info("  - 多层CVD分类基于真实交易金额")
        logger.info("  - 背离检测基于真实CVD指标")
        logger.info("  - 数据覆盖率≥70%")
    else:
        logger.error("❌ 真实CVD数据收集失败")
        logger.error("请检查网络连接和数据源可用性")


if __name__ == '__main__':
    main()
