#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
5分钟加密货币动量策略配置管理器

统一管理动量策略的所有参数配置，支持：
1. 参数验证和约束检查
2. 市场条件自适应调整
3. 性能目标监控
4. 优化参数网格生成
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class MomentumStrategyParams:
    """动量策略核心参数"""
    # 动量引擎参数
    momentum_fast_period: int = 3
    momentum_slow_period: int = 8
    momentum_threshold: float = 0.001
    
    # 趋势跟踪参数
    trend_fast_ema: int = 5
    trend_slow_ema: int = 13
    trend_filter_ema: int = 21
    
    # 突破检测参数
    breakout_lookback: int = 4
    breakout_threshold: float = 0.0008
    
    # 成交量确认参数
    volume_ma_period: int = 8
    volume_threshold: float = 1.1
    
    # 风险管理参数
    max_trades_per_hour: int = 12
    signal_cooldown_minutes: int = 3


@dataclass
class RiskManagementParams:
    """风险管理参数"""
    max_position_size: float = 1.0
    max_open_trades: int = 3
    max_daily_trades: int = 50
    max_hourly_trades: int = 12
    base_stop_loss: float = 0.012
    base_take_profit: float = 0.012
    trailing_stop_enabled: bool = True
    trailing_offset: float = 0.005


@dataclass
class PerformanceTargets:
    """性能目标参数"""
    min_trades_per_day: int = 20
    target_trades_per_day: int = 40
    max_trades_per_day: int = 80
    target_daily_return: float = 0.02
    max_daily_drawdown: float = 0.05
    min_win_rate: float = 0.45
    min_sharpe_ratio: float = 0.5


class CryptoMomentumConfigManager:
    """
    5分钟加密货币动量策略配置管理器
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认使用项目配置文件
        """
        if config_path is None:
            config_path = Path(__file__).parent / "crypto_momentum_config.json"
        
        self.config_path = Path(config_path)
        self.config_data = {}
        
        # 策略参数
        self.strategy_params = MomentumStrategyParams()
        self.risk_params = RiskManagementParams()
        self.performance_targets = PerformanceTargets()
        
        # 加载配置
        self._load_config()
        
        logger.info(f"动量策略配置管理器初始化完成")
        logger.info(f"配置文件: {self.config_path}")
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                
                # 更新策略参数
                self._update_strategy_params()
                self._update_risk_params()
                self._update_performance_targets()
                
                logger.info("配置文件加载成功")
            else:
                logger.warning(f"配置文件不存在: {self.config_path}")
                self._create_default_config()
                
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self._create_default_config()
    
    def _update_strategy_params(self):
        """更新策略参数"""
        if 'strategy' in self.config_data and 'parameters' in self.config_data['strategy']:
            params = self.config_data['strategy']['parameters']
            
            for key, value in params.items():
                if hasattr(self.strategy_params, key):
                    setattr(self.strategy_params, key, value)
    
    def _update_risk_params(self):
        """更新风险管理参数"""
        if 'risk_management' in self.config_data:
            risk_config = self.config_data['risk_management']
            
            # 基础风险参数
            self.risk_params.max_position_size = risk_config.get('max_position_size', 1.0)
            self.risk_params.max_open_trades = risk_config.get('max_open_trades', 3)
            self.risk_params.max_daily_trades = risk_config.get('max_daily_trades', 50)
            self.risk_params.max_hourly_trades = risk_config.get('max_hourly_trades', 12)
            
            # 止损止盈参数
            if 'stop_loss' in risk_config:
                self.risk_params.base_stop_loss = risk_config['stop_loss'].get('base_percentage', 0.012) / 100
            
            if 'take_profit' in risk_config:
                self.risk_params.base_take_profit = risk_config['take_profit'].get('base_percentage', 0.012) / 100
    
    def _update_performance_targets(self):
        """更新性能目标"""
        if 'performance_targets' in self.config_data and 'daily' in self.config_data['performance_targets']:
            daily_targets = self.config_data['performance_targets']['daily']
            
            self.performance_targets.min_trades_per_day = daily_targets.get('min_trades', 20)
            self.performance_targets.target_trades_per_day = daily_targets.get('target_trades', 40)
            self.performance_targets.max_trades_per_day = daily_targets.get('max_trades', 80)
            self.performance_targets.target_daily_return = daily_targets.get('target_return', 0.02)
            self.performance_targets.max_daily_drawdown = daily_targets.get('max_drawdown', 0.05)
        
        if 'optimization' in self.config_data and 'constraints' in self.config_data['optimization']:
            constraints = self.config_data['optimization']['constraints']
            
            self.performance_targets.min_win_rate = constraints.get('min_win_rate', 0.45)
            self.performance_targets.min_sharpe_ratio = constraints.get('min_sharpe_ratio', 0.5)
    
    def _create_default_config(self):
        """创建默认配置"""
        logger.info("创建默认配置")
        # 使用当前参数作为默认值
        pass
    
    def get_strategy_params(self) -> MomentumStrategyParams:
        """获取策略参数"""
        return self.strategy_params
    
    def get_risk_params(self) -> RiskManagementParams:
        """获取风险管理参数"""
        return self.risk_params
    
    def get_performance_targets(self) -> PerformanceTargets:
        """获取性能目标"""
        return self.performance_targets
    
    def get_optimization_grid(self) -> Dict[str, List]:
        """获取优化参数网格"""
        if 'optimization' in self.config_data and 'grid' in self.config_data['optimization']:
            return self.config_data['optimization']['grid']
        
        # 默认优化网格
        return {
            'momentum_fast_period': [2, 3, 4, 5],
            'momentum_slow_period': [6, 8, 10, 12],
            'momentum_threshold': [0.0005, 0.001, 0.0015, 0.002],
            'trend_fast_ema': [3, 5, 8, 10],
            'trend_slow_ema': [10, 13, 16, 21],
            'breakout_threshold': [0.0005, 0.0008, 0.001, 0.0012],
            'volume_threshold': [1.05, 1.1, 1.15, 1.2]
        }
    
    def validate_params(self, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证参数有效性
        
        Args:
            params: 待验证的参数字典
            
        Returns:
            (is_valid, error_messages)
        """
        errors = []
        
        # 验证动量参数
        if 'momentum_fast_period' in params and 'momentum_slow_period' in params:
            if params['momentum_fast_period'] >= params['momentum_slow_period']:
                errors.append("快速动量周期必须小于慢速动量周期")
        
        # 验证趋势参数
        if 'trend_fast_ema' in params and 'trend_slow_ema' in params:
            if params['trend_fast_ema'] >= params['trend_slow_ema']:
                errors.append("快速EMA周期必须小于慢速EMA周期")
        
        # 验证阈值参数
        for param_name in ['momentum_threshold', 'breakout_threshold']:
            if param_name in params:
                if params[param_name] <= 0 or params[param_name] > 0.1:
                    errors.append(f"{param_name} 必须在 (0, 0.1] 范围内")
        
        # 验证交易频率参数
        if 'max_trades_per_hour' in params:
            if params['max_trades_per_hour'] < 1 or params['max_trades_per_hour'] > 20:
                errors.append("每小时最大交易数必须在 [1, 20] 范围内")
        
        return len(errors) == 0, errors
    
    def adjust_for_market_conditions(self, volatility: float, trend_strength: float) -> MomentumStrategyParams:
        """
        根据市场条件调整参数
        
        Args:
            volatility: 市场波动率
            trend_strength: 趋势强度
            
        Returns:
            调整后的策略参数
        """
        adjusted_params = MomentumStrategyParams(**asdict(self.strategy_params))
        
        # 根据波动率调整
        if 'market_conditions' in self.config_data:
            market_config = self.config_data['market_conditions']
            
            if 'volatility_adjustment' in market_config and market_config['volatility_adjustment']['enabled']:
                vol_config = market_config['volatility_adjustment']
                
                if volatility < vol_config['low_volatility']['threshold']:
                    # 低波动率：降低阈值，增加敏感度
                    multiplier = vol_config['low_volatility']['parameter_multiplier']
                    adjusted_params.momentum_threshold *= multiplier
                    adjusted_params.breakout_threshold *= multiplier
                    
                elif volatility > vol_config['high_volatility']['threshold']:
                    # 高波动率：提高阈值，降低敏感度
                    multiplier = vol_config['high_volatility']['parameter_multiplier']
                    adjusted_params.momentum_threshold *= multiplier
                    adjusted_params.breakout_threshold *= multiplier
            
            # 根据趋势强度调整
            if 'trend_strength_adjustment' in market_config and market_config['trend_strength_adjustment']['enabled']:
                trend_config = market_config['trend_strength_adjustment']
                
                if trend_strength > trend_config['strong_trend']['threshold']:
                    # 强趋势：增强动量敏感度
                    boost = trend_config['strong_trend']['momentum_boost']
                    adjusted_params.momentum_threshold /= boost
                    
                elif trend_strength < trend_config['weak_trend']['threshold']:
                    # 弱趋势：降低动量敏感度
                    reduction = trend_config['weak_trend']['momentum_reduction']
                    adjusted_params.momentum_threshold /= reduction
        
        return adjusted_params
    
    def check_performance_targets(self, metrics: Dict[str, float]) -> Dict[str, bool]:
        """
        检查性能目标达成情况
        
        Args:
            metrics: 性能指标字典
            
        Returns:
            目标达成情况字典
        """
        results = {}
        
        # 检查交易频率
        trades_per_day = metrics.get('trades_per_day', 0)
        results['min_trades_met'] = trades_per_day >= self.performance_targets.min_trades_per_day
        results['max_trades_exceeded'] = trades_per_day > self.performance_targets.max_trades_per_day
        
        # 检查收益率
        daily_return = metrics.get('daily_return', 0)
        results['return_target_met'] = daily_return >= self.performance_targets.target_daily_return
        
        # 检查回撤
        max_drawdown = metrics.get('max_drawdown', 0)
        results['drawdown_acceptable'] = max_drawdown <= self.performance_targets.max_daily_drawdown
        
        # 检查胜率
        win_rate = metrics.get('win_rate', 0)
        results['win_rate_acceptable'] = win_rate >= self.performance_targets.min_win_rate
        
        # 检查夏普比率
        sharpe_ratio = metrics.get('sharpe_ratio', 0)
        results['sharpe_acceptable'] = sharpe_ratio >= self.performance_targets.min_sharpe_ratio
        
        return results
    
    def get_freqtrade_config(self) -> Dict[str, Any]:
        """获取FreqTrade兼容的配置"""
        if 'freqtrade' in self.config_data:
            return self.config_data['freqtrade']

        # 默认FreqTrade配置
        return {
            "trading": {
                "minimal_roi": 0.012,
                "stoploss": -0.012,
                "timeframe": "5m",
                "can_short": True,
                "startup_candle_count": 100
            }
        }

    def get_adl_strategy_config(self) -> Dict[str, Any]:
        """
        获取ADL策略专用配置

        基于ADL策略文档的参数设置，优化5分钟加密货币交易
        """
        if 'adl_strategy' in self.config_data:
            return self.config_data['adl_strategy']

        # ADL策略优化配置 - 基于参数优化报告的最佳配置
        return {
            # 核心指标参数 - 激进参数配置（提升信号频率）
            "indicators": {
                "atr_period": 14,           # ATR计算周期
                "atr_sma_period": 50,       # ATR移动平均周期
                "atr_multiplier": 1.8,      # ATR扩张倍数阈值（从2.5优化到1.8）
                "zscore_period": 50,        # Z-Score计算周期
                "zscore_threshold": 2.2     # Z-Score触发阈值（从3.0优化到2.2）
            },

            # 风险管理参数 - 优化风险回报比
            "risk_management": {
                "stoploss": -0.03,          # 3%止损
                "atr_stop_multiplier": 0.3, # 止损ATR倍数（从0.5优化到0.3）
                "atr_profit_multiplier": 0.9, # 止盈ATR倍数（从0.75优化到0.9）
                "max_leverage": 3.0,        # 最大杠杆
                "risk_reward_ratio": 3.0    # 风险回报比（从1.5优化到3.0）
            },

            # 交易对筛选参数
            "pair_selection": {
                "min_volume_usd": 20_000_000,  # 最小日交易额
                "max_pairs": 20,               # 最大交易对数量
                "usdc_priority": True,         # 优先USDC-M合约
                "cache_duration": 3600         # 缓存时长（秒）
            },

            # 执行参数 - 优化信号频率
            "execution": {
                "maker_only": True,         # 强制挂单执行
                "min_signal_gap": 2,        # 最小信号间隔（从5优化到2）
                "time_stop_minutes": 120    # 时间止损（分钟）
            },

            # FreqTrade保护配置
            "protections": [
                {
                    "method": "CooldownPeriod",
                    "stop_duration_candles": 5
                },
                {
                    "method": "MaxDrawdown",
                    "lookback_period_candles": 48,
                    "trade_limit": 20,
                    "stop_duration_candles": 4,
                    "max_allowed_drawdown": 0.15  # 15%最大回撤
                },
                {
                    "method": "StoplossGuard",
                    "lookback_period_candles": 24,
                    "trade_limit": 4,
                    "stop_duration_candles": 2,
                    "only_per_pair": False
                },
                {
                    "method": "LowProfitPairs",
                    "lookback_period_candles": 6,
                    "trade_limit": 2,
                    "stop_duration_candles": 60,
                    "required_profit": 0.02
                }
            ]
        }
