#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ADL-Anticipation策略：利用币安期货市场微观结构无效率

基于ADL策略文档，实现预测自动减仓（ADL）事件的5分钟交易策略。
核心论点：通过预测ADL事件的发生，抢先交易由强制平仓引起的价格瞬间波动。

策略特点：
1. 基于"准-ADL"复合指标：ATR波动性扩张 + Z-Score价格偏离
2. 专注USDC-M合约的0%挂单费率优势
3. 严格的1.5:1风险回报比和基于ATR的动态止损
4. 集成FreqTrade专业风险保护系统
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import IStrategy, Trade
from freqtrade.strategy import CategoricalParameter
from freqtrade.persistence import Order
from pandas import DataFrame
import talib.abstract as ta




class ADLAnticipationStrategy(IStrategy):
    """
    ADL-Anticipation策略
    
    基于ADL策略文档第3节的完整实现：
    - 交易对动态筛选：选择波动性与流动性的"甜蜜点"
    - "准-ADL"信号生成：ATR扩张 + Z-Score偏离的复合指标
    - 挂单执行：永远做Maker，利用USDC-M的0%费率优势
    - 风险管理：基于ATR的动态止盈止损，1.5:1风险回报比
    """
    
    # 策略元数据
    INTERFACE_VERSION = 3
    timeframe = '5m'  # 严格按照ADL文档要求的5分钟时间框架
    startup_candle_count: int = 60  # 需要60根K线来计算指标
    max_open_trades = 5  # 强制设置最大持仓数
    
    # ROI表：从配置文件加载，支持动态调整
    # 默认值作为备用，优先使用配置文件中的设置
    minimal_roi = {
        "0": 0.025,   # 默认立即2.5%止盈
        "10": 0.012,  # 默认10分钟后1.2%
        "20": 0.006,  # 默认20分钟后0.6%
        "30": 0       # 默认30分钟后0%
    }

    stoploss = -0.080  # 8.0%固定止损，进一步放宽：1.8-3倍杠杆下相当于-2.7%到-4.4%价格变动

    # 动态止盈配置
    use_custom_stoploss = False  # 禁用动态止盈功能，专注基础参数优化

    # 🎯 ADL策略参数 - 从配置文件动态加载
    # 参数现在在config_adl_backtest.json的adl_strategy_params部分定义
    # 这样可以方便地进行参数测试而无需修改策略代码



    # 执行优化参数
    maker_only = CategoricalParameter([True, False], default=True, space="buy")  # 启用挂单模式（利用0%费率）
    
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)

        # 从配置文件读取ADL策略参数
        adl_params = config.get('adl_strategy_params', {})

        # 核心参数 - 支持hyperopt优化
        self.atr_period = adl_params.get('atr_period', 14)
        self.atr_sma_period = adl_params.get('atr_sma_period', 50)
        self.zscore_period = adl_params.get('zscore_period', 50)

        # 可优化参数 - hyperopt时使用优化值，否则使用配置文件值
        self.atr_multiplier = adl_params.get('atr_multiplier', 2.0)
        self.zscore_threshold = adl_params.get('zscore_threshold', 2.5)
        self.atr_profit_multiplier = adl_params.get('atr_profit_multiplier', 0.82)

        # 风险管理参数
        self.atr_stop_multiplier = adl_params.get('atr_stop_multiplier', 0.5)

        # 🚀 第三步优化参数加载
        step3_config = adl_params.get('step3_optimization', {})
        self.step3_enabled = step3_config.get('enable', False)
        
        if self.step3_enabled:
            # 信号强度分层系统
            tier_config = step3_config.get('signal_tier_system', {})
            self.tier1_strength_min = tier_config.get('tier1_strength_min', 2.0)
            self.tier2_strength_min = tier_config.get('tier2_strength_min', 1.5)
            self.tier3_strength_min = tier_config.get('tier3_strength_min', 1.2)
            self.tier1_max_trades = tier_config.get('tier1_max_trades', 3)
            self.tier2_max_trades = tier_config.get('tier2_max_trades', 2)
            self.tier3_max_trades = tier_config.get('tier3_max_trades', 1)
            
            # 激进时间管理
            time_config = step3_config.get('aggressive_time_management', {})
            self.aggressive_time_enabled = time_config.get('enable', True)
            self.tier1_time_limits = time_config.get('tier1_time_limits', [12, 15, 18, 20, 25])
            self.tier2_time_limits = time_config.get('tier2_time_limits', [8, 10, 12, 15, 18])
            self.tier3_time_limits = time_config.get('tier3_time_limits', [5, 6, 8, 10, 12])
            self.profit_protection_time = time_config.get('profit_protection_time', 8)
            
            # 价格动量确认
            momentum_config = step3_config.get('momentum_confirmation', {})
            self.momentum_confirmation_enabled = momentum_config.get('enable', True)
            self.price_acceleration_periods = momentum_config.get('price_acceleration_periods', 3)
            self.min_acceleration_ratio = momentum_config.get('min_acceleration_ratio', 1.3)
            self.volume_burst_multiplier = momentum_config.get('volume_burst_multiplier', 1.8)
            # RSI阈值设置
            self.rsi_oversold_threshold = momentum_config.get('rsi_oversold_threshold', 45)
            self.rsi_overbought_threshold = momentum_config.get('rsi_overbought_threshold', 55)
            
            # 盈利保护机制
            profit_config = step3_config.get('profit_protection', {})
            self.profit_protection_enabled = profit_config.get('enable', True)
            self.min_profit_for_protection = profit_config.get('min_profit_for_protection', 0.003)
            self.protected_time_limit = profit_config.get('protected_time_limit', 8)
            self.quick_exit_loss_threshold = profit_config.get('quick_exit_loss_threshold', -0.008)
            
            # 信号质量控制
            quality_config = step3_config.get('signal_quality_control', {})
            self.consecutive_loss_limit = quality_config.get('consecutive_loss_limit', 3)
            self.daily_signal_limit = quality_config.get('daily_signal_limit', 15)
            self.cooldown_after_loss = quality_config.get('cooldown_after_loss', 2)
            
            self.logger.info("🚀 第三步优化已启用：信号分层 + 激进时间管理 + 动量确认")
        else:
            # 第三步优化未启用时，仍然需要读取RSI阈值
            momentum_config = adl_params.get('step3_optimization', {}).get('momentum_confirmation', {})
            self.rsi_oversold_threshold = momentum_config.get('rsi_oversold_threshold', 45)
            self.rsi_overbought_threshold = momentum_config.get('rsi_overbought_threshold', 55)
            self.logger.info("📊 使用第二步优化配置（第三步优化未启用）")

        # ROI配置：从配置文件加载，支持动态调整
        config_roi = adl_params.get('minimal_roi', {})
        if config_roi:
            self.minimal_roi = config_roi
            self.logger.info(f"📊 从配置加载ROI表: {config_roi}")
        else:
            self.logger.info(f"📊 使用默认ROI表: {self.minimal_roi}")

        # 执行参数
        self.maker_only = adl_params.get('maker_only', True)

        self.logger.info("🔧 从配置文件加载ADL参数")

        # 策略状态跟踪
        self.last_signal_time = {}  # 记录最后信号时间，避免频繁交易
        self.consecutive_losses = 0  # 连续亏损计数
        self.daily_signal_count = 0  # 日内信号计数
        self.last_trade_date = None  # 上次交易日期

        # 参数诊断
        self._diagnose_parameters()

        self.logger.info("🚀 ADL-Anticipation策略初始化完成")
        self.logger.info(f"📊 目标交易对：USDC-M合约（0%挂单费率）")
        self.logger.info(f"⚡ 时间框架：{self.timeframe}")
        self.logger.info(f"🎯 风险回报比：1.5:1")

    def _diagnose_parameters(self):
        """诊断参数加载情况"""
        self.logger.info("🔍 === 参数诊断 ===")
        self.logger.info(f"📊 ATR倍数: {self.atr_multiplier}")
        self.logger.info(f"📊 Z-Score阈值: {self.zscore_threshold}")
        self.logger.info(f"📊 ATR周期: {self.atr_period}")
        self.logger.info(f"📊 ATR SMA周期: {self.atr_sma_period}")
        self.logger.info(f"📊 Z-Score周期: {self.zscore_period}")
        self.logger.info(f"📊 Max Open Trades: {getattr(self, 'max_open_trades', 'undefined')}")
        self.logger.info(f"📊 Maker Only: {self.maker_only}")
        self.logger.info("🔍 === 诊断完成 ===")

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标
        
        基于ADL策略文档第3.2节，实现"准-ADL"复合指标计算
        """
        pair = metadata.get('pair', 'UNKNOWN')
        self.logger.info(f"🔄 计算ADL指标 - {pair}")
        
        # 🔧 参数确认：显示实际使用的参数值
        if pair == 'BTC/USDC:USDC':  # 只对BTC输出，避免日志过多
            self.logger.info(f"🔧 关键参数确认: ATR倍数={self.atr_multiplier}, Z-Score阈值={self.zscore_threshold}")
        
        # 使用内置ADL计算，确保参数一致性
        dataframe = self._calculate_adl_indicators_builtin(dataframe)
            
        # 基础技术指标
        dataframe['rsi'] = ta.RSI(dataframe['close'], timeperiod=14)
        dataframe['ema_20'] = ta.EMA(dataframe['close'], timeperiod=20)

        # 趋势判断指标（用于分层止损）
        dataframe['sma_20'] = ta.SMA(dataframe['close'], timeperiod=20)
        dataframe['sma_50'] = ta.SMA(dataframe['close'], timeperiod=50)

        # 成交量确认
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_confirm'] = dataframe['volume'] > dataframe['volume_sma']
        
        # 🚀 第三步优化：价格动量确认指标
        if self.step3_enabled and self.momentum_confirmation_enabled:
            # 价格加速度检测（3周期价格变化率的变化率）
            dataframe['price_change_1'] = dataframe['close'].pct_change(1)
            dataframe['price_change_2'] = dataframe['close'].pct_change(2) 
            dataframe['price_change_3'] = dataframe['close'].pct_change(3)
            
            # 计算价格加速度（变化率的增加趋势）
            dataframe['price_acceleration'] = (
                abs(dataframe['price_change_1']) / 
                (abs(dataframe['price_change_2']) + 1e-8)
            ).fillna(1.0)
            
            # 成交量爆发检测
            dataframe['volume_ratio'] = (
                dataframe['volume'] / dataframe['volume_sma']
            ).fillna(1.0)
            
            # 动量确认条件
            dataframe['momentum_confirmed'] = (
                (dataframe['price_acceleration'] > self.min_acceleration_ratio) &
                (dataframe['volume_ratio'] > self.volume_burst_multiplier)
            )
            
            self.logger.info(f"📈 动量确认指标已计算 - {pair}: 加速度阈值{self.min_acceleration_ratio}, 成交量倍数{self.volume_burst_multiplier}")
        else:
            # 第三步优化未启用时，默认所有信号都通过动量确认
            dataframe['momentum_confirmed'] = True
        
        return dataframe
        
    def _calculate_adl_indicators_builtin(self, dataframe: DataFrame) -> DataFrame:
        """
        内置ADL指标计算（备用方案）

        实现与ADLAnticipationIndicator相同的逻辑
        支持hyperopt参数优化
        """
        # 🔧 修复：强制使用配置文件参数（而非hyperopt默认值）
        # 在普通回测中，直接使用config文件加载的参数
        atr_multiplier = self.atr_multiplier
        zscore_threshold = self.zscore_threshold
        


        # 1. ATR波动性扩张检测（与文档伪代码保持一致）
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period)
        dataframe['sma_atr'] = ta.SMA(dataframe['atr'], timeperiod=self.atr_sma_period)
        dataframe['atr_expansion'] = dataframe['atr'] > (dataframe['sma_atr'] * atr_multiplier)

        # 2. Z-Score价格偏离检测
        dataframe['sma'] = dataframe['close'].rolling(window=self.zscore_period).mean()
        dataframe['stddev'] = dataframe['close'].rolling(window=self.zscore_period).std()
        dataframe['zscore'] = (dataframe['close'] - dataframe['sma']) / dataframe['stddev']

        dataframe['zscore_extreme_low'] = dataframe['zscore'] < -zscore_threshold
        dataframe['zscore_extreme_high'] = dataframe['zscore'] > zscore_threshold
        
        # 3. "准-ADL"复合信号
        dataframe['adl_long_signal'] = dataframe['zscore_extreme_low'] & dataframe['atr_expansion']
        dataframe['adl_short_signal'] = dataframe['zscore_extreme_high'] & dataframe['atr_expansion']
        
        # 4. 信号强度（修复计算逻辑，支持hyperopt优化）
        dataframe['adl_signal_strength'] = (
            (np.abs(dataframe['zscore']) / zscore_threshold) *
            (dataframe['atr'] / dataframe['sma_atr']) / atr_multiplier
        ).clip(0, 2)  # 允许超过1的强度值
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场信号
        
        🚀 第三步优化：实现信号强度分层机制
        - 顶级信号(>2.0)：最高优先级，正常交易
        - 二级信号(1.5-2.0)：中等优先级，限制交易数量
        - 三级信号(1.2-1.5)：低优先级，严格限制
        """
        pair = metadata.get('pair', 'UNKNOWN')
        
        # 基础ADL信号
        long_adl_signal = dataframe['adl_long_signal'] == True
        short_adl_signal = dataframe['adl_short_signal'] == True

        # 🔧 第四步优化：放宽信号质量过滤器以提高频率
        volume_filter = dataframe['volume_confirm'] == True
        rsi_oversold = dataframe['rsi'] < self.rsi_oversold_threshold  # 从配置文件读取
        rsi_overbought = dataframe['rsi'] > self.rsi_overbought_threshold  # 从配置文件读取
        
        # 🚀 第三步优化：信号强度分层和动量确认
        if self.step3_enabled:
            # 信号强度分层
            signal_strength = dataframe['adl_signal_strength']
            tier1_strength = signal_strength > self.tier1_strength_min  # >2.0 顶级信号
            tier2_strength = (signal_strength > self.tier2_strength_min) & (signal_strength <= self.tier1_strength_min)  # 1.5-2.0
            tier3_strength = (signal_strength > self.tier3_strength_min) & (signal_strength <= self.tier2_strength_min)  # 1.2-1.5
            
            # 动量确认
            momentum_confirmed = dataframe['momentum_confirmed'] == True
            
            # 分层入场条件
            # 顶级信号：最严格的质量要求
            tier1_long_condition = (
                long_adl_signal & 
                volume_filter & 
                rsi_oversold & 
                tier1_strength & 
                momentum_confirmed
            )
            
            tier1_short_condition = (
                short_adl_signal & 
                volume_filter & 
                rsi_overbought & 
                tier1_strength & 
                momentum_confirmed
            )
            
            # 二级信号：中等要求，可选择性放宽某些条件
            tier2_long_condition = (
                long_adl_signal & 
                volume_filter & 
                (rsi_oversold | (dataframe['rsi'] < self.rsi_oversold_threshold - 10)) &  # 基于配置值放宽RSI要求
                tier2_strength & 
                momentum_confirmed
            )
            
            tier2_short_condition = (
                short_adl_signal & 
                volume_filter & 
                (rsi_overbought | (dataframe['rsi'] > self.rsi_overbought_threshold + 10)) &  # 基于配置值放宽RSI要求
                tier2_strength & 
                momentum_confirmed
            )
            
            # 三级信号：基本要求，但数量严格限制
            tier3_long_condition = (
                long_adl_signal & 
                volume_filter & 
                tier3_strength & 
                momentum_confirmed  # 仍需动量确认
            )
            
            tier3_short_condition = (
                short_adl_signal & 
                volume_filter & 
                tier3_strength & 
                momentum_confirmed
            )
            
            # 信号标记：为不同等级的信号设置不同的entry_tag
            dataframe.loc[tier1_long_condition, 'enter_long'] = 1
            dataframe.loc[tier1_long_condition, 'enter_tag'] = 'tier1_long'
            
            dataframe.loc[tier1_short_condition, 'enter_short'] = 1  
            dataframe.loc[tier1_short_condition, 'enter_tag'] = 'tier1_short'
            
            dataframe.loc[tier2_long_condition, 'enter_long'] = 1
            dataframe.loc[tier2_long_condition, 'enter_tag'] = 'tier2_long'
            
            dataframe.loc[tier2_short_condition, 'enter_short'] = 1
            dataframe.loc[tier2_short_condition, 'enter_tag'] = 'tier2_short'
            
            dataframe.loc[tier3_long_condition, 'enter_long'] = 1
            dataframe.loc[tier3_long_condition, 'enter_tag'] = 'tier3_long'
            
            dataframe.loc[tier3_short_condition, 'enter_short'] = 1
            dataframe.loc[tier3_short_condition, 'enter_tag'] = 'tier3_short'
            
            # 统计各层级信号
            tier1_signals = tier1_long_condition.sum() + tier1_short_condition.sum()
            tier2_signals = tier2_long_condition.sum() + tier2_short_condition.sum()
            tier3_signals = tier3_long_condition.sum() + tier3_short_condition.sum()
            total_signals = tier1_signals + tier2_signals + tier3_signals
            
            self.logger.info(f"🎯 分层信号统计 - {pair}: T1={tier1_signals}, T2={tier2_signals}, T3={tier3_signals}, 总计={total_signals}")
            
        else:
            # 第四步优化：降低信号强度门槛以提高频率
            strong_signal = dataframe['adl_signal_strength'] > 0.5
            
            long_entry_condition = (
                long_adl_signal & 
                volume_filter & 
                rsi_oversold & 
                strong_signal
            )

            short_entry_condition = (
                short_adl_signal & 
                volume_filter & 
                rsi_overbought & 
                strong_signal
            )
            
            dataframe.loc[long_entry_condition, 'enter_long'] = 1
            dataframe.loc[short_entry_condition, 'enter_short'] = 1
            
            total_signals = long_entry_condition.sum() + short_entry_condition.sum()
            self.logger.info(f"🎯 传统信号统计 - {pair}: 总计={total_signals}")
        
        # 基础统计信息（保持向后兼容）
        adl_long_count = long_adl_signal.sum()
        adl_short_count = short_adl_signal.sum()
        
        self.logger.info(f"🔍 ADL信号检测 - {pair}: ADL多头={adl_long_count}, ADL空头={adl_short_count}")
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充出场信号

        基于ADL策略文档第3.3.3节的出场逻辑：
        - 主要依赖ROI和stoploss配置
        - 时间止损：防止资金被无效交易占用
        """
        # ADL策略主要依赖动态止盈止损，这里保持简单
        # 可以添加反向ADL信号作为出场确认

        # 反向信号出场（可选）
        dataframe.loc[dataframe['adl_short_signal'] == True, 'exit_long'] = 1
        dataframe.loc[dataframe['adl_long_signal'] == True, 'exit_short'] = 1

        return dataframe





    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: datetime,
                           entry_tag: Optional[str], side: str, **kwargs) -> bool:
        """
        确认交易入场

        ADL策略核心：强制使用挂单（Maker），利用USDC-M的0%费率
        """
        # ADL策略强制要求：只交易USDC-M合约（文档第4.2.2节）
        if 'USDC' not in pair:
            self.logger.error(f"❌ 拒绝非USDC-M合约 - {pair}: ADL策略要求0%挂单费率")
            return False

        if self.maker_only:
            # 检查是否为挂单
            if order_type != 'limit':
                self.logger.warning(f"❌ 拒绝市价单 - {pair}: ADL策略要求挂单执行")
                return False

            self.logger.info(f"✅ USDC-M合约挂单确认 - {pair}")
            return True

        return True

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        自定义出场逻辑 - 🚀 第三步优化：激进时间管理 + 盈利保护

        新增功能：
        - 根据信号等级设置不同的时间限制
        - 盈利保护：有浮盈后立即切换到更严格的时间控制
        - 快速亏损止损：早期出现较大亏损时提前退出
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if dataframe.empty:
            return None

        # 获取入场时的ATR值（与止损逻辑一致）
        entry_atr = getattr(trade, 'entry_atr', None)
        if entry_atr is None:
            entry_atr = dataframe.iloc[-1].get('atr', 0)
            trade.entry_atr = entry_atr

        if entry_atr == 0:
            return None

        # ATR动态止盈检查（保持原有逻辑）
        atr_profit_multiplier = self.atr_profit_multiplier

        # 计算动态止盈目标
        if trade.is_short == False:  # 多头交易
            target_profit_price = trade.open_rate + (atr_profit_multiplier * entry_atr)
            target_profit_pct = (target_profit_price - trade.open_rate) / trade.open_rate
            if current_profit >= target_profit_pct:
                self.logger.info(f"✅ ATR动态止盈触发 - {pair}: 入场ATR={entry_atr:.6f}, 倍数{atr_profit_multiplier}, 目标{target_profit_pct:.3f}%, 当前{current_profit:.3f}%")
                return "atr_profit_target"
        else:  # 空头交易
            target_profit_price = trade.open_rate - (atr_profit_multiplier * entry_atr)
            target_profit_pct = (trade.open_rate - target_profit_price) / trade.open_rate
            if current_profit >= target_profit_pct:
                self.logger.info(f"✅ ATR动态止盈触发 - {pair}: 入场ATR={entry_atr:.6f}, 倍数{atr_profit_multiplier}, 目标{target_profit_pct:.3f}%, 当前{current_profit:.3f}%")
                return "atr_profit_target"

        # 🚀 第三步优化：激进时间管理
        trade_duration = current_time - trade.open_date_utc
        
        if self.step3_enabled and self.aggressive_time_enabled:
            # 🛡️ 进一步放宽：调整快速亏损止损条件
            quick_loss_threshold = -7.0 / 100.0  # -7.0%，杠杆(1.8-3x)适配后相当于-2.3%到-3.9%价格变动
            if (self.profit_protection_enabled and 
                current_profit < quick_loss_threshold and 
                trade_duration.total_seconds() > 480):  # 8分钟后检查
                self.logger.info(f"❌ 快速亏损止损触发 - {pair}: 亏损{current_profit:.3f}% > 阈值{quick_loss_threshold:.3f}%")
                return "quick_loss_exit"
            
            # 🎯 根据信号等级确定时间限制
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            
            # 确定信号等级
            if 'tier1' in entry_tag:
                time_limits = self.tier1_time_limits
                tier = "T1"
            elif 'tier2' in entry_tag:
                time_limits = self.tier2_time_limits  
                tier = "T2"
            elif 'tier3' in entry_tag:
                time_limits = self.tier3_time_limits
                tier = "T3"
            else:
                # 未知等级，使用tier2作为默认值
                time_limits = self.tier2_time_limits
                tier = "T2(默认)"
            
            # 🛡️ 第四步优化：调整盈利保护阈值
            profit_protection_threshold = 0.5 / 100.0  # 0.5%
            if (self.profit_protection_enabled and 
                current_profit > profit_protection_threshold):
                time_limit_minutes = 15  # 盈利保护时间限制
                protection_status = "盈利保护"
            else:
                # 根据ATR动态调整时间限制（保持原有逻辑但使用新的时间范围）
                current_atr = dataframe.iloc[-1].get('atr', 0)
                atr_sma = dataframe.iloc[-1].get('atr_sma', 0)
                
                if atr_sma > 0:
                    atr_ratio = current_atr / atr_sma
                else:
                    atr_ratio = 1.0
                
                # 根据ATR比率选择时间限制
                if atr_ratio >= 1.8:
                    time_limit_minutes = time_limits[4]  # 最长时间
                    volatility_level = "极高波动"
                elif atr_ratio >= 1.3:
                    time_limit_minutes = time_limits[3]
                    volatility_level = "高波动"
                elif atr_ratio >= 1.0:
                    time_limit_minutes = time_limits[2]  # 中间值
                    volatility_level = "中波动"
                elif atr_ratio >= 0.7:
                    time_limit_minutes = time_limits[1]
                    volatility_level = "低波动"
                else:
                    time_limit_minutes = time_limits[0]  # 最短时间
                    volatility_level = "极低波动"
                
                protection_status = f"{volatility_level}(ATR比率{atr_ratio:.2f})"
            

            
            # 检查是否超过时间限制
            time_limit_seconds = time_limit_minutes * 60
            
            if trade_duration.total_seconds() > time_limit_seconds:
                self.logger.info(f"⏰ 激进时间止损触发 - {pair}: 持仓{trade_duration}, "
                               f"{tier}等级限制{time_limit_minutes}分钟, {protection_status}, "
                               f"当前盈利{current_profit:.3f}%")
                return "aggressive_time_stop"
        
        else:
            # 第三步优化未启用，使用原有的动态时间止损逻辑
            current_atr = dataframe.iloc[-1].get('atr', 0)
            atr_sma = dataframe.iloc[-1].get('atr_sma', 0)

            if atr_sma > 0:
                atr_ratio = current_atr / atr_sma
            else:
                atr_ratio = 1.0

            # 原有时间限制逻辑
            if atr_ratio >= 1.8:
                time_limit_minutes = 25
                volatility_level = "极高波动"
            elif atr_ratio >= 1.3:
                time_limit_minutes = 22
                volatility_level = "高波动"
            elif atr_ratio >= 1.0:
                time_limit_minutes = 18
                volatility_level = "中波动"
            elif atr_ratio >= 0.7:
                time_limit_minutes = 15
                volatility_level = "低波动"
            else:
                time_limit_minutes = 12
                volatility_level = "极低波动"

            time_limit_seconds = time_limit_minutes * 60

            if trade_duration.total_seconds() > time_limit_seconds:
                self.logger.info(f"⏰ 动态时间止损触发 - {pair}: 持仓{trade_duration}, "
                               f"ATR比率{atr_ratio:.2f}({volatility_level}), 限制{time_limit_minutes}分钟")
                return "time_stop"

        # 🚀 第一步重构（第三版）：多重止盈系统
        multi_exit_config = self.config.get('adl_strategy_params', {}).get('multi_exit_system', {})
        
        if multi_exit_config.get('enable', False):
            # 根据入场标签确定止盈目标
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            
            if 'tier1' in entry_tag:
                profit_targets = multi_exit_config.get('tier1_profit_targets', [0.008, 0.015, 0.025, 0.040])
                tier_level = "T1"
            elif 'tier2' in entry_tag:
                profit_targets = multi_exit_config.get('tier2_profit_targets', [0.006, 0.012, 0.020, 0.032])
                tier_level = "T2"
            elif 'tier3' in entry_tag:
                profit_targets = multi_exit_config.get('tier3_profit_targets', [0.004, 0.008, 0.015, 0.025])
                tier_level = "T3"
            else:
                profit_targets = [0.005, 0.010, 0.018, 0.030]
                tier_level = "默认"
            
            # 波动率自适应调整
            if multi_exit_config.get('volatility_adaptive', True):
                # 获取当前ATR相对强度
                try:
                    dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
                    if not dataframe.empty:
                        current_atr = dataframe.iloc[-1].get('atr', 0)
                        atr_sma = dataframe.iloc[-1].get('atr_sma', current_atr)
                        
                        if current_atr > 0 and atr_sma > 0:
                            volatility_ratio = current_atr / atr_sma
                            
                            # 高波动率提高止盈目标，低波动率降低目标
                            if volatility_ratio > 1.3:  # 高波动
                                multiplier = 1.4
                                volatility_status = "高波动"
                            elif volatility_ratio > 1.1:  # 中等波动 
                                multiplier = 1.2
                                volatility_status = "中等波动"
                            else:  # 低波动
                                multiplier = 0.9
                                volatility_status = "低波动"
                            
                            profit_targets = [target * multiplier for target in profit_targets]
                except:
                    volatility_status = "未知波动"
                    pass
            
            # 检查多重止盈触发
            for i, target in enumerate(profit_targets):
                if current_profit >= target:
                    # 检查是否已经执行过此级别的止盈
                    exit_tag = f"multi_exit_L{i+1}_{tier_level}"
                    
                    self.logger.info(f"🎯 多重止盈触发 - {pair}: 级别L{i+1}/{len(profit_targets)}, "
                                   f"目标{target:.1%}, 当前{current_profit:.3%}, "
                                   f"{tier_level}信号, {volatility_status}")
                    
                    return exit_tag
            
            # 信号强度追踪 - 如果信号正在衰减，提前止盈
            if multi_exit_config.get('signal_maturity_tracking', True):
                # 持仓时间超过10分钟且有小幅盈利，检查信号强度变化
                if (trade_duration.total_seconds() > 600 and  # 10分钟
                    current_profit > 0.002):  # 0.2%以上盈利
                    
                    try:
                        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
                        if not dataframe.empty:
                            current_signal_strength = dataframe.iloc[-1].get('adl_signal_strength', 0)
                            
                            # 如果当前信号强度明显下降，执行成熟度止盈
                            if current_signal_strength < 0.8:  # 信号强度下降
                                self.logger.info(f"📈 信号成熟度止盈 - {pair}: 信号强度下降至{current_signal_strength:.2f}, "
                                               f"当前盈利{current_profit:.3%}")
                                return "signal_maturity_exit"
                    except:
                        pass

        return None



    def adjust_trade_position(self, trade: Trade, current_time: datetime,
                             current_rate: float, current_profit: float,
                             min_stake: Optional[float], max_stake: float,
                             current_entry_rate: float, current_exit_rate: float,
                             current_entry_profit: float, current_exit_profit: float,
                             **kwargs) -> Optional[float]:
        """
        基于ADL信号强度的动态加仓策略
        
        核心逻辑：
        1. 只有在盈利或微亏时才考虑加仓
        2. 基于当前ADL信号强度决定加仓规模
        3. 根据tier等级设置不同的加仓策略
        4. 最多允许4次加仓（配置中的max_entry_position_adjustment）
        """
        try:
            # 获取当前数据
            dataframe, _ = self.dp.get_analyzed_dataframe(trade.pair, self.timeframe)
            if dataframe.empty:
                return None
            
            # 只有在盈利或微亏时才考虑加仓（更保守的条件）
            if current_profit < -0.005:  # 亏损超过0.5%时不加仓
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 亏损过大 {current_profit:.3%}")
                return None
            
            # 获取当前ADL信号强度
            current_signal_strength = dataframe.iloc[-1].get('adl_signal_strength', 0)
            current_adl_long_signal = dataframe.iloc[-1].get('adl_long_signal', False)
            current_adl_short_signal = dataframe.iloc[-1].get('adl_short_signal', False)
            
            # 检查是否有当前ADL信号（同向）
            has_same_direction_signal = (
                (not trade.is_short and current_adl_long_signal) or
                (trade.is_short and current_adl_short_signal)
            )
            
            if not has_same_direction_signal:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 无同向ADL信号")
                return None
            
            # 根据入场标签确定加仓策略
            entry_tag = getattr(trade, 'enter_tag', 'unknown')
            
            if 'tier1' in entry_tag:
                # Tier1信号：激进加仓策略
                min_signal_strength = 2.2
                max_additions = 2  # 最多2次加仓（更保守）
                position_multiplier = 0.8  # 每次加仓80%的原始仓位（更保守）
                tier_level = "T1"
            elif 'tier2' in entry_tag:
                # Tier2信号：中等加仓策略
                min_signal_strength = 1.8
                max_additions = 1  # 最多1次加仓（更保守）
                position_multiplier = 0.6  # 每次加仓60%的原始仓位（更保守）
                tier_level = "T2"
            elif 'tier3' in entry_tag:
                # Tier3信号：保守加仓策略
                min_signal_strength = 1.5
                max_additions = 1  # 最多1次加仓
                position_multiplier = 0.4  # 每次加仓40%的原始仓位（更保守）
                tier_level = "T3"
            else:
                # 未知tier，使用默认策略
                min_signal_strength = 1.3
                max_additions = 2
                position_multiplier = 0.9
                tier_level = "默认"
            
            # 检查信号强度是否足够
            if current_signal_strength < min_signal_strength:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 信号强度不足 {current_signal_strength:.2f} < {min_signal_strength}")
                return None
            
            # 检查已加仓次数
            filled_entries = trade.filled_entries if hasattr(trade, 'filled_entries') else []
            current_additions = len(filled_entries) - 1  # 减去初始入场
            
            if current_additions >= max_additions:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 已达最大加仓次数 {current_additions}/{max_additions}")
                return None
            
            # 计算加仓规模
            initial_stake = trade.stake_amount
            additional_stake = initial_stake * position_multiplier
            
            # 确保不超过最大仓位限制
            additional_stake = min(additional_stake, max_stake)
            additional_stake = max(additional_stake, min_stake) if min_stake else additional_stake
            
            # 额外的安全检查：基于时间间隔
            trade_duration = current_time - trade.open_date_utc
            min_interval_minutes = 10  # 至少10分钟间隔
            
            if trade_duration.total_seconds() < min_interval_minutes * 60:
                self.logger.debug(f"⛔ 加仓取消 - {trade.pair}: 时间间隔不足 {trade_duration}")
                return None
            
            # 基于波动率调整加仓规模
            try:
                current_atr = dataframe.iloc[-1].get('atr', 0)
                atr_sma = dataframe.iloc[-1].get('atr_sma', current_atr)
                
                if current_atr > 0 and atr_sma > 0:
                    volatility_ratio = current_atr / atr_sma
                    
                    # 高波动时增加加仓规模，低波动时减少
                    if volatility_ratio > 1.5:  # 高波动
                        vol_multiplier = 1.3
                        vol_status = "高波动"
                    elif volatility_ratio > 1.2:  # 中等波动
                        vol_multiplier = 1.1
                        vol_status = "中等波动"
                    else:  # 低波动
                        vol_multiplier = 0.9
                        vol_status = "低波动"
                    
                    additional_stake *= vol_multiplier
                else:
                    vol_status = "未知波动"
            except:
                vol_status = "波动率计算失败"
            
            self.logger.info(f"💰 执行加仓 - {trade.pair}: {tier_level}信号(强度{current_signal_strength:.2f}), "
                           f"第{current_additions + 1}次加仓, 规模{additional_stake:.2f} USDC, "
                           f"当前盈利{current_profit:.3%}, {vol_status}")
            
            return additional_stake
            
        except Exception as e:
            self.logger.error(f"❌ 加仓策略执行失败 - {trade.pair}: {e}")
            return None

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        动态杠杆控制方法
        
        根据信号等级动态调整杠杆：
        - Tier1信号：使用较高杠杆（提升收益）
        - Tier2信号：使用中等杠杆  
        - Tier3信号：使用较低杠杆（控制风险）
        """
        if not self.step3_enabled:
            # 第三步优化未启用时，使用固定杠杆
            return min(2.5, max_leverage)
        
        # 根据信号等级确定杠杆（新配置：tier3=3x, tier2=2x）
        if entry_tag:
            if 'tier1' in entry_tag:
                # 顶级信号：使用2.5倍杠杆
                leverage = 2.5
                self.logger.info(f"🎯 Tier1信号杠杆 - {pair}: {leverage:.1f}x (最高等级)")
            elif 'tier2' in entry_tag:
                # 二级信号：使用1.8倍杠杆（降低风险）
                leverage = 1.8
                self.logger.info(f"🎯 Tier2信号杠杆 - {pair}: {leverage:.1f}x (中等等级)")
            elif 'tier3' in entry_tag:
                # 三级信号：使用3.0倍杠杆
                leverage = 3.0
                self.logger.info(f"🎯 Tier3信号杠杆 - {pair}: {leverage:.1f}x (基础等级)")
            else:
                # 未知等级，使用默认杠杆
                leverage = 2.0
                self.logger.info(f"🎯 默认信号杠杆 - {pair}: {leverage:.1f}x (未知等级)")
        else:
            # 无entry_tag时使用默认杠杆
            leverage = 1.6
            self.logger.info(f"🎯 默认杠杆 - {pair}: {leverage:.1f}x (无标签)")
        
        return leverage

    def adjust_entry_price(self, trade: Trade, order: Order, pair: str,
                          current_time: datetime, proposed_rate: float, current_order_rate: float,
                          entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        调整加仓入场价格
        
        策略：使用稍微更有利的价格来增加成交概率
        """
        try:
            # 获取当前市场数据
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if dataframe.empty:
                return proposed_rate
            
            current_atr = dataframe.iloc[-1].get('atr', 0)
            if current_atr == 0:
                return proposed_rate
            
            # 使用ATR的5%作为价格调整幅度
            price_adjustment = current_atr * 0.05
            
            if side == 'long':
                # 多头加仓：使用稍低价格增加成交概率
                adjusted_price = proposed_rate - price_adjustment
                self.logger.debug(f"📈 多头加仓价格调整 - {pair}: {proposed_rate:.6f} -> {adjusted_price:.6f}")
                return max(adjusted_price, proposed_rate * 0.995)  # 最多调整0.5%
            else:
                # 空头加仓：使用稍高价格增加成交概率
                adjusted_price = proposed_rate + price_adjustment
                self.logger.debug(f"📉 空头加仓价格调整 - {pair}: {proposed_rate:.6f} -> {adjusted_price:.6f}")
                return min(adjusted_price, proposed_rate * 1.005)  # 最多调整0.5%
                
        except Exception as e:
            self.logger.warning(f"⚠️ 加仓价格调整失败 - {pair}: {e}")
            return proposed_rate


