#!/usr/bin/env python3
import requests
from datetime import datetime, timedelta

def test_oi_api():
    print("=== 测试持仓量API行为 ===")
    
    # 测试1: 不带时间范围
    print("\n1. 测试不带时间范围...")
    response = requests.get(
        'https://fapi.binance.com/futures/data/openInterestHist',
        params={
            'symbol': 'BTCUSDT',
            'period': '5m',
            'limit': 100
        },
        timeout=10
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"   获取记录数: {len(data)}")
        if data:
            print(f"   最新时间: {data[-1]['timestamp']}")
            print(f"   最旧时间: {data[0]['timestamp']}")
    else:
        print(f"   错误: {response.status_code}")
    
    # 测试2: 带短时间范围(1天)
    print("\n2. 测试1天时间范围...")
    end_time = int(datetime.now().timestamp() * 1000)
    start_time = int((datetime.now() - timedelta(days=1)).timestamp() * 1000)
    
    response = requests.get(
        'https://fapi.binance.com/futures/data/openInterestHist',
        params={
            'symbol': 'BTCUSDT',
            'period': '5m',
            'limit': 100,
            'startTime': start_time,
            'endTime': end_time
        },
        timeout=10
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"   获取记录数: {len(data)}")
        if data:
            print(f"   最新时间: {data[-1]['timestamp']}")
            print(f"   最旧时间: {data[0]['timestamp']}")
    else:
        print(f"   错误: {response.status_code}")

if __name__ == "__main__":
    test_oi_api() 