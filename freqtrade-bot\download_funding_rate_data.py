#!/usr/bin/env python3
"""
资金费率和持仓量数据下载器
功能：下载币安期货资金费率和持仓量历史数据，支持6个交易对
作者：AriQuantification
版本：1.0.0
"""

import os
import json
import time
import pandas as pd
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BinanceFundingDataDownloader:
    """币安资金费率和持仓量数据下载器"""
    
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 支持的交易对
        self.target_pairs = [
            'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 
            'XRPUSDT', 'BNBUSDT', 'SOLUSDT'
        ]
        
        # 数据存储路径
        self.data_dir = Path("user_data/data")
        self.funding_dir = self.data_dir / "funding_rates"
        self.oi_dir = self.data_dir / "open_interest"
        
        # 创建目录
        self.funding_dir.mkdir(parents=True, exist_ok=True)
        self.oi_dir.mkdir(parents=True, exist_ok=True)
        
    def get_funding_rate_history(self, symbol: str, start_time: Optional[int] = None, 
                                end_time: Optional[int] = None, limit: int = 1000) -> List[Dict]:
        """获取资金费率历史数据"""
        endpoint = "/fapi/v1/fundingRate"
        url = f"{self.base_url}{endpoint}"
        
        params = {
            'symbol': symbol,
            'limit': limit
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取{symbol}资金费率数据失败: {e}")
            return []
    
    def get_open_interest_history(self, symbol: str, period: str = "5m", 
                                 start_time: Optional[int] = None, 
                                 end_time: Optional[int] = None, limit: int = 500) -> List[Dict]:
        """获取持仓量历史数据"""
        endpoint = "/futures/data/openInterestHist"
        url = f"{self.base_url}{endpoint}"
        
        params = {
            'symbol': symbol,
            'period': period,
            'limit': limit
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取{symbol}持仓量数据失败: {e}")
            return []
    
    def download_funding_rate_data(self, symbol: str, days_back: int = 30) -> bool:
        """下载指定交易对的资金费率数据"""
        logger.info(f"开始下载{symbol}资金费率数据，回溯{days_back}天")
        
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days_back)).timestamp() * 1000)
        
        all_data = []
        current_start = start_time
        
        while current_start < end_time:
            data = self.get_funding_rate_history(
                symbol=symbol,
                start_time=current_start,
                end_time=end_time,
                limit=1000
            )
            
            if not data:
                break
                
            all_data.extend(data)
            
            # 更新起始时间为最后一条记录的时间
            if data:
                current_start = data[-1]['fundingTime'] + 1
            else:
                break
                
            # 避免API限制
            time.sleep(0.1)
        
        if all_data:
            # 转换为DataFrame并保存
            df = pd.DataFrame(all_data)
            df['fundingTime'] = pd.to_datetime(df['fundingTime'], unit='ms')
            df['fundingRate'] = df['fundingRate'].astype(float)
            df['markPrice'] = df['markPrice'].astype(float)
            
            # 按时间排序
            df = df.sort_values('fundingTime').reset_index(drop=True)
            
            # 保存到文件
            file_path = self.funding_dir / f"{symbol}_funding_rates.feather"
            df.to_feather(file_path)
            
            logger.info(f"{symbol}资金费率数据下载完成，共{len(df)}条记录")
            return True
        else:
            logger.warning(f"{symbol}资金费率数据下载失败")
            return False
    
    def download_open_interest_data(self, symbol: str, days_back: int = 30) -> bool:
        """下载指定交易对的持仓量数据"""
        logger.info(f"开始下载{symbol}持仓量数据，回溯{days_back}天")
        
        all_data = []
        
        # 对于30天数据，使用简化策略：分7天批次下载，避免时间戳问题
        batch_days = 7
        current_time = datetime.now()
        
        for batch_num in range(0, days_back, batch_days):
            actual_batch_days = min(batch_days, days_back - batch_num)
            batch_end = current_time - timedelta(days=batch_num)
            batch_start = current_time - timedelta(days=batch_num + actual_batch_days)
            
            start_time_ms = int(batch_start.timestamp() * 1000)
            end_time_ms = int(batch_end.timestamp() * 1000)
            
            logger.info(f"下载批次 {batch_num//batch_days + 1}: {batch_start.strftime('%Y-%m-%d %H:%M')} 到 {batch_end.strftime('%Y-%m-%d %H:%M')}")
            
            # 直接下载当前批次的数据，不进行复杂的时间戳更新
            batch_data = []
            for page in range(10):  # 最多10页，每页500条
                try:
                    data = self.get_open_interest_history(
                        symbol=symbol,
                        period="5m",
                        start_time=start_time_ms,
                        end_time=end_time_ms,
                        limit=500
                    )
                    
                    if not data:
                        logger.debug(f"批次 {batch_num//batch_days + 1}, 页面 {page + 1}: 无数据")
                        break
                    
                    # 验证时间戳合理性
                    valid_data = []
                    for item in data:
                        timestamp = item.get('timestamp', 0)
                        if start_time_ms <= timestamp <= end_time_ms:
                            valid_data.append(item)
                        else:
                            logger.debug(f"跳过无效时间戳: {timestamp}")
                    
                    if valid_data:
                        batch_data.extend(valid_data)
                        logger.debug(f"批次 {batch_num//batch_days + 1}, 页面 {page + 1}: 获取 {len(valid_data)} 条有效记录")
                        
                        # 如果返回数据少于500条，说明已经到了数据边界
                        if len(data) < 500:
                            break
                    else:
                        break
                        
                    # 更新起始时间为最后一条记录的下一毫秒
                    if valid_data:
                        start_time_ms = valid_data[-1]['timestamp'] + 1
                        # 如果更新后的时间超过了批次结束时间，退出
                        if start_time_ms >= end_time_ms:
                            break
                    
                    time.sleep(0.1)  # API限制
                    
                except Exception as e:
                    logger.error(f"批次 {batch_num//batch_days + 1}, 页面 {page + 1} 下载失败: {e}")
                    break
            
            if batch_data:
                all_data.extend(batch_data)
                logger.info(f"批次 {batch_num//batch_days + 1} 完成: {len(batch_data)} 条记录")
            else:
                logger.warning(f"批次 {batch_num//batch_days + 1} 无数据")
            
            # 批次间间隔
            time.sleep(0.3)
        
        if all_data:
            # 转换为DataFrame并保存
            df = pd.DataFrame(all_data)
            
            # 去重并按时间排序
            df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp').reset_index(drop=True)
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['sumOpenInterest'] = df['sumOpenInterest'].astype(float)
            df['sumOpenInterestValue'] = df['sumOpenInterestValue'].astype(float)
            
            # 保存到文件
            file_path = self.oi_dir / f"{symbol}_open_interest.feather"
            df.to_feather(file_path)
            
            logger.info(f"{symbol}持仓量数据下载完成，共{len(df)}条记录")
            return True
        else:
            logger.warning(f"{symbol}持仓量数据下载失败")
            return False
    
    def download_all_pairs_data(self, days_back: int = 30):
        """下载所有交易对的数据"""
        logger.info(f"开始下载{len(self.target_pairs)}个交易对前{days_back}天的数据")
        
        for symbol in self.target_pairs:
            logger.info(f"处理交易对: {symbol}")
            
            # 下载资金费率数据
            funding_success = self.download_funding_rate_data(symbol, days_back)
            
            # 下载持仓量数据
            oi_success = self.download_open_interest_data(symbol, days_back)
            
            if funding_success and oi_success:
                logger.info(f"{symbol}数据下载完成")
            else:
                logger.warning(f"{symbol}数据下载部分失败")
            
            # 交易对间间隔
            time.sleep(1)
        
        logger.info("所有交易对数据下载完成")
    
    def get_current_funding_info(self, symbol: str) -> Dict:
        """获取当前资金费率信息"""
        endpoint = "/fapi/v1/premiumIndex"
        url = f"{self.base_url}{endpoint}"
        
        params = {'symbol': symbol}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取{symbol}当前资金费率失败: {e}")
            return {}

if __name__ == "__main__":
    downloader = BinanceFundingDataDownloader()
    
    # 下载前1个月数据（30天）用于回测
    downloader.download_all_pairs_data(days_back=30)
    
    # 测试当前数据获取
    for symbol in downloader.target_pairs:
        current_info = downloader.get_current_funding_info(symbol)
        if current_info:
            logger.info(f"{symbol}当前资金费率: {current_info.get('lastFundingRate', 'N/A')}") 