#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外汇ADL策略测试脚本 - 基于我们优化的30.97%年化收益参数
使用MetaTrader5进行外汇交易
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

class ForexADLStrategy:
    """外汇ADL策略 - 移植自freqtrade优化参数"""
    
    def __init__(self):
        # 优化后的参数配置（30.97%年化收益）
        self.zscore_threshold = 1.92
        self.atr_profit_multiplier = 0.45
        self.roi_settings = {
            0: 0.038,   # 开仓即时3.8%ROI
            2: 0.018,   # 2分钟后1.8%ROI
            4: 0.009,   # 4分钟后0.9%ROI
            6: 0.000    # 6分钟后0%ROI
        }
        self.max_open_trades = 3
        self.tier3_strength_min = 1.2
        self.min_acceleration_ratio = 1.2
        
        # 外汇货币对
        self.forex_pairs = [
            "EURUSD", "GBPUSD", "USDJPY", 
            "AUDUSD", "USDCAD", "NZDUSD"
        ]
        
    def initialize_mt5(self):
        """初始化MT5连接"""
        # 启动MT5
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            print("错误信息:", mt5.last_error())
            return False
            
        print("✅ MT5连接成功")
        
        # 获取账户信息
        account_info = mt5.account_info()
        if account_info is not None:
            print(f"📊 账户信息:")
            print(f"   经纪商: {account_info.company}")
            print(f"   账户: {account_info.login}")
            print(f"   余额: ${account_info.balance:.2f}")
            print(f"   杠杆: 1:{account_info.leverage}")
        
        return True
    
    def get_forex_data(self, symbol, timeframe=mt5.TIMEFRAME_M5, bars=100):
        """获取外汇数据"""
        try:
            # 获取历史数据
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
            if rates is None or len(rates) == 0:
                print(f"⚠️  无法获取 {symbol} 数据")
                return None
                
            # 转换为DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            return df
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_adl_signal(self, df):
        """计算ADL信号 - 使用相同的算法逻辑"""
        if len(df) < 20:
            return 0, 0
            
        # 计算ADL指标
        high_low = df['high'] - df['low']
        close_low = df['close'] - df['low']
        high_close = df['high'] - df['close']
        
        # 计算Money Flow Multiplier
        mfm = np.where(high_low != 0, (close_low - high_close) / high_low, 0)
        
        # 计算Money Flow Volume
        mfv = mfm * df['tick_volume']
        
        # 计算累积分配线
        adl = mfv.cumsum()
        
        # 计算Z-Score
        adl_mean = adl.rolling(window=20).mean()
        adl_std = adl.rolling(window=20).std()
        z_score = (adl - adl_mean) / adl_std
        
        # 生成信号
        latest_zscore = z_score.iloc[-1]
        prev_zscore = z_score.iloc[-2]
        
        signal_strength = abs(latest_zscore)
        
        # 信号判断（基于我们的1.92阈值）
        if latest_zscore < -self.zscore_threshold and prev_zscore > -self.zscore_threshold:
            return 1, signal_strength  # 买入信号
        elif latest_zscore > self.zscore_threshold and prev_zscore < self.zscore_threshold:
            return -1, signal_strength  # 卖出信号
        
        return 0, signal_strength
    
    def test_strategy(self):
        """测试策略"""
        print("🚀 开始测试外汇ADL策略...")
        print(f"📈 使用参数: Z-Score阈值={self.zscore_threshold}, ATR倍数={self.atr_profit_multiplier}")
        
        signals_found = 0
        
        for symbol in self.forex_pairs:
            print(f"\n📊 分析 {symbol}...")
            
            # 获取数据
            df = self.get_forex_data(symbol)
            if df is None:
                continue
                
            # 计算信号
            signal, strength = self.calculate_adl_signal(df)
            
            current_price = df['close'].iloc[-1]
            
            if signal != 0:
                signals_found += 1
                signal_type = "🟢 买入" if signal > 0 else "🔴 卖出"
                print(f"   {signal_type} 信号！强度: {strength:.2f}")
                print(f"   当前价格: {current_price:.5f}")
                
                # 计算ATR止盈
                atr = self.calculate_atr(df)
                if signal > 0:
                    take_profit = current_price + (atr * self.atr_profit_multiplier)
                    print(f"   建议止盈: {take_profit:.5f}")
                else:
                    take_profit = current_price - (atr * self.atr_profit_multiplier)
                    print(f"   建议止盈: {take_profit:.5f}")
            else:
                print(f"   无信号 (Z-Score: {strength:.2f})")
        
        print(f"\n✅ 测试完成！发现 {signals_found} 个交易信号")
        
    def calculate_atr(self, df, period=14):
        """计算ATR"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        tr = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = tr.rolling(window=period).mean()
        
        return atr.iloc[-1]
    
    def __del__(self):
        """析构函数 - 关闭MT5连接"""
        mt5.shutdown()

def main():
    """主函数"""
    print("🌍 外汇ADL策略测试程序")
    print("基于30.97%年化收益的优化参数")
    print("=" * 50)
    
    # 创建策略实例
    strategy = ForexADLStrategy()
    
    # 初始化MT5
    if not strategy.initialize_mt5():
        print("❌ 无法连接到MetaTrader 5")
        print("\n📋 解决方案:")
        print("1. 确保已安装MetaTrader 5")
        print("2. 打开MT5并登录您的账户")
        print("3. 在MT5中，转到 工具 > 选项 > Expert Advisors")
        print("4. 勾选 '允许DLL导入' 和 '允许WebRequest'")
        return
    
    # 测试策略
    strategy.test_strategy()
    
    print("\n🎯 下一步:")
    print("1. 如果看到交易信号，说明策略工作正常")
    print("2. 可以开始进行历史回测")
    print("3. 在demo账户上测试实盘交易")

if __name__ == "__main__":
    main() 