 #!/usr/bin/env python3
"""
币安期货3分钟标记价格K线数据下载器

根据币安API文档下载3分钟标记价格K线数据：
https://developers.binance.com/docs/derivatives/usds-margined-futures/market-data/rest-api/Mark-Price-Kline-Candlestick-Data

使用方法：
python download_mark_price_3m.py --symbols BTC/USDT:USDT ETH/USDT:USDT --days 30
"""

import requests
import pandas as pd
import json
import time
import os
import argparse
from datetime import datetime, timedelta
from typing import List
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BinanceMarkPriceDownloader:
    """币安标记价格K线数据下载器"""
    
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.session = requests.Session()
        
    def symbol_to_binance_format(self, freqtrade_symbol: str) -> str:
        """将FreqTrade格式的交易对转换为币安格式"""
        # BTC/USDT:USDT -> BTCUSDT
        if ':' in freqtrade_symbol:
            base_quote = freqtrade_symbol.split(':')[0]
        else:
            base_quote = freqtrade_symbol
        return base_quote.replace('/', '')
    
    def symbol_to_filename(self, freqtrade_symbol: str) -> str:
        """将FreqTrade格式的交易对转换为文件名格式"""
        # BTC/USDT:USDT -> BTC_USDT_USDT
        return freqtrade_symbol.replace('/', '_').replace(':', '_')
    
    def download_mark_price_klines(self, symbol: str, interval: str = "3m", 
                                  start_time: int = None, end_time: int = None,
                                  limit: int = 1500) -> pd.DataFrame:
        """
        下载标记价格K线数据
        
        Parameters:
        - symbol: 币安格式的交易对 (如: BTCUSDT)
        - interval: K线间隔 (3m)
        - start_time: 开始时间戳(毫秒)
        - end_time: 结束时间戳(毫秒)
        - limit: 每次请求的最大K线数量
        """
        endpoint = f"{self.base_url}/fapi/v1/markPriceKlines"
        
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        try:
            response = self.session.get(endpoint, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                logger.warning(f"No data received for {symbol}")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'ignore', 'count', 'ignore2', 'ignore3', 'ignore4'
            ])
            
            # 只保留需要的列并转换数据类型
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            df['timestamp'] = pd.to_numeric(df['timestamp'])
            df['open'] = pd.to_numeric(df['open'])
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            df['close'] = pd.to_numeric(df['close'])
            df['volume'] = pd.to_numeric(df['volume'])
            
            # 转换时间戳为可读格式
            df['date'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            logger.info(f"Downloaded {len(df)} candles for {symbol} from {df['date'].min()} to {df['date'].max()}")
            return df
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading data for {symbol}: {e}")
            return pd.DataFrame()
    
    def download_historical_data(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """下载指定天数的历史数据"""
        binance_symbol = self.symbol_to_binance_format(symbol)
        
        # 计算时间范围
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
        
        all_data = []
        current_start = start_time
        
        logger.info(f"Downloading {days} days of mark price data for {symbol}")
        
        while current_start < end_time:
            # 每次最多下载1500根K线（币安API限制）
            df = self.download_mark_price_klines(
                binance_symbol,
                start_time=current_start,
                end_time=end_time,
                limit=1500
            )
            
            if df.empty:
                break
                
            all_data.append(df)
            
            # 更新下次请求的开始时间
            current_start = df['timestamp'].max() + 1
            
            # 避免API限制
            time.sleep(0.1)
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df = combined_df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
            logger.info(f"Total downloaded: {len(combined_df)} candles for {symbol}")
            return combined_df
        else:
            logger.warning(f"No data downloaded for {symbol}")
            return pd.DataFrame()
    
    def save_to_freqtrade_format(self, df: pd.DataFrame, symbol: str, data_dir: str = "user_data/data/binance"):
        """保存数据为FreqTrade格式"""
        if df.empty:
            logger.warning(f"No data to save for {symbol}")
            return
        
        # 创建目录
        futures_dir = os.path.join(data_dir, "futures")
        os.makedirs(futures_dir, exist_ok=True)
        
        # 文件名格式：BTC_USDT_USDT-3m-mark.json
        filename = f"{self.symbol_to_filename(symbol)}-3m-mark.json"
        filepath = os.path.join(futures_dir, filename)
        
        # 转换为FreqTrade期望的格式：[[timestamp, open, high, low, close, volume]]
        freqtrade_data = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].values.tolist()
        
        # 保存为JSON
        with open(filepath, 'w') as f:
            json.dump(freqtrade_data, f)
        
        logger.info(f"Saved {len(freqtrade_data)} candles to {filepath}")

def main():
    parser = argparse.ArgumentParser(description='Download Binance futures mark price data')
    parser.add_argument('--symbols', nargs='+', required=True,
                       help='Symbols to download (e.g., BTC/USDT:USDT ETH/USDT:USDT)')
    parser.add_argument('--days', type=int, default=30,
                       help='Number of days to download (default: 30)')
    parser.add_argument('--data-dir', default='user_data/data/binance',
                       help='Data directory (default: user_data/data/binance)')
    
    args = parser.parse_args()
    
    downloader = BinanceMarkPriceDownloader()
    
    for symbol in args.symbols:
        logger.info(f"Processing {symbol}...")
        
        # 下载数据
        df = downloader.download_historical_data(symbol, args.days)
        
        # 保存数据
        downloader.save_to_freqtrade_format(df, symbol, args.data_dir)
        
        logger.info(f"Completed {symbol}")
        time.sleep(0.5)  # 避免API限制
    
    logger.info("All downloads completed!")

if __name__ == "__main__":
    main()