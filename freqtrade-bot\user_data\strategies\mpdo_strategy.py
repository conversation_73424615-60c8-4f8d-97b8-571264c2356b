#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MPDO策略 - 标记价格偏离振荡器策略（核心逻辑修复版）

功能说明：
- 基于标记价格与现货价格偏离的趋势跟随策略
- 核心出场：MPDO零轴交叉（价格偏离修复时止盈）
- 15分钟时间止损，符合文档建议
- 优化ROI梯度配合短周期动量特性
"""

from freqtrade.strategy import IStrategy, IntParameter, DecimalParameter, informative, Trade
from pandas import DataFrame
import pandas as pd
import talib.abstract as ta
import numpy as np
import logging
from datetime import datetime
import freqtrade.vendor.qtpylib.indicators as qtpylib
import os
from typing import Optional

# 添加详细的日志设置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MPDOStrategy(IStrategy):
    """
    MPDO策略 - 核心逻辑修复版
    
    核心修复：
    1. 恢复MPDO零轴交叉出场逻辑
    2. 调整时间止损为15分钟（文档建议）
    3. 优化ROI梯度适配短周期策略
    4. 微调偏离阈值提高信号质量
    """

    # 策略基本信息
    INTERFACE_VERSION = 3
    timeframe = '3m'
    can_short = True  # 支持做空

    # 启动期设置
    startup_candle_count: int = 120

    # 从config加载，不再硬编码
    minimal_roi = {}
    stoploss = -0.05  # 默认值，会被config覆盖
    trailing_stop = False  # 默认值，会被config覆盖
    use_custom_stoploss = True

    # ATR相关参数
    atr_period = IntParameter(10, 20, default=14, space="sell")
    atr_multiplier = DecimalParameter(1.5, 3.0, default=2.0, space="sell")

    def __init__(self, config: dict) -> None:
        super().__init__(config)
        
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 从config加载策略参数
        self.strategy_config = config.get('mpdo_strategy', {})
        
        # 直接从config加载所有参数，不使用fallback
        self.lookback_period = self.strategy_config['lookback_period']
        self.deviation_threshold = self.strategy_config['deviation_threshold']
        self.volume_threshold = self.strategy_config['volume_threshold']
        self.atr_period = self.strategy_config['atr_period']
        self.time_stop_minutes = self.strategy_config['time_stop_minutes']
        self.quick_loss_threshold = self.strategy_config['quick_loss_threshold']
        
        # 过滤开关
        self.enable_price_acceleration = self.strategy_config['enable_price_acceleration']
        self.enable_volume_burst = self.strategy_config['enable_volume_burst']
        self.enable_trend_confirmation = self.strategy_config['enable_trend_confirmation']
        
        # 从config加载交易参数
        if 'minimal_roi' in self.strategy_config:
            self.minimal_roi = self.strategy_config['minimal_roi']
        if 'stoploss' in self.strategy_config:
            self.stoploss = self.strategy_config['stoploss']
        if 'trailing_stop' in self.strategy_config:
            self.trailing_stop = self.strategy_config['trailing_stop']
        if 'use_custom_stoploss' in self.strategy_config:
            self.use_custom_stoploss = self.strategy_config['use_custom_stoploss']
        
        self.logger.info("🎯 MPDO策略 - 配置化版本...")
        self.logger.info(f"📊 偏离阈值: {self.deviation_threshold}σ")
        self.logger.info(f"💨 成交量阈值: {self.volume_threshold}x")
        self.logger.info(f"⏰ 时间止损: {self.time_stop_minutes}分钟")
        self.logger.info(f"🛡️ 固定止损: {self.stoploss*100:.1f}%")
        self.logger.info(f"⚡ 快速止损: {self.quick_loss_threshold*100:.1f}%")

    def informative_pairs(self):
        """定义需要获取的信息对 - 用于实盘和dry run模式"""
        if not self.dp or self.dp.runmode.value == 'backtest':
            # 回测模式不需要informative pairs
            return []
        
        # 实盘和dry run模式需要标记价格数据
        pairs = []
        if hasattr(self.dp, 'current_whitelist'):
            whitelist = self.dp.current_whitelist()
            for pair in whitelist:
                # 添加标记价格数据 - 使用相同的时间框架
                pairs.append((pair, self.timeframe, "mark"))
        
        return pairs

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """填充技术指标 - 适配不同运行模式"""
        pair = metadata.get('pair', 'UNKNOWN')
        
        # 检测运行模式
        if self.dp and self.dp.runmode.value in ('live', 'dry_run'):
            # 实盘/干跑模式：使用informative pairs获取实时标记价格
            merged_df = self._get_live_mark_price_data(dataframe, pair)
        else:
            # 回测模式：使用历史文件数据
            mark_price_df = self._load_mark_price_data(pair)
            if mark_price_df is None or mark_price_df.empty:
                self.logger.error(f"❌ 无法获取标记价格数据 - {pair}")
                return self._add_basic_indicators(dataframe)
            merged_df = self._merge_mark_price_data(dataframe, mark_price_df, pair)
        
        # 计算技术指标
        merged_df['atr'] = ta.ATR(merged_df, timeperiod=self.atr_period)
        merged_df = self._calculate_mpdo_indicators(merged_df)
        merged_df = self._calculate_confirmation_indicators(merged_df)
        
        self.logger.info(f"✅ 指标计算完成 - {pair} (模式: {self.dp.runmode.value if self.dp else 'unknown'})")
        return merged_df

    def _get_live_mark_price_data(self, dataframe: DataFrame, pair: str) -> DataFrame:
        """获取实时标记价格数据 - 用于实盘和dry run模式（带简单重试）"""
        max_retries = 3
        retry_delay = 2  # 秒
        
        for attempt in range(max_retries):
            try:
                if not self.dp:
                    self.logger.error(f"❌ DataProvider不可用 - {pair}")
                    return self._add_basic_indicators(dataframe)
                
                # 从informative pairs获取标记价格数据
                mark_df = self.dp.get_pair_dataframe(pair=pair, timeframe=self.timeframe, candle_type="mark")
                
                if mark_df.empty:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"⚠️ 标记价格数据为空，重试 {attempt + 1}/{max_retries} - {pair}")
                        import time
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.logger.warning(f"⚠️ 标记价格数据为空，使用降级方案 - {pair}")
                        return self._add_basic_indicators(dataframe)
                
                # 重命名标记价格列
                mark_df_renamed = mark_df.copy()
                mark_df_renamed = mark_df_renamed.rename(columns={'close': 'mark_price_close'})
                
                # 合并数据 - 使用相同的索引进行合并
                merged_df = dataframe.join(mark_df_renamed[['mark_price_close']], how='left')
                
                # 填充缺失值
                merged_df['mark_price_close'] = merged_df['mark_price_close'].ffill()
                
                valid_count = merged_df['mark_price_close'].notna().sum()
                total_count = len(merged_df)
                
                # 检查数据有效性
                if valid_count == 0:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"⚠️ 标记价格数据无效，重试 {attempt + 1}/{max_retries} - {pair}")
                        import time
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.logger.warning(f"⚠️ 标记价格数据无效，使用降级方案 - {pair}")
                        return self._add_basic_indicators(dataframe)
                
                self.logger.info(f"📊 实时标记价格数据合并完成 - {pair}: {valid_count}/{total_count} ({valid_count/total_count*100:.1f}%)")
                
                if attempt > 0:
                    self.logger.info(f"✅ 重试成功 - {pair}")
                
                return merged_df
                
            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"⚠️ 获取数据失败，重试 {attempt + 1}/{max_retries} - {pair}: {str(e)}")
                    import time
                    time.sleep(retry_delay)
                    continue
                else:
                    self.logger.error(f"❌ 多次重试后仍获取失败，使用降级方案 - {pair}: {str(e)}")
                    return

    def _load_mark_price_data(self, pair: str) -> Optional[DataFrame]:
        """加载标记价格数据 - 用于回测模式"""
        try:
            # 处理多种交易对格式
            symbol_filename = pair.replace('/', '_').replace(':', '_')
            # 尝试不同的文件命名格式
            possible_paths = [
                f"user_data/data/binance/futures/{symbol_filename}-3m-mark.feather",
                f"user_data/data/binance/futures/{symbol_filename}_USDT-3m-mark.feather",
                f"user_data/data/binance/futures/{symbol_filename.replace('_USDT', '')}_USDT_USDT-3m-mark.feather"
            ]
            
            for mark_file_path in possible_paths:
                if os.path.exists(mark_file_path):
                    mark_df = pd.read_feather(mark_file_path)
                    self.logger.info(f"✅ 标记价格数据: {len(mark_df)}条记录 - {mark_file_path}")
                    return mark_df
            
            self.logger.warning(f"⚠️ 未找到标记价格文件: {pair}")
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 标记价格加载失败: {str(e)}")
            return None

    def _merge_mark_price_data(self, dataframe: DataFrame, mark_df: DataFrame, pair: str) -> DataFrame:
        """合并标记价格数据"""
        try:
            mark_df_renamed = mark_df.copy()
            mark_df_renamed = mark_df_renamed.rename(columns={'close': 'mark_price_close'})
            merged_df = dataframe.join(mark_df_renamed[['mark_price_close']], how='left')
            
            valid_count = merged_df['mark_price_close'].notna().sum()
            self.logger.info(f"📊 数据合并: {valid_count}/{len(merged_df)} ({valid_count/len(merged_df)*100:.1f}%)")
            
            return merged_df
            
        except Exception as e:
            self.logger.error(f"❌ 数据合并失败: {str(e)}")
            return dataframe

    def _add_basic_indicators(self, dataframe: DataFrame) -> DataFrame:
        """基础指标（无标记价格时的降级方案）"""
        dataframe['ema_5'] = ta.EMA(dataframe, timeperiod=5)
        dataframe['volume_ma_20'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period)
        return dataframe
    
    def _calculate_mpdo_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算MPDO核心指标"""
        if 'mark_price_close' not in dataframe.columns:
            self.logger.error("❌ 缺少标记价格数据")
            return dataframe
        
        # MPDO计算
        dataframe['divergence'] = dataframe['close'] - dataframe['mark_price_close']
        dataframe['mpdo'] = (dataframe['divergence'] / dataframe['mark_price_close']) * 10000
        dataframe['mpdo'] = dataframe['mpdo'].replace([np.inf, -np.inf], np.nan)
        
        # 动态阈值
        dataframe['mpdo_std'] = dataframe['mpdo'].rolling(window=self.lookback_period, min_periods=1).std()
        dataframe['upper_band'] = dataframe['mpdo_std'] * self.deviation_threshold
        dataframe['lower_band'] = -dataframe['mpdo_std'] * self.deviation_threshold
        
        return dataframe
    
    def _calculate_confirmation_indicators(self, dataframe: DataFrame) -> DataFrame:
        """计算确认指标"""
        # 基础指标
        dataframe['ema_5'] = ta.EMA(dataframe, timeperiod=5)
        dataframe['ema_20'] = ta.EMA(dataframe, timeperiod=20)
        dataframe['volume_ma_20'] = dataframe['volume'].rolling(window=20).mean()
        
        # 确认指标
        dataframe['ema_5_rising'] = dataframe['ema_5'] > dataframe['ema_5'].shift(1)
        dataframe['ema_5_falling'] = dataframe['ema_5'] < dataframe['ema_5'].shift(1)
        
        # 过滤条件
        if self.enable_volume_burst:
            dataframe['volume_burst'] = dataframe['volume'] > (dataframe['volume_ma_20'] * self.volume_threshold)
        else:
            dataframe['volume_burst'] = True
            
        if self.enable_price_acceleration:
            # 价格加速度检测 - 识别瀑布/火箭效应
            dataframe['price_change_1'] = dataframe['close'].pct_change(1)
            dataframe['price_change_2'] = dataframe['close'].pct_change(2) 
            dataframe['price_acceleration'] = abs(dataframe['price_change_1']) > abs(dataframe['price_change_2'])
            dataframe['extreme_move'] = abs(dataframe['price_change_1']) > 0.01  # 1%极端变化
            dataframe['price_momentum'] = dataframe['price_acceleration'] & dataframe['extreme_move']
        else:
            dataframe['price_momentum'] = True
            
        if self.enable_trend_confirmation:
            dataframe['trend_slope'] = (dataframe['ema_20'] - dataframe['ema_20'].shift(3)) / dataframe['ema_20'].shift(3) * 100
            dataframe['trend_strong'] = abs(dataframe['trend_slope']) > 0.1
        else:
            dataframe['trend_strong'] = True
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """入场信号"""
        pair = metadata.get('pair', 'UNKNOWN')
        
        # 验证必需指标
        required_columns = ['mpdo', 'upper_band', 'lower_band', 'volume_burst', 'price_momentum', 'trend_strong']
        missing = [col for col in required_columns if col not in dataframe.columns]
        if missing:
            self.logger.error(f"❌ 缺少指标 - {pair}: {missing}")
            return dataframe
        
        # 多头入场：现货高于标记价格，顺势做多
        long_condition = (
            (dataframe['mpdo'] > dataframe['upper_band']) &
            (dataframe['volume_burst'] == True) &
            (dataframe['price_momentum'] == True) &
            (dataframe['trend_strong'] == True) &
            (dataframe['ema_5_rising'] == True) &
            (dataframe['volume'] > 0) &
            (dataframe['mpdo'].notna())
        )
        
        # 空头入场：现货低于标记价格，顺势做空
        short_condition = (
            (dataframe['mpdo'] < dataframe['lower_band']) &
            (dataframe['volume_burst'] == True) &
            (dataframe['price_momentum'] == True) &
            (dataframe['trend_strong'] == True) &
            (dataframe['ema_5_falling'] == True) &
            (dataframe['volume'] > 0) &
            (dataframe['mpdo'].notna())
        )
        
        dataframe.loc[long_condition, 'enter_long'] = 1
        dataframe.loc[short_condition, 'enter_short'] = 1
        
        # 统计信号
        long_signals = long_condition.sum()
        short_signals = short_condition.sum()
        
        if long_signals > 0 or short_signals > 0:
            self.logger.info(f"🎯 MPDO信号 - {pair}: 多头={long_signals}, 空头={short_signals}")
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """MPDO核心出场逻辑 - 零轴交叉"""
        pair = metadata.get('pair', 'UNKNOWN')
        
        # 验证MPDO指标存在
        if 'mpdo' not in dataframe.columns:
            self.logger.error(f"❌ 缺少MPDO指标 - {pair}")
            return dataframe
        
        # 多头出场：MPDO下穿零轴（偏离修复，动量释放）
        long_exit = qtpylib.crossed_below(dataframe['mpdo'], 0)
        
        # 空头出场：MPDO上穿零轴（偏离修复，动量释放）
        short_exit = qtpylib.crossed_above(dataframe['mpdo'], 0)
        
        dataframe.loc[long_exit, 'exit_long'] = 1
        dataframe.loc[short_exit, 'exit_short'] = 1
        
        # 统计出场信号
        long_exits = long_exit.sum()
        short_exits = short_exit.sum()
        
        if long_exits > 0 or short_exits > 0:
            self.logger.info(f"🚪 MPDO零轴交叉出场 - {pair}: 多头={long_exits}, 空头={short_exits}")
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """正确的自定义止损逻辑"""
        try:
            # 计算持仓时间（分钟）
            trade_duration_minutes = (current_time - trade.open_date_utc).total_seconds() / 60
            
            # 快速亏损止损：如果亏损超过阈值，使用快速止损
            if current_profit <= self.quick_loss_threshold:
                self.logger.info(f"⚡ 快速亏损止损 - {pair}: {current_profit:.3f}")
                return self.quick_loss_threshold  # 返回-0.015
            
            # 时间止损逻辑：超时则使用默认止损
            if trade_duration_minutes >= self.time_stop_minutes:
                self.logger.info(f"⏰ {self.time_stop_minutes}分钟时间止损 - {pair}: {trade_duration_minutes:.1f}分钟")
                return self.stoploss  # 返回-0.05
            
            # 不满足任何条件，保持默认止损
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 自定义止损计算失败 - {pair}: {str(e)}")
            return None

