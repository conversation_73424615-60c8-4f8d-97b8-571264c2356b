"""
优化的数据存储实现

提供高性能的数据存储方案，包括数据压缩和快速访问特性。
"""

import os
import glob
import shutil
import logging
import gzip
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple

import pandas as pd
import numpy as np

from data.storage.csv_storage import CSVDataStorage
from data.structures import OHLCVColumns
from data.utils import get_timeframe_file_path


class OptimizedStorage(CSVDataStorage):
    """
    优化的数据存储
    
    扩展基本的CSV存储，添加数据压缩、完整性检查和性能优化功能。
    """
    
    def __init__(self, data_dir: str, compression: bool = True, 
                chunk_size: Optional[int] = None, 
                auto_repair: bool = True):
        """
        初始化优化的数据存储
        
        Args:
            data_dir: 数据存储的根目录
            compression: 是否启用数据压缩
            chunk_size: 文件分块大小（行数），如果为None则不分块
            auto_repair: 是否自动修复损坏的数据
        """
        super().__init__(data_dir)
        self.compression = compression
        self.chunk_size = chunk_size
        self.auto_repair = auto_repair
    
    def save_data(self, data: pd.DataFrame, symbol: str, timeframe: str) -> None:
        """
        保存市场数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            symbol: 交易对或资产代码
            timeframe: 时间周期
        """
        if data.empty:
            self.logger.warning(f"尝试保存空数据: {symbol} {timeframe}")
            return
        
        # 确保数据索引是datetime类型
        if not isinstance(data.index, pd.DatetimeIndex):
            if OHLCVColumns.TIMESTAMP in data.columns:
                data = data.set_index(OHLCVColumns.TIMESTAMP)
            else:
                raise ValueError("数据必须包含时间戳列或以时间戳为索引")
        
        # 获取文件路径
        file_path = self._get_storage_path(symbol, timeframe)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 如果使用压缩
        if self.compression:
            # 使用gzip压缩
            with gzip.open(file_path, 'wt') as f:
                data.to_csv(f)
            self.logger.info(f"数据已压缩保存到: {file_path}, {len(data)}行")
        else:
            # 普通CSV保存
            data.to_csv(file_path)
            self.logger.info(f"数据已保存到: {file_path}, {len(data)}行")
    
    def load_data(self, symbol: str, timeframe: str, 
                 start_time: Optional[datetime] = None, 
                 end_time: Optional[datetime] = None) -> pd.DataFrame:
        """
        加载市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        file_path = self._get_storage_path(symbol, timeframe)
        
        if not os.path.exists(file_path):
            self.logger.warning(f"数据文件不存在: {file_path}")
            return pd.DataFrame()
        
        try:
            # 根据文件扩展名判断是否使用解压缩
            if file_path.endswith('.gz'):
                with gzip.open(file_path, 'rt') as f:
                    df = pd.read_csv(f, index_col=0, parse_dates=True)
            else:
                df = pd.read_csv(file_path, index_col=0, parse_dates=True)
            
            # 筛选时间范围
            if start_time is not None:
                df = df[df.index >= start_time]
            if end_time is not None:
                df = df[df.index <= end_time]
            
            self.logger.info(f"从{file_path}加载了{len(df)}行数据")
            return df
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {file_path}, 错误: {str(e)}")
            
            # 如果启用了自动修复，尝试修复数据
            if self.auto_repair:
                self.logger.info(f"尝试修复数据文件: {file_path}")
                if self._repair_data_file(file_path):
                    self.logger.info(f"数据文件已修复，重新尝试加载")
                    return self.load_data(symbol, timeframe, start_time, end_time)
            
            return pd.DataFrame()
    
    def update_data(self, data: pd.DataFrame, symbol: str, timeframe: str) -> None:
        """
        更新现有数据，合并新数据并去除重复项
        
        Args:
            data: 新的OHLCV数据
            symbol: 交易对或资产代码
            timeframe: 时间周期
        """
        if data.empty:
            self.logger.warning(f"尝试更新空数据: {symbol} {timeframe}")
            return
        
        # 确保数据索引是datetime类型
        if not isinstance(data.index, pd.DatetimeIndex):
            if OHLCVColumns.TIMESTAMP in data.columns:
                data = data.set_index(OHLCVColumns.TIMESTAMP)
            else:
                raise ValueError("数据必须包含时间戳列或以时间戳为索引")
        
        # 检查数据完整性
        if not self._check_data_integrity(data):
            self.logger.warning(f"输入数据完整性检查失败: {symbol} {timeframe}")
            # 尝试清理数据
            data = self._clean_data(data)
        
        # 尝试加载现有数据
        if self.has_data(symbol, timeframe):
            existing_data = self.load_data(symbol, timeframe)
            
            # 合并数据
            if not existing_data.empty:
                # 确保索引不冲突
                combined_data = pd.concat([existing_data, data])
                # 按时间排序
                combined_data = combined_data.sort_index()
                # 删除重复的索引，保留最新的数据
                combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
                
                # 保存合并后的数据
                self.save_data(combined_data, symbol, timeframe)
                
                # 检查数据完整性
                if self._check_data_integrity(combined_data):
                    self.logger.info(f"更新数据成功: {symbol} {timeframe}, 新增{len(data)}行，合并后{len(combined_data)}行")
                else:
                    self.logger.warning(f"合并后的数据完整性检查失败: {symbol} {timeframe}")
            else:
                # 没有现有数据，直接保存新数据
                self.save_data(data, symbol, timeframe)
                self.logger.info(f"新增数据: {symbol} {timeframe}, {len(data)}行")
        else:
            # 没有现有数据，直接保存新数据
            self.save_data(data, symbol, timeframe)
            self.logger.info(f"新增数据: {symbol} {timeframe}, {len(data)}行")
    
    def has_data(self, symbol: str, timeframe: str) -> bool:
        """
        检查是否有指定的市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            如果存在数据返回True，否则返回False
        """
        file_path = self._get_storage_path(symbol, timeframe)
        exists = os.path.exists(file_path)
        
        if exists:
            # 尝试读取文件头部以验证是否为有效的数据文件
            try:
                if file_path.endswith('.gz'):
                    with gzip.open(file_path, 'rt') as f:
                        header = f.readline().strip()
                        return len(header) > 0
                else:
                    with open(file_path, 'r') as f:
                        header = f.readline().strip()
                        return len(header) > 0
            except Exception as e:
                self.logger.error(f"验证数据文件失败: {file_path}, 错误: {str(e)}")
                return False
        
        return False
    
    def check_data_integrity(self, symbol: str, timeframe: str) -> bool:
        """
        检查数据文件的完整性
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            数据完整性检查结果，成功返回True
        """
        if not self.has_data(symbol, timeframe):
            return False
        
        try:
            data = self.load_data(symbol, timeframe)
            return self._check_data_integrity(data)
        except Exception as e:
            self.logger.error(f"数据完整性检查失败: {symbol} {timeframe}, 错误: {str(e)}")
            return False
    
    def _check_data_integrity(self, data: pd.DataFrame) -> bool:
        """
        检查DataFrame的数据完整性
        
        Args:
            data: 要检查的DataFrame
            
        Returns:
            数据完整性检查结果，成功返回True
        """
        if data.empty:
            return False
        
        # 检查必需的列
        required_columns = [OHLCVColumns.OPEN, OHLCVColumns.HIGH, 
                          OHLCVColumns.LOW, OHLCVColumns.CLOSE,
                          OHLCVColumns.VOLUME]
        
        for col in required_columns:
            if col not in data.columns:
                self.logger.error(f"数据缺少必需的列: {col}")
                return False
        
        # 检查数据类型
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(data[col]):
                self.logger.error(f"列 {col} 不是数值类型")
                return False
        
        # 检查缺失值
        missing_count = data[required_columns].isna().sum().sum()
        if missing_count > 0:
            self.logger.warning(f"数据包含 {missing_count} 个缺失值")
            # 对于少量缺失值，我们可能仍然认为数据是有效的
            if missing_count > len(data) * 0.01:  # 如果缺失值超过1%
                return False
        
        # 检查OHLC的逻辑关系
        invalid_rows = ((data[OHLCVColumns.HIGH] < data[OHLCVColumns.LOW]) | 
                       (data[OHLCVColumns.HIGH] < data[OHLCVColumns.OPEN]) | 
                       (data[OHLCVColumns.HIGH] < data[OHLCVColumns.CLOSE]) |
                       (data[OHLCVColumns.LOW] > data[OHLCVColumns.OPEN]) |
                       (data[OHLCVColumns.LOW] > data[OHLCVColumns.CLOSE]))
        
        invalid_count = invalid_rows.sum()
        if invalid_count > 0:
            self.logger.warning(f"数据包含 {invalid_count} 行OHLC逻辑不一致的数据")
            # 对于少量无效行，我们可能仍然认为数据是有效的
            if invalid_count > len(data) * 0.01:  # 如果无效行超过1%
                return False
        
        # 检查成交量非负
        if (data[OHLCVColumns.VOLUME] < 0).any():
            self.logger.error("数据包含负成交量")
            return False
        
        return True
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        清理DataFrame数据，修复常见问题
        
        Args:
            data: 要清理的DataFrame
            
        Returns:
            清理后的DataFrame
        """
        if data.empty:
            return data
        
        # 复制数据以避免修改原始数据
        data = data.copy()
        
        # 确保所有必需的列存在
        required_columns = [OHLCVColumns.OPEN, OHLCVColumns.HIGH, 
                          OHLCVColumns.LOW, OHLCVColumns.CLOSE,
                          OHLCVColumns.VOLUME]
        
        for col in required_columns:
            if col not in data.columns:
                # 尝试添加缺失的列
                if col == OHLCVColumns.OPEN:
                    data[col] = data[OHLCVColumns.CLOSE]
                elif col == OHLCVColumns.HIGH:
                    data[col] = data[[OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].max(axis=1)
                elif col == OHLCVColumns.LOW:
                    data[col] = data[[OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].min(axis=1)
                elif col == OHLCVColumns.CLOSE:
                    data[col] = data[OHLCVColumns.OPEN]
                elif col == OHLCVColumns.VOLUME:
                    data[col] = 0
        
        # 处理缺失值
        # 对于OHLC使用前向填充，对于成交量使用0填充
        data[required_columns[:-1]] = data[required_columns[:-1]].ffill()
        data[OHLCVColumns.VOLUME] = data[OHLCVColumns.VOLUME].fillna(0)
        
        # 修复OHLC的逻辑关系
        # 确保HIGH >= MAX(OPEN, CLOSE)
        data[OHLCVColumns.HIGH] = data[[OHLCVColumns.HIGH, OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].max(axis=1)
        
        # 确保LOW <= MIN(OPEN, CLOSE)
        data[OHLCVColumns.LOW] = data[[OHLCVColumns.LOW, OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].min(axis=1)
        
        # 确保成交量非负
        data[OHLCVColumns.VOLUME] = data[OHLCVColumns.VOLUME].clip(lower=0)
        
        return data
    
    def _repair_data_file(self, file_path: str) -> bool:
        """
        尝试修复损坏的数据文件
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            修复结果，成功返回True
        """
        try:
            # 创建备份文件
            backup_path = f"{file_path}.bak"
            shutil.copy2(file_path, backup_path)
            self.logger.info(f"已创建数据文件备份: {backup_path}")
            
            # 尝试读取数据，跳过错误行
            if file_path.endswith('.gz'):
                with gzip.open(file_path, 'rt') as f:
                    df = pd.read_csv(f, index_col=0, parse_dates=True,
                                    on_bad_lines='skip')
            else:
                df = pd.read_csv(file_path, index_col=0, parse_dates=True,
                               on_bad_lines='skip')
            
            # 清理数据
            df = self._clean_data(df)
            
            # 保存修复后的数据
            if file_path.endswith('.gz'):
                with gzip.open(file_path, 'wt') as f:
                    df.to_csv(f)
            else:
                df.to_csv(file_path)
            
            self.logger.info(f"数据文件已修复: {file_path}, {len(df)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"修复数据文件失败: {file_path}, 错误: {str(e)}")
            
            # 尝试恢复备份
            backup_path = f"{file_path}.bak"
            if os.path.exists(backup_path):
                try:
                    shutil.copy2(backup_path, file_path)
                    self.logger.info(f"已从备份恢复数据文件: {file_path}")
                except Exception:
                    pass
            
            return False

    def resample_data(self, symbol: str, source_timeframe: str, target_timeframe: str) -> bool:
        """
        重采样数据到不同的时间框架

        Args:
            symbol: 交易对或资产代码
            source_timeframe: 源时间框架（如'1m'）
            target_timeframe: 目标时间框架（如'5m'）

        Returns:
            重采样结果，成功返回True
        """
        try:
            # 检查源数据是否存在
            if not self.has_data(symbol, source_timeframe):
                self.logger.error(f"源数据不存在: {symbol} {source_timeframe}")
                return False

            # 加载源数据
            source_data = self.load_data(symbol, source_timeframe)
            if source_data.empty:
                self.logger.error(f"源数据为空: {symbol} {source_timeframe}")
                return False

            # 转换时间框架字符串为pandas频率
            freq_map = {
                '1m': '1min',
                '5m': '5min',
                '15m': '15min',
                '30m': '30min',
                '1h': '1H',
                '4h': '4H',
                '1d': '1D'
            }

            if target_timeframe not in freq_map:
                self.logger.error(f"不支持的目标时间框架: {target_timeframe}")
                return False

            target_freq = freq_map[target_timeframe]

            # 执行重采样
            resampled_data = source_data.resample(target_freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            if resampled_data.empty:
                self.logger.error(f"重采样后数据为空: {symbol} {source_timeframe} -> {target_timeframe}")
                return False

            # 保存重采样后的数据
            self.save_data(resampled_data, symbol, target_timeframe)

            self.logger.info(f"数据重采样成功: {symbol} {source_timeframe} -> {target_timeframe}")
            self.logger.info(f"源数据: {len(source_data)}行, 重采样后: {len(resampled_data)}行")

            return True

        except Exception as e:
            self.logger.error(f"数据重采样失败: {symbol} {source_timeframe} -> {target_timeframe}, 错误: {str(e)}")
            return False

    def _get_storage_path(self, symbol: str, timeframe: str) -> str:
        """
        获取数据存储路径
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            数据存储的完整路径
        """
        # 构建基本路径
        base_path = get_timeframe_file_path(self.data_dir, symbol, timeframe)
        
        # 如果使用压缩，添加.gz扩展名
        if self.compression:
            return f"{base_path}.gz"
        
        return base_path
        
    def get_data_info(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """
        获取数据信息
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            包含数据信息的字典，如开始时间、结束时间、数据点数量等
        """
        if not self.has_data(symbol, timeframe):
            return {
                "exists": False,
                "rows": 0,
                "start_time": None,
                "end_time": None,
                "timeframe": timeframe,
                "symbol": symbol
            }
        
        try:
            # 获取文件路径
            file_path = self._get_storage_path(symbol, timeframe)
            
            # 获取行数（压缩文件需要先读取）
            if file_path.endswith('.gz'):
                with gzip.open(file_path, 'rt') as f:
                    lines = sum(1 for _ in f) - 1  # 减去标题行
            else:
                with open(file_path, 'r') as f:
                    lines = sum(1 for _ in f) - 1  # 减去标题行
            
            if lines <= 0:
                return {
                    "exists": True,
                    "rows": 0,
                    "start_time": None,
                    "end_time": None,
                    "timeframe": timeframe,
                    "symbol": symbol
                }
            
            # 读取第一行和最后一行以获取时间范围（压缩文件处理）
            if file_path.endswith('.gz'):
                with gzip.open(file_path, 'rt') as f:
                    df_first = pd.read_csv(f, index_col=0, parse_dates=True, nrows=1)
                
                # 使用pandas的tail方法获取最后一行
                with gzip.open(file_path, 'rt') as f:
                    df_last = pd.read_csv(f, index_col=0, parse_dates=True, skiprows=range(1, lines))
            else:
                df_first = pd.read_csv(file_path, index_col=0, parse_dates=True, nrows=1)
                df_last = pd.read_csv(file_path, index_col=0, parse_dates=True, skiprows=range(1, lines))
            
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            return {
                "exists": True,
                "rows": lines,
                "start_time": df_first.index[0] if not df_first.empty else None,
                "end_time": df_last.index[0] if not df_last.empty else None,
                "timeframe": timeframe,
                "symbol": symbol,
                "file_size": file_size,
                "file_path": file_path,
                "compressed": file_path.endswith('.gz')
            }
            
        except Exception as e:
            self.logger.error(f"获取数据信息失败: {symbol} {timeframe}, 错误: {str(e)}")
            return {
                "exists": True,
                "rows": 0,
                "start_time": None,
                "end_time": None,
                "timeframe": timeframe,
                "symbol": symbol,
                "error": str(e)
            }

def load_dataframe(file_path: str) -> pd.DataFrame:
    """
    从文件加载DataFrame
    
    Args:
        file_path: 文件路径
        
    Returns:
        加载的DataFrame
    """
    logger = logging.getLogger(__name__)
    
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return pd.DataFrame()
    
    try:
        # 根据文件扩展名判断是否使用解压缩
        if file_path.endswith('.gz'):
            with gzip.open(file_path, 'rt') as f:
                df = pd.read_csv(f, index_col=0, parse_dates=True)
        else:
            df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        
        logger.info(f"从{file_path}加载了{len(df)}行数据")
        return df
        
    except Exception as e:
        logger.error(f"加载数据失败: {file_path}, 错误: {str(e)}")
        return pd.DataFrame()


def save_dataframe(df: pd.DataFrame, file_path: str, compress: bool = False) -> bool:
    """
    将DataFrame保存到文件
    
    Args:
        df: 要保存的DataFrame
        file_path: 保存路径
        compress: 是否使用gzip压缩
        
    Returns:
        是否保存成功
    """
    logger = logging.getLogger(__name__)
    
    if df.empty:
        logger.warning(f"尝试保存空DataFrame到: {file_path}")
        return False
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 根据是否压缩选择保存方式
        if compress or file_path.endswith('.gz'):
            # 如果路径不以.gz结尾，添加.gz扩展名
            if not file_path.endswith('.gz'):
                file_path = f"{file_path}.gz"
            
            # 使用gzip压缩
            with gzip.open(file_path, 'wt') as f:
                df.to_csv(f)
            logger.info(f"数据已压缩保存到: {file_path}, {len(df)}行")
        else:
            # 普通CSV保存
            df.to_csv(file_path)
            logger.info(f"数据已保存到: {file_path}, {len(df)}行")
        
        return True
        
    except Exception as e:
        logger.error(f"保存数据失败: {file_path}, 错误: {str(e)}")
        return False 