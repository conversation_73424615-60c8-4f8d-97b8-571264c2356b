#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略适配器 - 统一不同策略接口

解决FreqTrade策略与Backtrader引擎的兼容性问题：
1. 接口适配：FreqTrade IStrategy -> Backtrader Strategy
2. 信号格式转换：FreqTrade格式 -> Backtrader格式
3. 参数标准化：统一参数访问接口
4. 多时间框架支持：实现信号质量优化
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Union
from datetime import datetime

from ..base import Strategy

logger = logging.getLogger(__name__)


class FreqTradeToBacktraderAdapter(Strategy):
    """
    FreqTrade策略到Backtrader引擎的适配器
    
    将FreqTrade的IStrategy接口适配为Backtrader引擎可以使用的Strategy接口
    """
    
    def __init__(self, freqtrade_strategy, **params):
        """
        初始化适配器
        
        Args:
            freqtrade_strategy: FreqTrade策略实例
            **params: 额外参数
        """
        # 调用基类初始化
        super().__init__(**params)
        
        self.freqtrade_strategy = freqtrade_strategy
        self.strategy_name = freqtrade_strategy.__class__.__name__
        
        # 构建统一的参数字典
        self.params = self._build_params_dict()
        
        # 信号质量优化参数
        self.enable_signal_quality_filter = params.get('enable_signal_quality_filter', True)
        self.min_signal_quality_score = params.get('min_signal_quality_score', 2)
        self.enable_multi_timeframe = params.get('enable_multi_timeframe', True)
        
        logger.info(f"策略适配器初始化完成: {self.strategy_name}")
        logger.info(f"参数数量: {len(self.params)}")
        logger.info(f"信号质量过滤: {self.enable_signal_quality_filter}")
    
    def _build_params_dict(self) -> Dict[str, Any]:
        """构建统一的参数字典"""
        params = {}
        
        # 从FreqTrade策略提取参数
        strategy = self.freqtrade_strategy
        
        # 基础参数
        if hasattr(strategy, 'timeframe'):
            params['timeframe'] = strategy.timeframe
        if hasattr(strategy, 'minimal_roi'):
            params['minimal_roi'] = strategy.minimal_roi
        if hasattr(strategy, 'stoploss'):
            params['stoploss'] = strategy.stoploss
        
        # 动量策略特有参数
        momentum_params = [
            'momentum_fast_period', 'momentum_slow_period', 'momentum_threshold',
            'trend_fast_ema', 'trend_slow_ema', 'trend_filter_ema',
            'breakout_lookback', 'breakout_threshold',
            'volume_ma_period', 'volume_threshold',
            'max_trades_per_hour', 'signal_cooldown_minutes'
        ]
        
        for param in momentum_params:
            if hasattr(strategy, param):
                params[param] = getattr(strategy, param)
        
        return params
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        数据预处理 - 实现Backtrader引擎期望的接口
        
        Args:
            data: 原始OHLCV数据
            
        Returns:
            处理后的数据
        """
        try:
            logger.info(f"开始数据预处理，数据长度: {len(data)}")
            
            # 确保数据格式正确
            if not isinstance(data.index, pd.DatetimeIndex):
                if 'timestamp' in data.columns:
                    data['timestamp'] = pd.to_datetime(data['timestamp'])
                    data.set_index('timestamp', inplace=True)
                elif 'time' in data.columns:
                    data['time'] = pd.to_datetime(data['time'])
                    data.set_index('time', inplace=True)
            
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                logger.error(f"数据缺少必要的列: {missing_columns}")
                return data
            
            # 数据清理
            data = data.dropna()
            
            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            # 移除异常值
            for col in ['open', 'high', 'low', 'close']:
                if col in data.columns:
                    # 移除价格为0或负数的行
                    data = data[data[col] > 0]
            
            # 确保OHLC逻辑正确
            data = data[
                (data['high'] >= data['low']) &
                (data['high'] >= data['open']) &
                (data['high'] >= data['close']) &
                (data['low'] <= data['open']) &
                (data['low'] <= data['close'])
            ]
            
            logger.info(f"数据预处理完成，清理后数据长度: {len(data)}")
            return data
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号 - 实现Backtrader引擎期望的接口
        
        Args:
            data: 预处理后的数据
            
        Returns:
            包含信号的DataFrame
        """
        try:
            logger.info(f"开始生成交易信号，数据长度: {len(data)}")
            
            # 使用FreqTrade策略生成指标和信号
            metadata = {'pair': 'BTC_USDT'}
            
            # 1. 计算指标
            data_with_indicators = self.freqtrade_strategy.populate_indicators(data.copy(), metadata)
            
            # 2. 生成进场信号
            data_with_entry = self.freqtrade_strategy.populate_entry_trend(data_with_indicators, metadata)
            
            # 3. 生成出场信号
            data_with_signals = self.freqtrade_strategy.populate_exit_trend(data_with_entry, metadata)
            
            # 4. 转换信号格式：FreqTrade -> Backtrader
            signals_df = self._convert_signal_format(data_with_signals)
            
            # 5. 信号质量优化
            if self.enable_signal_quality_filter:
                signals_df = self._apply_signal_quality_filter(signals_df)
            
            # 6. 多时间框架确认
            if self.enable_multi_timeframe:
                signals_df = self._apply_multi_timeframe_confirmation(signals_df)
            
            logger.info(f"信号生成完成，总信号数: {signals_df['entries'].sum()}")
            
            return signals_df
            
        except Exception as e:
            logger.error(f"信号生成失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 返回空信号DataFrame
            return self._create_empty_signals(data)
    
    def _convert_signal_format(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        转换信号格式：FreqTrade -> Backtrader
        
        FreqTrade格式：enter_long, enter_short, exit_long, exit_short
        Backtrader格式：entries, exits, entries_long, entries_short
        """
        signals = data.copy()
        
        # 初始化Backtrader格式的信号列
        signals['entries'] = False
        signals['exits'] = False
        signals['entries_long'] = False
        signals['entries_short'] = False
        
        # 转换进场信号
        if 'enter_long' in signals.columns:
            signals['entries_long'] = signals['enter_long'].fillna(0).astype(bool)
            signals['entries'] |= signals['entries_long']
        
        if 'enter_short' in signals.columns:
            signals['entries_short'] = signals['enter_short'].fillna(0).astype(bool)
            signals['entries'] |= signals['entries_short']
        
        # 转换出场信号
        if 'exit_long' in signals.columns and 'exit_short' in signals.columns:
            signals['exits'] = (
                signals['exit_long'].fillna(0).astype(bool) |
                signals['exit_short'].fillna(0).astype(bool)
            )
        
        return signals
    
    def _apply_signal_quality_filter(self, signals: pd.DataFrame) -> pd.DataFrame:
        """
        应用信号质量过滤器
        
        基于信号质量评分过滤低质量信号，在保持高频的同时提升信号质量
        """
        try:
            # 检查是否有信号质量评分
            if 'signal_quality_long' not in signals.columns or 'signal_quality_short' not in signals.columns:
                logger.warning("缺少信号质量评分，跳过质量过滤")
                return signals
            
            original_long_signals = signals['entries_long'].sum()
            original_short_signals = signals['entries_short'].sum()
            
            # 应用质量过滤
            quality_filter_long = signals['signal_quality_long'] >= self.min_signal_quality_score
            quality_filter_short = signals['signal_quality_short'] >= self.min_signal_quality_score
            
            # 更新信号
            signals['entries_long'] = signals['entries_long'] & quality_filter_long
            signals['entries_short'] = signals['entries_short'] & quality_filter_short
            signals['entries'] = signals['entries_long'] | signals['entries_short']
            
            filtered_long_signals = signals['entries_long'].sum()
            filtered_short_signals = signals['entries_short'].sum()
            
            logger.info(f"信号质量过滤完成:")
            logger.info(f"  多头信号: {original_long_signals} -> {filtered_long_signals} "
                       f"(保留率: {filtered_long_signals/original_long_signals*100:.1f}%)")
            logger.info(f"  空头信号: {original_short_signals} -> {filtered_short_signals} "
                       f"(保留率: {filtered_short_signals/original_short_signals*100:.1f}%)")
            
            return signals
            
        except Exception as e:
            logger.error(f"信号质量过滤失败: {e}")
            return signals
    
    def _apply_multi_timeframe_confirmation(self, signals: pd.DataFrame) -> pd.DataFrame:
        """
        应用多时间框架确认
        
        使用更高时间框架的趋势确认信号，减少假信号
        """
        try:
            # 计算更高时间框架的趋势（使用更长周期的EMA）
            if 'ema_fast' in signals.columns and 'ema_slow' in signals.columns:
                # 计算长期趋势（使用50和100周期EMA）
                long_term_ema_fast = signals['close'].ewm(span=50).mean()
                long_term_ema_slow = signals['close'].ewm(span=100).mean()
                
                # 长期趋势方向
                long_term_uptrend = long_term_ema_fast > long_term_ema_slow
                long_term_downtrend = long_term_ema_fast < long_term_ema_slow
                
                original_long_signals = signals['entries_long'].sum()
                original_short_signals = signals['entries_short'].sum()
                
                # 应用多时间框架过滤
                signals['entries_long'] = signals['entries_long'] & long_term_uptrend
                signals['entries_short'] = signals['entries_short'] & long_term_downtrend
                signals['entries'] = signals['entries_long'] | signals['entries_short']
                
                confirmed_long_signals = signals['entries_long'].sum()
                confirmed_short_signals = signals['entries_short'].sum()
                
                logger.info(f"多时间框架确认完成:")
                logger.info(f"  多头信号: {original_long_signals} -> {confirmed_long_signals} "
                           f"(确认率: {confirmed_long_signals/original_long_signals*100:.1f}%)")
                logger.info(f"  空头信号: {original_short_signals} -> {confirmed_short_signals} "
                           f"(确认率: {confirmed_short_signals/original_short_signals*100:.1f}%)")
            
            return signals
            
        except Exception as e:
            logger.error(f"多时间框架确认失败: {e}")
            return signals
    
    def _create_empty_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建空的信号DataFrame"""
        signals = data.copy()
        signals['entries'] = False
        signals['exits'] = False
        signals['entries_long'] = False
        signals['entries_short'] = False
        return signals
