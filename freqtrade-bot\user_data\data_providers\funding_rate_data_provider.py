#!/usr/bin/env python3
"""
资金费率和持仓量数据提供者
功能：为freqtrade策略提供资金费率和持仓量数据，支持实盘/回测模式
作者：AriQuantification
版本：1.0.0
"""

import pandas as pd
import numpy as np
import requests
from pathlib import Path
from typing import Dict, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from freqtrade.strategy import merge_informative_pair

logger = logging.getLogger(__name__)

class FundingRateDataProvider:
    """资金费率和持仓量数据提供者"""
    
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 数据缓存路径
        self.data_dir = Path(__file__).parent.parent / "data"
        self.funding_dir = self.data_dir / "funding_rates"
        self.oi_dir = self.data_dir / "open_interest"
        
        # 内存缓存
        self._funding_cache = {}
        self._oi_cache = {}
        self._last_update = {}
        
        # 缓存过期时间（秒）
        self.cache_expiry = 300  # 5分钟
        
    def get_current_funding_rate(self, pair: str) -> Dict:
        """获取当前资金费率信息（实盘模式）"""
        symbol = pair.replace('/', '')
        
        # 检查缓存
        cache_key = f"current_{symbol}"
        if self._is_cache_valid(cache_key):
            return self._funding_cache[cache_key]
        
        endpoint = "/fapi/v1/premiumIndex"
        url = f"{self.base_url}{endpoint}"
        params = {'symbol': symbol}
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # 转换数据格式
            result = {
                'funding_rate': float(data.get('lastFundingRate', 0)),
                'next_funding_time': pd.to_datetime(data.get('nextFundingTime', 0), unit='ms'),
                'mark_price': float(data.get('markPrice', 0)),
                'index_price': float(data.get('indexPrice', 0)),
                'funding_rate_8h': float(data.get('lastFundingRate', 0)) * 3,  # 日化资金费率
                'timestamp': datetime.now()
            }
            
            # 更新缓存
            self._funding_cache[cache_key] = result
            self._last_update[cache_key] = datetime.now()
            
            return result
            
        except Exception as e:
            logger.error(f"获取{symbol}当前资金费率失败: {e}")
            return {
                'funding_rate': 0.0,
                'next_funding_time': datetime.now(),
                'mark_price': 0.0,
                'index_price': 0.0,
                'funding_rate_8h': 0.0,
                'timestamp': datetime.now()
            }
    
    def load_historical_funding_data(self, pair: str) -> Optional[pd.DataFrame]:
        """加载历史资金费率数据（回测模式）"""
        symbol = pair.replace('/', '').replace(':', '')
        file_path = self.funding_dir / f"{symbol}_funding_rates.feather"
        
        try:
            if file_path.exists():
                df = pd.read_feather(file_path)
                # 确保列名一致
                if 'fundingTime' in df.columns:
                    df = df.rename(columns={'fundingTime': 'timestamp'})
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp')
                
                # 添加计算字段
                df['funding_rate_8h'] = df['fundingRate'] * 3  # 日化资金费率
                df['funding_rate_pct'] = df['fundingRate'] * 100  # 百分比形式
                
                logger.info(f"加载{symbol}历史资金费率数据成功，共{len(df)}条记录")
                return df
            else:
                logger.warning(f"未找到{symbol}的历史资金费率数据文件: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"加载{symbol}历史资金费率数据失败: {e}")
            return None
    
    def load_historical_oi_data(self, pair: str) -> Optional[pd.DataFrame]:
        """加载历史持仓量数据（回测模式）"""
        symbol = pair.replace('/', '').replace(':', '')
        file_path = self.oi_dir / f"{symbol}_open_interest.feather"
        
        try:
            if file_path.exists():
                df = pd.read_feather(file_path)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp')
                
                # 计算持仓量变化率
                df['oi_change'] = df['sumOpenInterest'].pct_change()
                df['oi_change_value'] = df['sumOpenInterestValue'].pct_change()
                
                # 计算移动平均
                df['oi_ma_24h'] = df['sumOpenInterest'].rolling(window=288).mean()  # 24小时平均（5分钟*288）
                df['oi_trend'] = (df['sumOpenInterest'] / df['oi_ma_24h'] - 1) * 100
                
                logger.info(f"加载{symbol}历史持仓量数据成功，共{len(df)}条记录")
                return df
            else:
                logger.warning(f"未找到{symbol}的历史持仓量数据文件: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"加载{symbol}历史持仓量数据失败: {e}")
            return None
    
    def get_funding_indicators(self, dataframe: pd.DataFrame, pair: str, 
                             is_backtest: bool = True, extreme_positive_threshold: float = 0.00075,
                             extreme_negative_threshold: float = -0.00075) -> pd.DataFrame:
        """为策略添加资金费率指标"""
        
        if is_backtest:
            # 回测模式：使用历史数据
            funding_df = self.load_historical_funding_data(pair)
            oi_df = self.load_historical_oi_data(pair)
            
            if funding_df is not None:
                # 将资金费率数据合并到主数据框
                funding_df_1m = self._resample_funding_to_1m(funding_df)
                dataframe = self._merge_funding_data(dataframe, funding_df_1m)
            
            if oi_df is not None:
                # 将持仓量数据合并到主数据框
                oi_df_1m = self._resample_oi_to_1m(oi_df)
                dataframe = self._merge_oi_data(dataframe, oi_df_1m)
        else:
            # 实盘模式：使用当前数据
            current_funding = self.get_current_funding_rate(pair)
            dataframe = self._add_current_funding_data(dataframe, current_funding)
        
        # 计算资金费率技术指标
        dataframe = self._calculate_funding_indicators(dataframe, extreme_positive_threshold, extreme_negative_threshold)
        
        return dataframe
    
    def _resample_funding_to_1m(self, funding_df: pd.DataFrame) -> pd.DataFrame:
        """将资金费率数据重采样到1分钟"""
        # 资金费率每8小时更新一次，需要前向填充
        result = funding_df.resample('1min').ffill()
        
        # 确保结果有UTC时区，以匹配K线数据
        if result.index.tz is None:
            result.index = result.index.tz_localize('UTC')
        
        return result
    
    def _resample_oi_to_1m(self, oi_df: pd.DataFrame) -> pd.DataFrame:
        """将持仓量数据重采样到1分钟"""
        # 持仓量数据每5分钟更新，需要插值处理
        result = oi_df.resample('1min').interpolate(method='linear')
        
        # 确保结果有UTC时区，以匹配K线数据
        if result.index.tz is None:
            result.index = result.index.tz_localize('UTC')
        
        return result
    
    def _merge_funding_data(self, dataframe: pd.DataFrame, funding_df: pd.DataFrame) -> pd.DataFrame:
        """合并资金费率数据到主数据框"""
        funding_columns = ['fundingRate', 'markPrice', 'funding_rate_8h', 'funding_rate_pct']
        
        # 确保索引类型一致，特别是时区处理
        if hasattr(dataframe, 'index') and hasattr(funding_df, 'index'):
            # 将dataframe的索引转换为datetime类型
            if not isinstance(dataframe.index, pd.DatetimeIndex):
                dataframe.index = pd.to_datetime(dataframe.index)
            
            # 确保funding_df的索引也是datetime类型
            if not isinstance(funding_df.index, pd.DatetimeIndex):
                funding_df.index = pd.to_datetime(funding_df.index)
            
            # 处理时区匹配问题
            if dataframe.index.tz is not None and funding_df.index.tz is None:
                # dataframe有时区，funding_df没有时区 - 给funding_df添加UTC时区
                funding_df.index = funding_df.index.tz_localize('UTC')
            elif dataframe.index.tz is None and funding_df.index.tz is not None:
                # dataframe没有时区，funding_df有时区 - 移除funding_df的时区
                funding_df.index = funding_df.index.tz_localize(None)
        
        for col in funding_columns:
            if col in funding_df.columns:
                # 修复列名映射逻辑
                if col == 'fundingRate':
                    new_col_name = 'funding_rate'
                elif col == 'markPrice':
                    new_col_name = 'funding_mark_price'
                elif col == 'funding_rate_8h':
                    new_col_name = 'funding_rate_8h'
                elif col == 'funding_rate_pct':
                    new_col_name = 'funding_rate_pct'
                else:
                    new_col_name = f"funding_{col.lower()}"
                    
                try:
                    dataframe[new_col_name] = funding_df[col].reindex(dataframe.index, method='ffill')
                except Exception as e:
                    logger.warning(f"合并资金费率列{col}失败: {e}")
                    dataframe[new_col_name] = 0.0
        
        return dataframe
    
    def _merge_oi_data(self, dataframe: pd.DataFrame, oi_df: pd.DataFrame) -> pd.DataFrame:
        """合并持仓量数据到主数据框"""
        oi_columns = ['sumOpenInterest', 'sumOpenInterestValue', 'oi_change', 'oi_trend']
        
        # 确保索引类型一致，特别是时区处理
        if hasattr(dataframe, 'index') and hasattr(oi_df, 'index'):
            # 将dataframe的索引转换为datetime类型
            if not isinstance(dataframe.index, pd.DatetimeIndex):
                dataframe.index = pd.to_datetime(dataframe.index)
            
            # 确保oi_df的索引也是datetime类型
            if not isinstance(oi_df.index, pd.DatetimeIndex):
                oi_df.index = pd.to_datetime(oi_df.index)
            
            # 处理时区匹配问题
            if dataframe.index.tz is not None and oi_df.index.tz is None:
                # dataframe有时区，oi_df没有时区 - 给oi_df添加UTC时区
                oi_df.index = oi_df.index.tz_localize('UTC')
            elif dataframe.index.tz is None and oi_df.index.tz is not None:
                # dataframe没有时区，oi_df有时区 - 移除oi_df的时区
                oi_df.index = oi_df.index.tz_localize(None)
        
        for col in oi_columns:
            if col in oi_df.columns:
                # 修复列名映射逻辑
                if col == 'sumOpenInterest':
                    new_col_name = 'oi_total'
                elif col == 'sumOpenInterestValue':
                    new_col_name = 'oi_total_value'
                elif col == 'oi_change':
                    new_col_name = 'oi_change'
                elif col == 'oi_trend':
                    new_col_name = 'oi_trend'
                else:
                    new_col_name = f"oi_{col.lower()}"
                    
                try:
                    dataframe[new_col_name] = oi_df[col].reindex(dataframe.index, method='ffill')
                except Exception as e:
                    logger.warning(f"合并持仓量列{col}失败: {e}")
                    dataframe[new_col_name] = 0.0
        
        return dataframe
    
    def _add_current_funding_data(self, dataframe: pd.DataFrame, current_funding: Dict) -> pd.DataFrame:
        """添加当前资金费率数据到数据框"""
        dataframe['funding_rate'] = current_funding['funding_rate']
        dataframe['funding_mark_price'] = current_funding['mark_price']
        dataframe['funding_rate_8h'] = current_funding['funding_rate_8h']
        dataframe['funding_next_time'] = current_funding['next_funding_time']
        
        return dataframe
    
    def _calculate_funding_indicators(self, dataframe: pd.DataFrame, 
                                    extreme_positive_threshold: float = 0.00075,
                                    extreme_negative_threshold: float = -0.00075) -> pd.DataFrame:
        """计算资金费率技术指标"""
        
        # 检查必要的列是否存在
        if 'funding_rate' not in dataframe.columns:
            dataframe['funding_rate'] = 0.0
        if 'oi_total' not in dataframe.columns:
            dataframe['oi_total'] = 0.0
        
        # 1. 资金费率移动平均
        dataframe['funding_rate_ma_24h'] = dataframe['funding_rate'].rolling(window=1440).mean()  # 24小时
        dataframe['funding_rate_ma_7d'] = dataframe['funding_rate'].rolling(window=10080).mean()  # 7天
        
        # 2. 资金费率标准化
        dataframe['funding_rate_zscore'] = (
            (dataframe['funding_rate'] - dataframe['funding_rate_ma_7d']) / 
            dataframe['funding_rate'].rolling(window=10080).std()
        )
        
        # 3. 极端资金费率信号（使用动态阈值）
        dataframe['funding_extreme_positive'] = dataframe['funding_rate'] > extreme_positive_threshold
        dataframe['funding_extreme_negative'] = dataframe['funding_rate'] < extreme_negative_threshold
        
        # 4. 持仓量趋势信号（按文档要求：持平或下降/上升）
        if 'oi_change' in dataframe.columns:
            dataframe['oi_declining'] = dataframe['oi_change'] <= 0.0  # 持仓量持平或下降
            dataframe['oi_expanding'] = dataframe['oi_change'] > 0.0   # 持仓量上升
        else:
            dataframe['oi_declining'] = False
            dataframe['oi_expanding'] = False
        
        # 5. 组合信号
        dataframe['funding_bullish_exhaustion'] = (
            dataframe['funding_extreme_positive'] & 
            dataframe['oi_declining']
        )
        
        dataframe['funding_bearish_capitulation'] = (
            dataframe['funding_extreme_negative'] & 
            dataframe['oi_declining']
        )
        
        # 6. 资金费率动量
        dataframe['funding_momentum'] = dataframe['funding_rate'].diff()
        dataframe['funding_acceleration'] = dataframe['funding_momentum'].diff()
        
        # 7. 奇美拉策略SPI指标计算
        dataframe = self._calculate_spi_indicators(dataframe)
        
        return dataframe
    
    def _calculate_spi_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """计算SPI（情绪压力指数）指标 - 奇美拉策略核心指标"""
        
        # SPI组成部分的Z-Score计算窗口（24小时 = 1440分钟）
        spi_window = 1440
        
        # 1. 资金费率Z-Score
        fr_mean = dataframe['funding_rate'].rolling(window=spi_window).mean()
        fr_std = dataframe['funding_rate'].rolling(window=spi_window).std()
        dataframe['fr_zscore'] = (dataframe['funding_rate'] - fr_mean) / fr_std
        dataframe['fr_zscore'] = dataframe['fr_zscore'].fillna(0)
        
        # 2. 持仓量Z-Score
        if 'oi_total' in dataframe.columns:
            oi_mean = dataframe['oi_total'].rolling(window=spi_window).mean()
            oi_std = dataframe['oi_total'].rolling(window=spi_window).std()
            dataframe['oi_zscore'] = (dataframe['oi_total'] - oi_mean) / oi_std
            dataframe['oi_zscore'] = dataframe['oi_zscore'].fillna(0)
        else:
            dataframe['oi_zscore'] = 0.0
        
        # 3. 资金费率变化率Z-Score
        dataframe['fr_roc'] = dataframe['funding_rate'].pct_change(periods=8)  # 8期变化率
        fr_roc_mean = dataframe['fr_roc'].rolling(window=spi_window).mean()
        fr_roc_std = dataframe['fr_roc'].rolling(window=spi_window).std()
        dataframe['fr_roc_zscore'] = (dataframe['fr_roc'] - fr_roc_mean) / fr_roc_std
        dataframe['fr_roc_zscore'] = dataframe['fr_roc_zscore'].fillna(0)
        
        # 4. 持仓量变化率Z-Score
        if 'oi_change' in dataframe.columns:
            oi_roc_mean = dataframe['oi_change'].rolling(window=spi_window).mean()
            oi_roc_std = dataframe['oi_change'].rolling(window=spi_window).std()
            dataframe['oi_roc_zscore'] = (dataframe['oi_change'] - oi_roc_mean) / oi_roc_std
            dataframe['oi_roc_zscore'] = dataframe['oi_roc_zscore'].fillna(0)
        else:
            dataframe['oi_roc_zscore'] = 0.0
        
        # 5. SPI加权组合（等权重开始，后续可优化）
        spi_weights = {
            'fr_zscore': 0.25,
            'oi_zscore': 0.25,
            'fr_roc_zscore': 0.25,
            'oi_roc_zscore': 0.25
        }
        
        dataframe['spi'] = (
            dataframe['fr_zscore'] * spi_weights['fr_zscore'] +
            dataframe['oi_zscore'] * spi_weights['oi_zscore'] +
            dataframe['fr_roc_zscore'] * spi_weights['fr_roc_zscore'] +
            dataframe['oi_roc_zscore'] * spi_weights['oi_roc_zscore']
        )
        
        # 6. SPI极值信号
        dataframe['spi_extreme_positive'] = dataframe['spi'] > 2.0  # 多头拥挤
        dataframe['spi_extreme_negative'] = dataframe['spi'] < -2.0  # 空头拥挤
        dataframe['spi_neutral'] = dataframe['spi'].between(-0.5, 0.5)  # 中性区域
        
        # 7. SPI趋势信号
        dataframe['spi_momentum'] = dataframe['spi'].diff()
        dataframe['spi_accelerating_positive'] = (
            (dataframe['spi'] > 0) & 
            (dataframe['spi_momentum'] > 0)
        )
        dataframe['spi_accelerating_negative'] = (
            (dataframe['spi'] < 0) & 
            (dataframe['spi_momentum'] < 0)
        )
        
        logger.debug("SPI指标计算完成")
        return dataframe
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._last_update:
            return False
        
        elapsed = (datetime.now() - self._last_update[cache_key]).total_seconds()
        return elapsed < self.cache_expiry

# 全局实例
funding_provider = FundingRateDataProvider() 