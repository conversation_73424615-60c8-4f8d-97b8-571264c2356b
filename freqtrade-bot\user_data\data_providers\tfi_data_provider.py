#!/usr/bin/env python3
"""
TFI（吃单流不平衡）数据提供者
功能：基于币安聚合交易API获取逐笔交易数据，计算TFI指标
作者：AriQuantification
版本：1.0.0
基于：奇美拉策略核心指标实现
"""

import pandas as pd
import numpy as np
import requests
import json
from pathlib import Path
from typing import Dict, Optional, List, Any
from datetime import datetime, timedelta
import logging
import time
from collections import deque
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AggTradeData:
    """聚合交易数据结构"""
    timestamp: int
    price: float
    quantity: float
    first_trade_id: int
    last_trade_id: int
    is_buyer_maker: bool  # m字段：true表示买方是maker，false表示买方是taker

class TFIDataProvider:
    """TFI（吃单流不平衡）数据提供者"""
    
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 数据缓存路径
        self.data_dir = Path(__file__).parent.parent / "data"
        self.tfi_dir = self.data_dir / "tfi_data"
        self.tfi_dir.mkdir(parents=True, exist_ok=True)
        
        # 内存缓存
        self._tfi_cache = {}
        self._raw_trades_cache = {}
        self._last_update = {}
        
        # 缓存过期时间（秒）
        self.cache_expiry = 60  # 1分钟
        
        # TFI计算参数
        self.tfi_window = 120  # Z-Score计算窗口（120个1分钟周期）
        self.rate_limit_delay = 0.1  # API限速延迟
        
        logger.info("TFI数据提供者初始化完成")
    
    def get_tfi_indicators(self, dataframe: pd.DataFrame, pair: str, 
                          is_backtest: bool = True) -> pd.DataFrame:
        """为策略添加TFI指标"""
        
        if is_backtest:
            # 回测模式：使用历史数据
            tfi_df = self.load_historical_tfi_data(pair)
            
            if tfi_df is not None:
                # 将TFI数据合并到主数据框
                tfi_df_1m = self._resample_tfi_to_1m(tfi_df)
                dataframe = self._merge_tfi_data(dataframe, tfi_df_1m)
            else:
                # 没有历史数据，添加默认值
                dataframe = self._add_default_tfi_indicators(dataframe)
        else:
            # 实盘模式：计算实时TFI
            current_tfi = self.calculate_current_tfi(pair)
            dataframe = self._add_current_tfi_data(dataframe, current_tfi)
        
        return dataframe
    
    def get_aggregated_trades(self, symbol: str, start_time: int = None, 
                            end_time: int = None, limit: int = 1000) -> List[AggTradeData]:
        """获取聚合交易数据"""
        endpoint = "/fapi/v1/aggTrades"
        url = f"{self.base_url}{endpoint}"
        
        params = {
            'symbol': symbol,
            'limit': min(limit, 1000)  # API最大限制
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
        
        try:
            time.sleep(self.rate_limit_delay)  # 限速
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            raw_data = response.json()
            
            trades = []
            for trade in raw_data:
                trades.append(AggTradeData(
                    timestamp=trade['T'],
                    price=float(trade['p']),
                    quantity=float(trade['q']),
                    first_trade_id=trade['f'],
                    last_trade_id=trade['l'],
                    is_buyer_maker=trade['m']
                ))
            
            logger.debug(f"获取{symbol}聚合交易数据成功，共{len(trades)}条")
            return trades
            
        except Exception as e:
            logger.error(f"获取{symbol}聚合交易数据失败: {e}")
            return []
    
    def calculate_tfi_for_period(self, trades: List[AggTradeData], 
                               start_time: int, end_time: int) -> Dict[str, float]:
        """计算指定时间段的TFI指标"""
        taker_buy_volume = 0.0
        taker_sell_volume = 0.0
        
        for trade in trades:
            if start_time <= trade.timestamp < end_time:
                if trade.is_buyer_maker:
                    # 买方是maker，意味着卖方是taker
                    taker_sell_volume += trade.quantity
                else:
                    # 买方是taker
                    taker_buy_volume += trade.quantity
        
        # 计算TFI原始值
        tfi_raw = taker_buy_volume - taker_sell_volume
        total_volume = taker_buy_volume + taker_sell_volume
        
        return {
            'taker_buy_volume': taker_buy_volume,
            'taker_sell_volume': taker_sell_volume,
            'tfi_raw': tfi_raw,
            'total_taker_volume': total_volume,
            'taker_buy_ratio': taker_buy_volume / total_volume if total_volume > 0 else 0.5
        }
    
    def calculate_tfi_zscore(self, tfi_values: List[float], current_tfi: float) -> float:
        """计算TFI的Z-Score"""
        if len(tfi_values) < 10:  # 最少需要10个数据点
            return 0.0
        
        tfi_array = np.array(tfi_values)
        mean = np.mean(tfi_array)
        std = np.std(tfi_array)
        
        if std == 0:
            return 0.0
        
        return (current_tfi - mean) / std
    
    def calculate_current_tfi(self, pair: str) -> Dict[str, float]:
        """计算当前TFI指标（实盘模式）"""
        symbol = pair.replace('/', '').replace(':', '')
        
        # 获取最近1分钟的交易数据
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = end_time - 60000  # 1分钟前
        
        trades = self.get_aggregated_trades(symbol, start_time, end_time)
        
        if not trades:
            return {
                'tfi_raw': 0.0,
                'tfi_z_score': 0.0,
                'taker_buy_volume': 0.0,
                'taker_sell_volume': 0.0,
                'total_taker_volume': 0.0,
                'taker_buy_ratio': 0.5
            }
        
        # 计算当前周期TFI
        current_period = self.calculate_tfi_for_period(trades, start_time, end_time)
        
        # 获取历史TFI值计算Z-Score
        historical_tfi = self._get_historical_tfi_values(symbol, self.tfi_window)
        tfi_z_score = self.calculate_tfi_zscore(historical_tfi, current_period['tfi_raw'])
        
        result = current_period.copy()
        result['tfi_z_score'] = tfi_z_score
        
        return result
    
    def _get_historical_tfi_values(self, symbol: str, window: int) -> List[float]:
        """获取历史TFI值用于Z-Score计算"""
        try:
            # 从缓存或文件加载历史数据
            cache_key = f"historical_tfi_{symbol}"
            if cache_key in self._tfi_cache:
                return self._tfi_cache[cache_key][-window:]
            
            # 临时实现：返回空列表，实际应从历史数据文件加载
            return []
            
        except Exception as e:
            logger.warning(f"获取{symbol}历史TFI值失败: {e}")
            return []
    
    def load_historical_tfi_data(self, pair: str) -> Optional[pd.DataFrame]:
        """加载历史TFI数据（回测模式）"""
        symbol = pair.replace('/', '').replace(':', '')
        file_path = self.tfi_dir / f"{symbol}_tfi_data.feather"
        
        try:
            if file_path.exists():
                df = pd.read_feather(file_path)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp')
                
                logger.info(f"加载{symbol}历史TFI数据成功，共{len(df)}条记录")
                return df
            else:
                logger.warning(f"未找到{symbol}的历史TFI数据文件: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"加载{symbol}历史TFI数据失败: {e}")
            return None
    
    def _resample_tfi_to_1m(self, tfi_df: pd.DataFrame) -> pd.DataFrame:
        """将TFI数据重采样到1分钟"""
        # TFI数据已经是1分钟周期，直接返回
        result = tfi_df.copy()
        
        # 确保结果有UTC时区，以匹配K线数据
        if result.index.tz is None:
            result.index = result.index.tz_localize('UTC')
        
        return result
    
    def _merge_tfi_data(self, dataframe: pd.DataFrame, tfi_df: pd.DataFrame) -> pd.DataFrame:
        """合并TFI数据到主数据框"""
        tfi_columns = ['tfi_raw', 'tfi_z_score', 'taker_buy_volume', 'taker_sell_volume', 
                      'total_taker_volume', 'taker_buy_ratio']
        
        # 确保索引类型一致
        if hasattr(dataframe, 'index') and hasattr(tfi_df, 'index'):
            if not isinstance(dataframe.index, pd.DatetimeIndex):
                dataframe.index = pd.to_datetime(dataframe.index)
            
            if not isinstance(tfi_df.index, pd.DatetimeIndex):
                tfi_df.index = pd.to_datetime(tfi_df.index)
            
            # 处理时区匹配
            if dataframe.index.tz is not None and tfi_df.index.tz is None:
                tfi_df.index = tfi_df.index.tz_localize('UTC')
            elif dataframe.index.tz is None and tfi_df.index.tz is not None:
                tfi_df.index = tfi_df.index.tz_localize(None)
        
        for col in tfi_columns:
            if col in tfi_df.columns:
                try:
                    dataframe[col] = tfi_df[col].reindex(dataframe.index, method='ffill')
                except Exception as e:
                    logger.warning(f"合并TFI列{col}失败: {e}")
                    dataframe[col] = 0.0
        
        return dataframe
    
    def _add_current_tfi_data(self, dataframe: pd.DataFrame, current_tfi: Dict) -> pd.DataFrame:
        """添加当前TFI数据到数据框"""
        for key, value in current_tfi.items():
            dataframe[key] = value
        
        return dataframe
    
    def _add_default_tfi_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """添加默认TFI指标（当没有数据时）"""
        dataframe['tfi_raw'] = 0.0
        dataframe['tfi_z_score'] = 0.0
        dataframe['taker_buy_volume'] = 0.0
        dataframe['taker_sell_volume'] = 0.0
        dataframe['total_taker_volume'] = 0.0
        dataframe['taker_buy_ratio'] = 0.5
        
        return dataframe
    
    def save_tfi_data(self, symbol: str, tfi_data: pd.DataFrame):
        """保存TFI数据到文件"""
        file_path = self.tfi_dir / f"{symbol}_tfi_data.feather"
        
        try:
            # 重置索引以确保时间戳列正确保存
            tfi_data_to_save = tfi_data.reset_index()
            if 'timestamp' not in tfi_data_to_save.columns and 'index' in tfi_data_to_save.columns:
                tfi_data_to_save = tfi_data_to_save.rename(columns={'index': 'timestamp'})
            
            tfi_data_to_save.to_feather(file_path)
            logger.info(f"保存{symbol}的TFI数据成功: {file_path}")
            
        except Exception as e:
            logger.error(f"保存{symbol}的TFI数据失败: {e}")

# 全局实例
tfi_provider = TFIDataProvider() 