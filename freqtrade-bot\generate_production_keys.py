#!/usr/bin/env python3
"""
安全密钥生成脚本 - FreqTrade ADL策略实盘部署

功能说明：
- 生成加密安全的JWT密钥（用于API认证）
- 生成WebSocket令牌（用于实时数据连接）
- 生成强密码（用于API服务器登录）
- 提供环境变量设置指南

使用方法：
python generate_production_keys.py

安全注意事项：
- 生成的密钥应妥善保管，不要提交到代码仓库
- 建议定期更换密钥以提高安全性
- 在生产环境中使用环境变量存储敏感信息
"""

import secrets
import string
import os
from datetime import datetime

def generate_jwt_secret():
    """生成JWT密钥 - 用于API服务器认证"""
    return secrets.token_hex(32)

def generate_ws_token():
    """生成WebSocket令牌 - 用于实时数据连接"""
    return secrets.token_urlsafe(32)

def generate_strong_password(length=16):
    """生成强密码 - 用于API服务器登录"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for _ in range(length))
    return password

def generate_api_username():
    """生成API用户名"""
    return f"ADL_Trader_{secrets.token_hex(4)}"

def display_keys():
    """显示生成的密钥和配置指南"""
    print("="*60)
    print("🔐 FreqTrade ADL策略实盘部署 - 安全密钥生成")
    print("="*60)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 生成密钥
    jwt_secret = generate_jwt_secret()
    ws_token = generate_ws_token()
    api_password = generate_strong_password()
    api_username = generate_api_username()
    
    print("📋 生成的安全密钥:")
    print("-" * 40)
    print(f"JWT密钥:      {jwt_secret}")
    print(f"WebSocket令牌: {ws_token}")
    print(f"API用户名:     {api_username}")
    print(f"API密码:      {api_password}")
    print()
    
    print("🔧 环境变量配置 (Windows PowerShell):")
    print("-" * 40)
    print("# 币安API密钥 (需要替换为真实值)")
    print('$env:FREQTRADE_BINANCE_KEY="your_binance_api_key_here"')
    print('$env:FREQTRADE_BINANCE_SECRET="your_binance_api_secret_here"')
    print()
    print("# FreqTrade服务密钥")
    print(f'$env:FREQTRADE_JWT_SECRET="{jwt_secret}"')
    print(f'$env:FREQTRADE_WS_TOKEN="{ws_token}"')
    print(f'$env:FREQTRADE_API_USERNAME="{api_username}"')
    print(f'$env:FREQTRADE_API_PASSWORD="{api_password}"')
    print()
    
    print("🔧 环境变量配置 (Linux/Mac):")
    print("-" * 40)
    print("# 币安API密钥 (需要替换为真实值)")
    print('export FREQTRADE_BINANCE_KEY="your_binance_api_key_here"')
    print('export FREQTRADE_BINANCE_SECRET="your_binance_api_secret_here"')
    print()
    print("# FreqTrade服务密钥")
    print(f'export FREQTRADE_JWT_SECRET="{jwt_secret}"')
    print(f'export FREQTRADE_WS_TOKEN="{ws_token}"')
    print(f'export FREQTRADE_API_USERNAME="{api_username}"')
    print(f'export FREQTRADE_API_PASSWORD="{api_password}"')
    print()
    
    print("📝 config_adl_production.json配置片段:")
    print("-" * 40)
    print('"exchange": {')
    print('  "name": "binance",')
    print('  "key": "${FREQTRADE_BINANCE_KEY}",')
    print('  "secret": "${FREQTRADE_BINANCE_SECRET}",')
    print('  // ... 其他配置')
    print('},')
    print('"api_server": {')
    print('  "enabled": true,')
    print('  "listen_ip_address": "127.0.0.1",')
    print('  "listen_port": 8081,')
    print(f'  "jwt_secret_key": "{jwt_secret}",')
    print(f'  "ws_token": "{ws_token}",')
    print(f'  "username": "{api_username}",')
    print(f'  "password": "{api_password}"')
    print('}')
    print()
    
    print("⚠️  安全提醒:")
    print("-" * 40)
    print("1. 将环境变量添加到系统或.env文件中")
    print("2. 确保API密钥具有期货交易权限但禁用提现权限")
    print("3. 定期检查和更换密钥以提高安全性")
    print("4. 不要将密钥提交到代码仓库中")
    print("5. 建议使用IP白名单限制API访问")
    print()
    
    # 将密钥写入文件供配置使用
    with open('production_keys.txt', 'w', encoding='utf-8') as f:
        f.write(f"# FreqTrade ADL Production Keys - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"JWT_SECRET={jwt_secret}\n")
        f.write(f"WS_TOKEN={ws_token}\n")
        f.write(f"API_USERNAME={api_username}\n")
        f.write(f"API_PASSWORD={api_password}\n")
    
    print("💾 密钥已保存到 production_keys.txt 文件")
    print("   (记得在配置完成后删除此文件)")
    print("="*60)

if __name__ == "__main__":
    display_keys() 